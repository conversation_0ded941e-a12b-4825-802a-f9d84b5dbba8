"use client";

import {
  Activity,
  AlertTriangle,
  BarChart3,
  Clock,
  Database,
  Globe,
  RefreshCw,
  TrendingUp,
  Users,
  Zap,
} from "lucide-react";
import { useEffect, useState } from "react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  enhancedPerformanceMonitor,
  type PerformanceAlert,
} from "@/lib/enhanced-performance-monitor";
import { performanceMonitor } from "@/lib/performance-monitor";

interface PerformanceStats {
  webVitals: Array<{
    name: string;
    count: number;
    average: number;
    ratings: {
      good: number;
      needsImprovement: number;
      poor: number;
    };
  }>;
  analytics: {
    eventsByType: Record<string, number>;
    uniquePages: number;
    errorRate: number;
    topPages: Array<{ page: string; count: number }>;
  };
  serverMetrics: {
    averageDuration: number;
    slowQueries: any[];
    queryCount: number;
    topSlowQueries: Array<{
      query: string;
      avgDuration: number;
      count: number;
    }>;
  };
  alerts: PerformanceAlert[];
}

export default function PerformanceDashboard() {
  const [stats, setStats] = useState<PerformanceStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [timeWindow, setTimeWindow] = useState(60); // minutes

  const fetchStats = async () => {
    try {
      setLoading(true);
      setError(null);

      const report = enhancedPerformanceMonitor.getPerformanceReport(
        timeWindow * 60 * 1000
      );
      setStats({
        webVitals: report.webVitals,
        analytics: report.analytics,
        serverMetrics: report.serverMetrics,
        alerts: report.alerts,
      });
    } catch (error) {
      console.error("Failed to fetch performance stats:", error);

      // Set user-friendly error message
      const errorMessage =
        error instanceof Error
          ? error.message
          : "An unexpected error occurred while fetching performance data";

      setError(errorMessage);
      setStats(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, [timeWindow]);

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(fetchStats, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, [autoRefresh, timeWindow]);

  const getWebVitalRating = (name: string, value: number) => {
    const thresholds = {
      CLS: { good: 0.1, poor: 0.25 },
      FID: { good: 100, poor: 300 },
      FCP: { good: 1800, poor: 3000 },
      LCP: { good: 2500, poor: 4000 },
      TTFB: { good: 800, poor: 1800 },
      INP: { good: 200, poor: 500 },
    };

    const threshold = thresholds[name as keyof typeof thresholds];
    if (!threshold) return "good";

    if (value <= threshold.good) return "good";
    if (value <= threshold.poor) return "needs-improvement";
    return "poor";
  };

  const getRatingColor = (rating: string) => {
    switch (rating) {
      case "good":
        return "text-green-600 bg-green-100";
      case "needs-improvement":
        return "text-yellow-600 bg-yellow-100";
      case "poor":
        return "text-red-600 bg-red-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "critical":
        return "text-red-600 bg-red-100";
      case "high":
        return "text-orange-600 bg-orange-100";
      case "medium":
        return "text-yellow-600 bg-yellow-100";
      case "low":
        return "text-blue-600 bg-blue-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center gap-2">
          <RefreshCw className="w-4 h-4 animate-spin" />
          <span>Loading performance data...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-app-headline">
              Performance Dashboard
            </h1>
            <p className="text-app-sub-headline">
              Real-time performance monitoring and analytics
            </p>
          </div>

          <Button variant="outline" size="sm" onClick={fetchStats}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry
          </Button>
        </div>

        <Alert className="border-l-4 border-l-red-500">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Failed to Load Performance Data</AlertTitle>
          <AlertDescription>
            {error}
            <br />
            <span className="text-sm text-muted-foreground">
              Please check your connection and try again.
            </span>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-app-headline">
            Performance Dashboard
          </h1>
          <p className="text-app-sub-headline">
            Real-time performance monitoring and analytics
          </p>
        </div>

        <div className="flex items-center gap-4">
          <select
            value={timeWindow}
            onChange={(e) => setTimeWindow(Number(e.target.value))}
            className="px-3 py-2 border border-app-stroke rounded-md bg-app-background text-app-headline"
          >
            <option value={15}>Last 15 minutes</option>
            <option value={60}>Last hour</option>
            <option value={240}>Last 4 hours</option>
            <option value={1440}>Last 24 hours</option>
          </select>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={autoRefresh ? "bg-green-50 border-green-200" : ""}
          >
            <Activity className="w-4 h-4 mr-2" />
            {autoRefresh ? "Auto-refresh ON" : "Auto-refresh OFF"}
          </Button>

          <Button variant="outline" size="sm" onClick={fetchStats}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Alerts */}
      {stats?.alerts && stats.alerts.length > 0 && (
        <div className="space-y-2">
          <h2 className="text-lg font-semibold text-app-headline flex items-center gap-2">
            <AlertTriangle className="w-5 h-5" />
            Active Alerts ({stats.alerts.length})
          </h2>
          {stats.alerts.slice(0, 5).map((alert) => (
            <Alert key={alert.id} className="border-l-4 border-l-red-500">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle className="flex items-center gap-2">
                {alert.title}
                <Badge className={getSeverityColor(alert.severity)}>
                  {alert.severity}
                </Badge>
              </AlertTitle>
              <AlertDescription>{alert.message}</AlertDescription>
            </Alert>
          ))}
        </div>
      )}

      {/* Main Dashboard */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="web-vitals">Web Vitals</TabsTrigger>
          <TabsTrigger value="server">Server Performance</TabsTrigger>
          <TabsTrigger value="analytics">User Analytics</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Avg Page Load
                </CardTitle>
                <Globe className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {stats?.webVitals
                    .find((v) => v.name === "LCP")
                    ?.average.toFixed(0) || "N/A"}
                  ms
                </div>
                <p className="text-xs text-muted-foreground">
                  Largest Contentful Paint
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Avg Query Time
                </CardTitle>
                <Database className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {stats?.serverMetrics.averageDuration.toFixed(0) || "N/A"}ms
                </div>
                <p className="text-xs text-muted-foreground">
                  Database queries
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Error Rate
                </CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {((stats?.analytics.errorRate || 0) * 100).toFixed(1)}%
                </div>
                <p className="text-xs text-muted-foreground">
                  Client-side errors
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Active Users
                </CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {stats?.analytics.uniquePages || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Unique page views
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Web Vitals Tab */}
        <TabsContent value="web-vitals" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {stats?.webVitals.map((vital) => (
              <Card key={vital.name}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    {vital.name}
                    <Badge
                      className={getRatingColor(
                        getWebVitalRating(vital.name, vital.average)
                      )}
                    >
                      {getWebVitalRating(vital.name, vital.average)}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <div className="text-2xl font-bold">
                      {vital.average.toFixed(0)}ms
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {vital.count} measurements
                    </div>
                  </div>

                  {vital.count > 0 ? (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Good</span>
                        <span>{vital.ratings.good}</span>
                      </div>
                      <Progress
                        value={(vital.ratings.good / vital.count) * 100}
                        className="h-2 bg-green-100"
                      />

                      <div className="flex justify-between text-sm">
                        <span>Needs Improvement</span>
                        <span>{vital.ratings.needsImprovement}</span>
                      </div>
                      <Progress
                        value={
                          (vital.ratings.needsImprovement / vital.count) * 100
                        }
                        className="h-2 bg-yellow-100"
                      />

                      <div className="flex justify-between text-sm">
                        <span>Poor</span>
                        <span>{vital.ratings.poor}</span>
                      </div>
                      <Progress
                        value={(vital.ratings.poor / vital.count) * 100}
                        className="h-2 bg-red-100"
                      />
                    </div>
                  ) : (
                    <div className="text-center py-4 text-muted-foreground">
                      <p className="text-sm">No measurements available</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Server Performance Tab */}
        <TabsContent value="server" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="w-5 h-5" />
                  Database Performance
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-2xl font-bold">
                      {stats?.serverMetrics.queryCount || 0}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Total Queries
                    </div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold">
                      {stats?.serverMetrics.slowQueries.length || 0}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Slow Queries
                    </div>
                  </div>
                </div>

                <div>
                  <div className="text-lg font-semibold mb-2">
                    Slowest Queries
                  </div>
                  <div className="space-y-2">
                    {stats?.serverMetrics.topSlowQueries
                      .slice(0, 5)
                      .map((query, index) => (
                        <div
                          key={index}
                          className="flex justify-between items-center p-2 bg-app-card rounded"
                        >
                          <span className="text-sm font-mono truncate">
                            {query.query}
                          </span>
                          <div className="text-right">
                            <div className="text-sm font-semibold">
                              {query.avgDuration.toFixed(0)}ms
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {query.count} calls
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5" />
                  Performance Trends
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <BarChart3 className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>Performance trend charts would be displayed here</p>
                  <p className="text-sm">
                    Integration with charting library needed
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Event Types</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {Object.entries(stats?.analytics.eventsByType || {}).map(
                    ([type, count]) => (
                      <div
                        key={type}
                        className="flex justify-between items-center"
                      >
                        <span className="capitalize">
                          {type.replace("_", " ")}
                        </span>
                        <Badge variant="outline">{count}</Badge>
                      </div>
                    )
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Top Pages</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {stats?.analytics.topPages.slice(0, 10).map((page, index) => (
                    <div
                      key={index}
                      className="flex justify-between items-center"
                    >
                      <span className="font-mono text-sm truncate">
                        {page.page}
                      </span>
                      <Badge variant="outline">{page.count}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

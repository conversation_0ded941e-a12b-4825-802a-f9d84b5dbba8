"use client";

import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import ShardLoadingAnimation from "./shard-loading-animation";

export interface LoadingProps {
  size?: "sm" | "md" | "lg" | "xl";
  variant?: "spinner" | "shard" | "dots" | "pulse";
  text?: string;
  className?: string;
  fullScreen?: boolean;
}

const sizeClasses = {
  sm: "w-4 h-4",
  md: "w-6 h-6",
  lg: "w-8 h-8",
  xl: "w-12 h-12",
};

const sizePx = {
  sm: 16,
  md: 24,
  lg: 32,
  xl: 48,
};

export function Loading({
  size = "md",
  variant = "spinner",
  text,
  className,
  fullScreen = false,
}: LoadingProps) {
  const containerClasses = cn(
    "flex items-center justify-center",
    fullScreen && "min-h-screen",
    className
  );

  const content = (
    <div className="flex flex-col items-center gap-3">
      {variant === "spinner" && (
        <Loader2
          className={cn("animate-spin text-app-main", sizeClasses[size])}
        />
      )}

      {variant === "shard" && <ShardLoadingAnimation size={sizePx[size]} />}

      {variant === "dots" && (
        <div className="flex space-x-1">
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              className={cn(
                "rounded-full bg-app-main animate-pulse",
                size === "sm" && "w-1 h-1",
                size === "md" && "w-2 h-2",
                size === "lg" && "w-3 h-3",
                size === "xl" && "w-4 h-4"
              )}
              style={{
                animationDelay: `${i * 0.2}s`,
                animationDuration: "1s",
              }}
            />
          ))}
        </div>
      )}

      {variant === "pulse" && (
        <div
          className={cn(
            "rounded-full bg-app-main animate-pulse",
            sizeClasses[size]
          )}
        />
      )}

      {text && (
        <p className="text-sm text-app-headline/60 animate-pulse">{text}</p>
      )}
    </div>
  );

  return <div className={containerClasses}>{content}</div>;
}

// Specialized loading components
export function PageLoading({ text = "Loading..." }: { text?: string }) {
  return (
    <Loading
      variant="shard"
      size="xl"
      text={text}
      fullScreen
      className="bg-app-background"
    />
  );
}

export function InlineLoading({
  text,
  size = "sm",
}: {
  text?: string;
  size?: "sm" | "md";
}) {
  return <Loading variant="spinner" size={size} text={text} className="py-4" />;
}

export function ButtonLoading({ size = "sm" }: { size?: "sm" | "md" }) {
  return <Loader2 className={cn("animate-spin", sizeClasses[size])} />;
}

// Loading overlay for existing content
export function LoadingOverlay({
  isLoading,
  children,
  text = "Loading...",
  className,
}: {
  isLoading: boolean;
  children: React.ReactNode;
  text?: string;
  className?: string;
}) {
  return (
    <div className={cn("relative", className)}>
      {children}
      {isLoading && (
        <div className="absolute inset-0 bg-app-background/80 backdrop-blur-sm flex items-center justify-center z-10">
          <Loading variant="shard" size="lg" text={text} />
        </div>
      )}
    </div>
  );
}

// Skeleton loading components
export function SkeletonText({
  lines = 1,
  className,
}: {
  lines?: number;
  className?: string;
}) {
  return (
    <div className={cn("space-y-2", className)}>
      {Array.from({ length: lines }).map((_, i) => (
        <div
          key={i}
          className={cn(
            "h-4 bg-app-stroke animate-pulse rounded",
            i === lines - 1 && lines > 1 && "w-3/4" // Last line shorter
          )}
        />
      ))}
    </div>
  );
}

export function SkeletonCard({ className }: { className?: string }) {
  return (
    <div className={cn("p-4 border border-app-stroke rounded-lg", className)}>
      <div className="space-y-3">
        <div className="h-4 bg-app-stroke animate-pulse rounded w-1/4" />
        <SkeletonText lines={3} />
        <div className="flex gap-2">
          <div className="h-8 w-16 bg-app-stroke animate-pulse rounded" />
          <div className="h-8 w-16 bg-app-stroke animate-pulse rounded" />
        </div>
      </div>
    </div>
  );
}

export function SkeletonAvatar({
  size = "md",
  className,
}: {
  size?: "sm" | "md" | "lg";
  className?: string;
}) {
  const avatarSizes = {
    sm: "w-8 h-8",
    md: "w-12 h-12",
    lg: "w-16 h-16",
  };

  return (
    <div
      className={cn(
        "rounded-full bg-app-stroke animate-pulse",
        avatarSizes[size],
        className
      )}
    />
  );
}

// Loading states for specific UI patterns
export function LoadingButton({
  children,
  isLoading,
  loadingText = "Loading...",
  ...props
}: {
  children: React.ReactNode;
  isLoading: boolean;
  loadingText?: string;
} & React.ButtonHTMLAttributes<HTMLButtonElement>) {
  return (
    <button
      {...props}
      disabled={isLoading || props.disabled}
      aria-label={isLoading ? loadingText : undefined}
    >
      {isLoading ? (
        <div className="flex items-center gap-2">
          <ButtonLoading />
          <span>{loadingText}</span>
        </div>
      ) : (
        children
      )}
    </button>
  );
}

export function LoadingTable({
  rows = 5,
  columns = 4,
  className,
}: {
  rows?: number;
  columns?: number;
  className?: string;
}) {
  return (
    <div className={cn("space-y-2", className)}>
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex gap-4">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <div
              key={colIndex}
              className="h-8 bg-app-stroke animate-pulse rounded flex-1"
            />
          ))}
        </div>
      ))}
    </div>
  );
}

"use client";

import { format } from "date-fns";
import { <PERSON><PERSON>, Loader2, Refresh<PERSON><PERSON> } from "lucide-react";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { trpc } from "@/utils/trpc";

interface AccountTweetsFilterProps {
  accountId: string;
}

export function AccountTweetsFilter({ accountId }: AccountTweetsFilterProps) {
  const [filters, setFilters] = useState({
    mentions: true,
    userTweets: true,
    replies: true,
    retweets: true,
  });
  const [sortBy, setSortBy] = useState<
    "createdAt" | "bullishScore" | "importanceScore"
  >("createdAt");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  const { data, isLoading, refetch, fetchNextPage, hasNextPage } =
    trpc.mentions.getAccountTweets.useInfiniteQuery(
      {
        accountId,
        limit: 20,
        tweetTypes: filters,
        sortBy,
        sortOrder,
      },
      {
        getNextPageParam: (lastPage: any) => lastPage.nextCursor,
      }
    );

  const allTweets = data?.pages.flatMap((page: any) => page.tweets) || [];

  const fetchUserTweetsMutation = trpc.twitter.fetchUserLastTweets.useMutation({
    onSuccess: () => {
      refetch();
    },
  });

  const handleFetchUserTweets = async () => {
    if (!data?.pages[0]?.filters.account.handle) return;

    // Fetch fresh tweets from Twitter API
    await fetchUserTweetsMutation.mutateAsync({
      username: data.pages[0].filters.account.handle,
      includeReplies: filters.replies,
      includeRetweets: filters.retweets,
      limit: 50,
    });
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Tweet Filters</CardTitle>
          <CardDescription>
            Filter tweets for @{data?.pages[0]?.filters.account.handle}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Tweet Type Filters */}
          <div className="space-y-2">
            <Label>Tweet Types</Label>
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="mentions"
                  checked={filters.mentions}
                  onCheckedChange={(checked) =>
                    setFilters((prev) => ({ ...prev, mentions: !!checked }))
                  }
                />
                <Label htmlFor="mentions" className="cursor-pointer">
                  Mentions of this account
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="userTweets"
                  checked={filters.userTweets}
                  onCheckedChange={(checked) =>
                    setFilters((prev) => ({ ...prev, userTweets: !!checked }))
                  }
                />
                <Label htmlFor="userTweets" className="cursor-pointer">
                  Tweets by this account
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="replies"
                  checked={filters.replies}
                  disabled={!filters.userTweets}
                  onCheckedChange={(checked) =>
                    setFilters((prev) => ({ ...prev, replies: !!checked }))
                  }
                />
                <Label
                  htmlFor="replies"
                  className="cursor-pointer text-sm text-muted-foreground"
                >
                  Include replies (user tweets only)
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="retweets"
                  checked={filters.retweets}
                  disabled={!filters.userTweets}
                  onCheckedChange={(checked) =>
                    setFilters((prev) => ({ ...prev, retweets: !!checked }))
                  }
                />
                <Label
                  htmlFor="retweets"
                  className="cursor-pointer text-sm text-muted-foreground"
                >
                  Include retweets (user tweets only)
                </Label>
              </div>
            </div>
          </div>

          {/* Sorting Options */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Sort By</Label>
              <Select
                value={sortBy}
                onValueChange={(
                  value: "createdAt" | "bullishScore" | "importanceScore"
                ) => setSortBy(value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="createdAt">Date</SelectItem>
                  <SelectItem value="bullishScore">Bullish Score</SelectItem>
                  <SelectItem value="importanceScore">Importance</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Order</Label>
              <Select
                value={sortOrder}
                onValueChange={(value: "asc" | "desc") => setSortOrder(value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="desc">Newest First</SelectItem>
                  <SelectItem value="asc">Oldest First</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              onClick={() => refetch()}
              disabled={isLoading}
              variant="outline"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Filter className="h-4 w-4" />
              )}
              Apply Filters
            </Button>

            <Button
              onClick={handleFetchUserTweets}
              disabled={isLoading || !filters.userTweets}
              variant="secondary"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Fetch Latest Tweets
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      <Card>
        <CardHeader>
          <CardTitle>
            {allTweets.length} Tweet{allTweets.length !== 1 ? "s" : ""} Found
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <div className="space-y-4">
              {allTweets.map((tweet: any) => (
                <div key={tweet.id} className="border-b pb-4 last:border-0">
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex items-center gap-2">
                      <span
                        className={`text-xs px-2 py-1 rounded ${
                          tweet.isUserTweet
                            ? "bg-blue-100 text-blue-700"
                            : "bg-green-100 text-green-700"
                        }`}
                      >
                        {tweet.isUserTweet ? "User Tweet" : "Mention"}
                      </span>
                      {tweet.isReply && (
                        <span className="text-xs px-2 py-1 rounded bg-gray-100 text-gray-700">
                          Reply
                        </span>
                      )}
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {format(new Date(tweet.createdAt), "MMM d, HH:mm")}
                    </span>
                  </div>

                  <div className="flex items-center gap-2 mb-2">
                    <span className="font-medium">@{tweet.authorHandle}</span>
                    <span className="text-muted-foreground">·</span>
                    <span className="text-sm text-muted-foreground">
                      {tweet.authorName}
                    </span>
                  </div>

                  <p className="text-sm mb-2">{tweet.content}</p>

                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    {tweet.bullishScore && (
                      <span>Bullish: {tweet.bullishScore}/100</span>
                    )}
                    {tweet.importanceScore && (
                      <span>Importance: {tweet.importanceScore}/100</span>
                    )}
                    <a
                      href={tweet.tweetUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:underline"
                    >
                      View on X
                    </a>
                  </div>
                </div>
              ))}

              {hasNextPage && (
                <Button
                  onClick={() => fetchNextPage()}
                  variant="outline"
                  className="w-full"
                >
                  Load More
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Crypto Service
 *
 * Handles all cryptocurrency-related business logic including
 * sector analysis, project research, and market intelligence.
 */

import type { FeatureType, PrismaClient } from "../../prisma/generated";
import { cookieClient } from "../lib/cookie-client";
import { cryptoCache } from "../lib/crypto-cache";
import { checkRateLimit, recordUsage } from "../lib/db-utils";
import { BaseService } from "./base.service";
import type {
  AccountFeedOptions,
  CacheClearOptions,
  ICryptoService,
  ILogger,
  MarketIntelligenceOptions,
  ProjectMetricsOptions,
  ProjectSearchOptions,
  SectorInfluencersOptions,
  ServiceResult,
  SmartFollowersOptions,
  TrendingProjectsOptions,
} from "./interfaces";

export class CryptoService extends BaseService implements ICryptoService {
  constructor(prisma: PrismaClient, logger: ILogger) {
    super(prisma, logger);
  }

  /**
   * Test Cookie.fun API connection
   */
  async testConnection(userId: string): Promise<ServiceResult> {
    try {
      this.logger.info(`Testing Cookie.fun API connection for user: ${userId}`);

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      // Check rate limits
      const rateLimit = await checkRateLimit(
        userId,
        "COOKIE_API_CALLS" as FeatureType,
        1
      );
      if (!rateLimit.allowed) {
        return this.failure(
          `Cookie.fun API calls limit exceeded. ${rateLimit.remaining} remaining this month.`
        );
      }

      const result = await cookieClient.testConnection();

      // Record usage
      await recordUsage(userId, "COOKIE_API_CALLS" as FeatureType, 1, {
        endpoint: "testConnection",
        timestamp: new Date().toISOString(),
      });

      return this.success({
        success: result.success,
        message: result.message,
        availableEndpoints: result.availableEndpoints,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error("Test connection error:", error as Error);
      return this.failure(
        error instanceof Error ? error.message : "Unknown error",
        {
          timestamp: new Date().toISOString(),
        }
      );
    }
  }

  /**
   * Get available crypto sectors (with caching)
   */
  async getSectors(userId: string): Promise<ServiceResult> {
    try {
      this.logger.info(`Getting crypto sectors for user: ${userId}`);

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      // Try cache first
      const cachedSectors = await cryptoCache.getCachedSectors();
      if (cachedSectors) {
        this.logger.info(
          `Sectors served from ${cachedSectors.fromMemory ? "memory" : "database"} cache`
        );
        return this.success({
          data: cachedSectors.data,
          cached: true,
          cacheSource: cachedSectors.fromMemory ? "memory" : "database",
        });
      }

      // Check rate limits for API call
      const rateLimit = await checkRateLimit(
        userId,
        "COOKIE_API_CALLS" as FeatureType,
        1
      );
      if (!rateLimit.allowed) {
        return this.failure(
          `Cookie.fun API calls limit exceeded. ${rateLimit.remaining} remaining this month.`
        );
      }

      // Fetch from API
      const response = await cookieClient.getSectors();
      await cryptoCache.cacheSectors(response.data);
      await recordUsage(userId, "COOKIE_API_CALLS" as FeatureType, 1);

      return this.success({
        data: response.data,
        cached: false,
        cacheSource: "api",
      });
    } catch (error) {
      return this.handleError(error, "getSectors");
    }
  }

  /**
   * Get smart followers for a Twitter account
   */
  async getSmartFollowers(
    userId: string,
    options: SmartFollowersOptions
  ): Promise<ServiceResult> {
    try {
      this.logger.info(
        `Getting smart followers for: ${options.username || options.userId}`
      );

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      if (!options.username && !options.userId) {
        return this.failure("Either username or userId must be provided");
      }

      // Check rate limits
      const rateLimit = await checkRateLimit(
        userId,
        "COOKIE_API_CALLS" as FeatureType,
        1
      );
      if (!rateLimit.allowed) {
        return this.failure(
          `Cookie.fun API calls limit exceeded. ${rateLimit.remaining} remaining this month.`
        );
      }

      const response = await cookieClient.getSmartFollowers({
        username: options.username,
        userId: options.userId,
        limit: options.limit || 20,
      });

      await recordUsage(userId, "COOKIE_API_CALLS" as FeatureType, 1);

      return this.success({
        data: response.data,
        pagination: response.pagination,
      });
    } catch (error) {
      return this.handleError(error, "getSmartFollowers");
    }
  }

  /**
   * Get account feed with filtering options
   */
  async getAccountFeed(
    userId: string,
    options: AccountFeedOptions
  ): Promise<ServiceResult> {
    try {
      this.logger.info(
        `Getting account feed for: ${options.username || options.userId}`
      );

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      if (!options.username && !options.userId) {
        return this.failure("Either username or userId must be provided");
      }

      // Check rate limits
      const rateLimit = await checkRateLimit(
        userId,
        "COOKIE_API_CALLS" as FeatureType,
        1
      );
      if (!rateLimit.allowed) {
        return this.failure(
          `Cookie.fun API calls limit exceeded. ${rateLimit.remaining} remaining this month.`
        );
      }

      const response = await cookieClient.getAccountFeed({
        username: options.username,
        userId: options.userId,
        startDate: options.startDate,
        endDate: options.endDate,
        type: options.type,
        hasMedia: options.hasMedia,
        sortBy: options.sortBy,
        sortOrder: options.sortOrder,
        limit: options.limit || 10,
      });

      await recordUsage(userId, "COOKIE_API_CALLS" as FeatureType, 1);

      return this.success({
        data: response.data,
        pagination: response.pagination,
      });
    } catch (error) {
      return this.handleError(error, "getAccountFeed");
    }
  }

  /**
   * Search for crypto projects
   */
  async searchProjects(
    userId: string,
    options: ProjectSearchOptions
  ): Promise<ServiceResult> {
    try {
      this.logger.info(
        `Searching projects with query: ${options.searchQuery || options.projectSlug}`
      );

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      if (!options.searchQuery && !options.projectSlug) {
        return this.failure(
          "At least one of searchQuery or projectSlug must be provided"
        );
      }

      // Check rate limits
      const rateLimit = await checkRateLimit(
        userId,
        "COOKIE_API_CALLS" as FeatureType,
        1
      );
      if (!rateLimit.allowed) {
        return this.failure(
          `Cookie.fun API calls limit exceeded. ${rateLimit.remaining} remaining this month.`
        );
      }

      const response = await cookieClient.searchProjects({
        query: options.searchQuery || options.projectSlug,
        limit: options.limit || 10,
      });

      await recordUsage(userId, "COOKIE_API_CALLS" as FeatureType, 1);

      return this.success({
        data: response.data,
        pagination: response.pagination,
        timeseries: response.timeseries,
      });
    } catch (error) {
      return this.handleError(error, "searchProjects");
    }
  }

  /**
   * Get trending projects with caching
   */
  async getTrendingProjects(
    userId: string,
    options: TrendingProjectsOptions
  ): Promise<ServiceResult> {
    try {
      this.logger.info(
        `Getting trending projects for sector: ${options.sectorSlug || "all"}`
      );

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      // Try cache first
      const cachedTrending = await cryptoCache.getCachedTrending(
        options.sectorSlug,
        options.timeframe || "_7Days"
      );
      if (cachedTrending) {
        this.logger.info(
          `Trending projects served from ${cachedTrending.fromMemory ? "memory" : "database"} cache`
        );
        return this.success({
          data: cachedTrending.data.slice(0, options.limit || 10),
          timeframe: options.timeframe || "_7Days",
          sector: options.sectorSlug,
          cached: true,
          cacheSource: cachedTrending.fromMemory ? "memory" : "database",
        });
      }

      // Check rate limits for API call
      const rateLimit = await checkRateLimit(
        userId,
        "COOKIE_API_CALLS" as FeatureType,
        1
      );
      if (!rateLimit.allowed) {
        return this.failure(
          `Cookie.fun API calls limit exceeded. ${rateLimit.remaining} remaining this month.`
        );
      }

      // Fetch from API
      const projects = await cookieClient.getTrendingProjects(
        options.sectorSlug,
        options.timeframe || "_7Days"
      );

      await cryptoCache.cacheTrending(
        projects,
        options.sectorSlug,
        options.timeframe || "_7Days"
      );
      await recordUsage(userId, "COOKIE_API_CALLS" as FeatureType, 1);

      return this.success({
        data: projects.slice(0, options.limit || 10),
        timeframe: options.timeframe || "_7Days",
        sector: options.sectorSlug,
        cached: false,
        cacheSource: "api",
      });
    } catch (error) {
      return this.handleError(error, "getTrendingProjects");
    }
  }

  /**
   * Get project metrics over time
   */
  async getProjectMetrics(
    userId: string,
    options: ProjectMetricsOptions
  ): Promise<ServiceResult> {
    try {
      this.logger.info(`Getting metrics for project: ${options.projectSlug}`);

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      // Check rate limits
      const rateLimit = await checkRateLimit(
        userId,
        "COOKIE_API_CALLS" as FeatureType,
        1
      );
      if (!rateLimit.allowed) {
        return this.failure(
          `Cookie.fun API calls limit exceeded. ${rateLimit.remaining} remaining this month.`
        );
      }

      const metrics = await cookieClient.getProjectMetrics({
        projectSlug: options.projectSlug,
        metricType: options.metricType,
        granulation: options.granulation,
        startDate: options.startDate,
        endDate: options.endDate,
      });

      await recordUsage(userId, "COOKIE_API_CALLS" as FeatureType, 1);

      return this.success({
        data: metrics,
        project: options.projectSlug,
        metricType: options.metricType,
        granulation: options.granulation,
      });
    } catch (error) {
      return this.handleError(error, "getProjectMetrics");
    }
  }

  /**
   * Get competitive analysis for a project
   */
  async getCompetitiveAnalysis(
    userId: string,
    projectSlug: string
  ): Promise<ServiceResult> {
    try {
      this.logger.info(`Getting competitive analysis for: ${projectSlug}`);

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      // Check rate limits (multiple API calls)
      const rateLimit = await checkRateLimit(
        userId,
        "COOKIE_API_CALLS" as FeatureType,
        3
      );
      if (!rateLimit.allowed) {
        return this.failure(
          `Cookie.fun API calls limit exceeded. ${rateLimit.remaining} remaining this month.`
        );
      }

      const analysis = await cookieClient.getCompetitiveAnalysis(projectSlug);
      await recordUsage(userId, "COOKIE_API_CALLS" as FeatureType, 3);

      return this.success(analysis);
    } catch (error) {
      return this.handleError(error, "getCompetitiveAnalysis");
    }
  }

  /**
   * Find sector influencers
   */
  async findSectorInfluencers(
    userId: string,
    options: SectorInfluencersOptions
  ): Promise<ServiceResult> {
    try {
      this.logger.info(
        `Finding sector influencers for: ${options.targetSector}`
      );

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      // Check rate limits
      const rateLimit = await checkRateLimit(
        userId,
        "COOKIE_API_CALLS" as FeatureType,
        1
      );
      if (!rateLimit.allowed) {
        return this.failure(
          `Cookie.fun API calls limit exceeded. ${rateLimit.remaining} remaining this month.`
        );
      }

      const influencers = await cookieClient.findSectorInfluencers(
        options.username,
        options.targetSector
      );

      await recordUsage(userId, "COOKIE_API_CALLS" as FeatureType, 1);

      return this.success({
        data: influencers.slice(0, options.limit || 10),
        sector: options.targetSector,
        sourceAccount: options.username,
      });
    } catch (error) {
      return this.handleError(error, "findSectorInfluencers");
    }
  }

  /**
   * Get market intelligence for AI context
   */
  async getMarketIntelligence(
    userId: string,
    options: MarketIntelligenceOptions
  ): Promise<ServiceResult> {
    try {
      this.logger.info("Getting market intelligence for AI context");

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      // Check rate limits
      const rateLimit = await checkRateLimit(
        userId,
        "COOKIE_API_CALLS" as FeatureType,
        1
      );
      if (!rateLimit.allowed) {
        return this.failure(
          `Cookie.fun API calls limit exceeded. ${rateLimit.remaining} remaining this month.`
        );
      }

      // Get trending projects for context
      const trendingProjects = await cookieClient.getTrendingProjects(
        options.sectorSlug,
        options.timeframe || "_7Days"
      );

      // Get specific project data if requested
      interface ProjectData {
        slug: string;
        name: string;
        description?: string;
        sector?: string;
        [key: string]: any;
      }
      
      let projectData: ProjectData[] = [];
      if (options.projectSlugs && options.projectSlugs.length > 0) {
        const projectPromises = options.projectSlugs.map((slug) =>
          cookieClient.searchProjects({ query: slug, limit: 1 })
        );
        const results = await Promise.all(projectPromises);
        projectData = results.map((r) => r.data[0]).filter(Boolean);
      }

      await recordUsage(userId, "COOKIE_API_CALLS" as FeatureType, 1);

      return this.success({
        trendingProjects: trendingProjects.slice(0, 5),
        projectData,
        timeframe: options.timeframe || "_7Days",
        sector: options.sectorSlug,
        generatedAt: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error("Get market intelligence error:", error as Error);

      // Don't throw error for AI context - return empty data
      return this.success({
        trendingProjects: [],
        projectData: [],
        timeframe: options.timeframe || "_7Days",
        sector: options.sectorSlug,
        generatedAt: new Date().toISOString(),
        error: "Failed to fetch market intelligence",
      });
    }
  }

  /**
   * Clear crypto cache
   */
  async clearCache(options: CacheClearOptions): Promise<ServiceResult> {
    try {
      this.logger.info(`Clearing cache: ${options.type || "all"}`);

      await cryptoCache.invalidateCache(
        options.type || "all",
        options.sectorSlug,
        options.timeframe
      );

      cookieClient.clearCache();

      return this.success({
        message: `Crypto cache cleared: ${options.type || "all"}`,
      });
    } catch (error) {
      return this.handleError(error, "clearCache");
    }
  }

  /**
   * Get cache statistics
   */
  async getCacheStats(): Promise<ServiceResult> {
    try {
      this.logger.info("Getting cache statistics");

      const [cryptoCacheStats, cookieClientStats] = await Promise.all([
        cryptoCache.getCacheStats(),
        cookieClient.getCacheStats(),
      ]);

      return this.success({
        cryptoCache: cryptoCacheStats,
        cookieClient: cookieClientStats,
      });
    } catch (error) {
      return this.handleError(error, "getCacheStats");
    }
  }

  /**
   * Cleanup expired cache entries
   */
  async cleanupCache(): Promise<ServiceResult> {
    try {
      this.logger.info("Cleaning up expired cache entries");

      const result = await cryptoCache.cleanupExpiredCache();

      return this.success({
        message: `Cleaned up ${result.sectorsDeleted} expired sectors and ${result.trendingDeleted} expired trending cache entries`,
        sectorsDeleted: result.sectorsDeleted,
        trendingDeleted: result.trendingDeleted,
      });
    } catch (error) {
      return this.handleError(error, "cleanupCache");
    }
  }
}

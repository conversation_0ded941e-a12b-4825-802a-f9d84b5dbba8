/**
 * User Service
 *
 * Handles all user-related business logic including profile management,
 * usage tracking, personality settings, and AI model selection.
 */

import { clerkClient } from "@clerk/nextjs/server";
import type { PlanFeature, PrismaClient } from "../../prisma/generated";
import {
  sanitizeMetadata,
  sanitizeSystemPrompt,
  sanitizeUserName,
} from "../lib/security-utils";
import {
  canUserUseFeature,
  getOrCreateUser,
  getUserStats,
  logUsage,
} from "../lib/user-service";
import { BaseService } from "./base.service";
import type {
  ILogger,
  IUserService,
  PersonalityUpdate,
  ServiceResult,
  UserProfileUpdate,
} from "./interfaces";

export class UserService extends BaseService implements IUserService {
  constructor(prisma: PrismaClient, logger: ILogger) {
    super(prisma, logger);
  }

  /**
   * Get user profile with plan and usage data
   */
  async getProfile(userId: string): Promise<ServiceResult> {
    try {
      this.logger.info(`Getting profile for user: ${userId}`);

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      // Get Clerk user data
      const clerk = await clerkClient();
      const clerkUser = await clerk.users.getUser(userId);

      // Get or create user in database
      const user = await getOrCreateUser(userId, {
        email: clerkUser.emailAddresses[0]?.emailAddress,
        name:
          clerkUser.firstName && clerkUser.lastName
            ? `${clerkUser.firstName} ${clerkUser.lastName}`
            : clerkUser.firstName || undefined,
        avatar: clerkUser.imageUrl,
      });

      // Get user statistics
      const stats = await getUserStats(userId);

      const profileData = {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          avatar: user.avatar,
          isAdmin: user.isAdmin,
          createdAt: user.createdAt,
          lastActiveAt: user.lastActiveAt,
        },
        plan: {
          id: user.plan.id,
          name: user.plan.name,
          displayName: user.plan.displayName,
          description: user.plan.description,
          price: user.plan.price,
          features: user.plan.features.map((f: PlanFeature) => ({
            feature: f.feature,
            limit: f.limit,
          })),
        },
        stats,
      };

      return this.success(profileData);
    } catch (error) {
      return this.handleError(error, "getProfile");
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(
    userId: string,
    updates: UserProfileUpdate
  ): Promise<ServiceResult> {
    try {
      this.logger.info(`Updating profile for user: ${userId}`);

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      // Sanitize inputs
      const sanitizedName = updates.name
        ? sanitizeUserName(updates.name)
        : undefined;
      const sanitizedMetadata = updates.metadata
        ? sanitizeMetadata(updates.metadata)
        : undefined;

      // Update in Clerk if name is provided
      if (sanitizedName) {
        const [firstName, ...lastNameParts] = sanitizedName.split(" ");
        const clerk = await clerkClient();
        await clerk.users.updateUser(userId, {
          firstName,
          lastName: lastNameParts.join(" ") || undefined,
        });
      }

      // Update in database
      const user = await this.executeWithRetry(async () => {
        return await this.prisma.user.update({
          where: { id: userId },
          data: {
            name: sanitizedName,
            lastActiveAt: new Date(),
            // Note: metadata field might need to be added to schema
          },
        });
      });

      return this.success({
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          avatar: user.avatar,
        },
        message: "Profile updated successfully",
      });
    } catch (error) {
      return this.handleError(error, "updateProfile");
    }
  }

  /**
   * Get user's current usage for all features
   */
  async getUsage(userId: string): Promise<ServiceResult> {
    try {
      this.logger.info(`Getting usage for user: ${userId}`);

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      const features = [
        "AI_CALLS",
        "IMAGE_GENERATIONS",
        "MONITORED_ACCOUNTS",
        "MENTIONS_PER_MONTH",
      ];

      const usage = await Promise.all(
        features.map(async (feature) => {
          const result = await canUserUseFeature(userId, feature);
          return {
            feature,
            ...result,
          };
        })
      );

      return this.success(usage);
    } catch (error) {
      return this.handleError(error, "getUsage");
    }
  }

  /**
   * Check if user can use a specific feature
   */
  async canUseFeature(userId: string, feature: string): Promise<ServiceResult> {
    try {
      this.logger.debug(`Checking feature ${feature} for user: ${userId}`);

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      const result = await canUserUseFeature(userId, feature);
      return this.success(result);
    } catch (error) {
      return this.handleError(error, "canUseFeature");
    }
  }

  /**
   * Log feature usage
   */
  async logUsage(
    userId: string,
    feature: string,
    amount: number,
    metadata?: any
  ): Promise<ServiceResult> {
    try {
      this.logger.info(
        `Logging usage for user ${userId}: ${feature} (${amount})`
      );

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      if (amount < 1 || amount > 1000) {
        return this.failure("Invalid usage amount");
      }

      // Check if user can use this feature
      const canUse = await canUserUseFeature(userId, feature);
      if (!canUse.allowed) {
        return this.failure(
          `Usage limit exceeded for ${feature}. Current: ${canUse.currentUsage}, Limit: ${canUse.limit}`
        );
      }

      // Sanitize metadata and log usage
      const sanitizedMetadata = metadata
        ? this.sanitizeMetadata(metadata)
        : undefined;
      await logUsage(userId, feature, amount, sanitizedMetadata);

      return this.success({ message: "Usage logged successfully" });
    } catch (error) {
      return this.handleError(error, "logUsage");
    }
  }

  /**
   * Get user's personality settings
   */
  async getPersonality(userId: string): Promise<ServiceResult> {
    try {
      this.logger.info(`Getting personality for user: ${userId}`);

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: {
          personalityId: true,
          customSystemPrompt: true,
          useFirstPerson: true,
        },
      });

      if (!user) {
        return this.failure("User not found");
      }

      return this.success({
        personalityId: user.personalityId,
        customSystemPrompt: user.customSystemPrompt,
        useFirstPerson: user.useFirstPerson,
      });
    } catch (error) {
      return this.handleError(error, "getPersonality");
    }
  }

  /**
   * Update user's personality settings
   */
  async updatePersonality(
    userId: string,
    updates: PersonalityUpdate
  ): Promise<ServiceResult> {
    try {
      this.logger.info(`Updating personality for user: ${userId}`);

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      // Sanitize system prompt
      const sanitizedPrompt = updates.customSystemPrompt
        ? sanitizeSystemPrompt(updates.customSystemPrompt)
        : undefined;

      const updateData: any = {
        personalityId: updates.personalityId,
        customSystemPrompt: sanitizedPrompt,
        lastActiveAt: new Date(),
      };

      // Only update useFirstPerson if provided
      if (updates.useFirstPerson !== undefined) {
        updateData.useFirstPerson = updates.useFirstPerson;
      }

      await this.executeWithRetry(async () => {
        await this.prisma.user.update({
          where: { id: userId },
          data: updateData,
        });
      });

      return this.success({
        message: "Personality settings updated successfully",
      });
    } catch (error) {
      return this.handleError(error, "updatePersonality");
    }
  }

  /**
   * Get available AI models
   */
  async getAvailableModels(): Promise<ServiceResult> {
    try {
      this.logger.info("Fetching available AI models");

      const models = await this.prisma.aIModel.findMany({
        where: { isActive: true },
        select: {
          id: true,
          name: true,
          displayName: true,
          description: true,
          provider: true,
          modelId: true,
          costTier: true,
          speed: true,
        },
        orderBy: { name: "asc" },
      });

      return this.success(models);
    } catch (error) {
      return this.handleError(error, "getAvailableModels");
    }
  }

  /**
   * Get user's selected AI model
   */
  async getSelectedModel(userId: string): Promise<ServiceResult> {
    try {
      this.logger.info(`Getting selected model for user: ${userId}`);

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: {
          modelId: true,
          selectedModel: {
            select: {
              id: true,
              name: true,
              displayName: true,
              description: true,
              provider: true,
              modelId: true,
              costTier: true,
              speed: true,
            },
          },
        },
      });

      if (!user) {
        return this.failure("User not found");
      }

      return this.success(user.selectedModel || null);
    } catch (error) {
      return this.handleError(error, "getSelectedModel");
    }
  }

  /**
   * Update user's selected AI model
   */
  async updateSelectedModel(
    userId: string,
    modelId?: string
  ): Promise<ServiceResult> {
    try {
      this.logger.info(
        `Updating selected model for user ${userId} to: ${modelId || "None"}`
      );

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      // Validate model exists if provided
      if (modelId) {
        const model = await this.prisma.aIModel.findUnique({
          where: { id: modelId },
        });

        if (!model) {
          return this.failure("AI model not found");
        }

        if (!model.isActive) {
          return this.failure("Selected AI model is not active");
        }
      }

      // Update user's selected model
      await this.executeWithRetry(async () => {
        await this.prisma.user.update({
          where: { id: userId },
          data: {
            modelId: modelId || null,
            lastActiveAt: new Date(),
          },
        });
      });

      return this.success({ message: "Model selection updated successfully" });
    } catch (error) {
      return this.handleError(error, "updateSelectedModel");
    }
  }

  /**
   * Get available personality profiles
   */
  async getAvailablePersonalities(): Promise<ServiceResult> {
    try {
      this.logger.info("Fetching available personality profiles");

      const personalities = await this.prisma.personalityProfile.findMany({
        where: { isActive: true },
        select: {
          id: true,
          name: true,
          description: true,
        },
        orderBy: { name: "asc" },
      });

      return this.success(personalities);
    } catch (error) {
      return this.handleError(error, "getAvailablePersonalities");
    }
  }
}

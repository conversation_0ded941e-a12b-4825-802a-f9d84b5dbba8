/**
 * Subscription Service
 *
 * Handles all subscription-related business logic including
 * feature access control, usage tracking, and rate limiting.
 */

import type { FeatureType, PrismaClient } from "../../prisma/generated";
import { checkRateLimit, recordUsage } from "../lib/db-utils";
import { canUserUseFeature, logUsage } from "../lib/user-service";
import { BaseService } from "./base.service";
import type {
  ILogger,
  ISubscriptionService,
  ServiceResult,
} from "./interfaces";

export class SubscriptionService
  extends BaseService
  implements ISubscriptionService
{
  constructor(prisma: PrismaClient, logger: ILogger) {
    super(prisma, logger);
  }

  /**
   * Check if user has access to a specific feature
   */
  async checkFeatureAccess(
    userId: string,
    feature: string
  ): Promise<ServiceResult> {
    try {
      this.logger.info(
        `Checking feature access for user: ${userId}, feature: ${feature}`
      );

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      const result = await canUserUseFeature(userId, feature);

      return this.success({
        allowed: result.allowed,
        currentUsage: result.currentUsage,
        limit: result.limit,
        remaining: result.limit - result.currentUsage,
        resetDate: result.resetDate,
        feature,
      });
    } catch (error) {
      return this.handleError(error, "checkFeatureAccess");
    }
  }

  /**
   * Record usage for a specific feature
   */
  async recordUsage(
    userId: string,
    feature: string,
    amount: number,
    metadata?: any
  ): Promise<ServiceResult> {
    try {
      this.logger.info(
        `Recording usage for user: ${userId}, feature: ${feature}, amount: ${amount}`
      );

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      if (amount <= 0 || amount > 1000) {
        return this.failure("Invalid usage amount");
      }

      // Check if user can use this feature
      const canUse = await canUserUseFeature(userId, feature);
      if (!canUse.allowed) {
        return this.failure(
          `Usage limit exceeded for ${feature}. Current: ${canUse.currentUsage}, Limit: ${canUse.limit}`
        );
      }

      // Sanitize metadata
      const sanitizedMetadata = metadata
        ? this.sanitizeMetadata(metadata)
        : undefined;

      // Record the usage
      await logUsage(userId, feature, amount, sanitizedMetadata);

      return this.success({
        message: "Usage recorded successfully",
        feature,
        amount,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      return this.handleError(error, "recordUsage");
    }
  }

  /**
   * Get usage statistics for multiple features
   */
  async getUsageStats(
    userId: string,
    features?: string[]
  ): Promise<ServiceResult> {
    try {
      this.logger.info(`Getting usage stats for user: ${userId}`);

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      const defaultFeatures = [
        "AI_CALLS",
        "IMAGE_GENERATIONS",
        "MONITORED_ACCOUNTS",
        "MENTIONS_PER_MONTH",
        "COOKIE_API_CALLS",
        "STORAGE_GB",
        "TEAM_MEMBERS",
      ];

      const featuresToCheck = features || defaultFeatures;

      const usage = await Promise.all(
        featuresToCheck.map(async (feature) => {
          const result = await canUserUseFeature(userId, feature);
          return {
            feature,
            allowed: result.allowed,
            currentUsage: result.currentUsage,
            limit: result.limit,
            remaining: result.limit - result.currentUsage,
            resetDate: result.resetDate,
            usagePercentage:
              result.limit > 0 ? (result.currentUsage / result.limit) * 100 : 0,
          };
        })
      );

      return this.success({
        usage,
        totalFeatures: usage.length,
        exceededFeatures: usage.filter((u) => !u.allowed).length,
        warningFeatures: usage.filter((u) => u.usagePercentage > 80).length,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      return this.handleError(error, "getUsageStats");
    }
  }

  /**
   * Get rate limits for a user
   */
  async getRateLimits(userId: string): Promise<ServiceResult> {
    try {
      this.logger.info(`Getting rate limits for user: ${userId}`);

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      // Get user's plan with features
      const user = await this.executeWithRetry(async () => {
        return await this.prisma.user.findUnique({
          where: { id: userId },
          include: {
            plan: {
              include: {
                features: true,
              },
            },
          },
        });
      });

      if (!user) {
        return this.failure("User not found");
      }

      const rateLimits = user.plan.features.map((feature) => ({
        feature: feature.feature,
        limit: feature.limit,
        resetPeriod: "monthly", // All features reset monthly
      }));

      return this.success({
        rateLimits,
        plan: {
          name: user.plan.name,
          displayName: user.plan.displayName,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      return this.handleError(error, "getRateLimits");
    }
  }

  /**
   * Upgrade user subscription (placeholder)
   */
  async upgradeSubscription(
    userId: string,
    planId: string
  ): Promise<ServiceResult> {
    try {
      this.logger.info(
        `Upgrading subscription for user: ${userId} to plan: ${planId}`
      );

      if (!this.validateUserId(userId) || !this.validateId(planId)) {
        return this.failure("Invalid user ID or plan ID");
      }

      // Check if plan exists
      const plan = await this.prisma.subscriptionPlan.findUnique({
        where: { id: planId },
      });

      if (!plan) {
        return this.failure("Subscription plan not found");
      }

      // Update user's plan
      await this.executeWithRetry(async () => {
        await this.prisma.user.update({
          where: { id: userId },
          data: {
            planId: planId,
            lastActiveAt: new Date(),
          },
        });
      });

      this.logger.info(
        `Successfully upgraded user ${userId} to plan ${plan.name}`
      );
      return this.success({
        message: `Successfully upgraded to ${plan.displayName}`,
        plan: {
          id: plan.id,
          name: plan.name,
          displayName: plan.displayName,
        },
      });
    } catch (error) {
      return this.handleError(error, "upgradeSubscription");
    }
  }

  /**
   * Cancel user subscription (placeholder)
   */
  async cancelSubscription(userId: string): Promise<ServiceResult> {
    try {
      this.logger.info(`Canceling subscription for user: ${userId}`);

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      // Get free plan
      const freePlan = await this.prisma.subscriptionPlan.findFirst({
        where: { name: "free" },
      });

      if (!freePlan) {
        return this.failure("Free plan not found");
      }

      // Downgrade to free plan
      await this.executeWithRetry(async () => {
        await this.prisma.user.update({
          where: { id: userId },
          data: {
            planId: freePlan.id,
            lastActiveAt: new Date(),
          },
        });
      });

      this.logger.info(`Successfully canceled subscription for user ${userId}`);
      return this.success({
        message: "Subscription canceled successfully",
        plan: {
          id: freePlan.id,
          name: freePlan.name,
          displayName: freePlan.displayName,
        },
      });
    } catch (error) {
      return this.handleError(error, "cancelSubscription");
    }
  }

  /**
   * Check rate limit for a specific feature
   */
  async checkRateLimit(
    userId: string,
    feature: string,
    requestedAmount: number = 1
  ): Promise<ServiceResult> {
    try {
      this.logger.info(
        `Checking rate limit for user: ${userId}, feature: ${feature}`
      );

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      const result = await checkRateLimit(
        userId,
        feature as FeatureType,
        requestedAmount
      );

      return this.success({
        allowed: result.allowed,
        remaining: result.remaining,
        limit: result.limit,
        resetDate: new Date(),
        feature,
        requestedAmount,
      });
    } catch (error) {
      return this.handleError(error, "checkRateLimit");
    }
  }

  /**
   * Record usage directly through database utils
   */
  async recordUsageDirectly(
    userId: string,
    feature: string,
    amount: number,
    metadata?: any
  ): Promise<ServiceResult> {
    try {
      this.logger.info(
        `Recording usage directly for user: ${userId}, feature: ${feature}`
      );

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      const sanitizedMetadata = metadata
        ? this.sanitizeMetadata(metadata)
        : undefined;

      await recordUsage(
        userId,
        feature as FeatureType,
        amount,
        sanitizedMetadata
      );

      return this.success({
        message: "Usage recorded successfully",
        feature,
        amount,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      return this.handleError(error, "recordUsageDirectly");
    }
  }
}

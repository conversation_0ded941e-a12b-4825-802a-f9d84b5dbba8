/**
 * Logger Service
 *
 * Centralized logging implementation for the service layer
 */

import type { ILogger } from "./interfaces";

export class Logger implements ILogger {
  private readonly context: string;
  private readonly enableDebug: boolean;

  constructor(context: string = "Service") {
    this.context = context;
    this.enableDebug =
      process.env.NODE_ENV === "development" ||
      process.env.VERBOSE_LOGGING === "true";
  }

  info(message: string, ...args: any[]): void {
    console.log(`[${this.context}] ℹ️ ${message}`, ...args);
  }

  error(message: string, error?: Error, ...args: any[]): void {
    if (error) {
      console.error(`[${this.context}] ❌ ${message}`, error, ...args);
    } else {
      console.error(`[${this.context}] ❌ ${message}`, ...args);
    }
  }

  warn(message: string, ...args: any[]): void {
    console.warn(`[${this.context}] ⚠️ ${message}`, ...args);
  }

  debug(message: string, ...args: any[]): void {
    if (this.enableDebug) {
      console.debug(`[${this.context}] 🔍 ${message}`, ...args);
    }
  }
}

// Factory function for creating contextual loggers
export function createLogger(context: string): ILogger {
  return new Logger(context);
}

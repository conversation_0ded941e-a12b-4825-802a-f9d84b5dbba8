/**
 * Base Service Class
 *
 * Provides common functionality for all service classes including
 * logging, error handling, and database access patterns.
 */

import type { PrismaClient } from "../../prisma/generated";
import type { ExtendedPrismaClient } from "../lib/prisma-config";
import type { IBaseService, ILogger, ServiceResult } from "./interfaces";

export class BaseService implements IBaseService {
  protected readonly _prisma: PrismaClient | ExtendedPrismaClient;
  protected readonly _logger: ILogger;

  constructor(prisma: PrismaClient | ExtendedPrismaClient, logger: ILogger) {
    this._prisma = prisma;
    this._logger = logger;
  }

  get prisma(): PrismaClient | ExtendedPrismaClient {
    return this._prisma;
  }

  get logger(): ILogger {
    return this._logger;
  }

  /**
   * Create a successful service result
   */
  protected success<T>(data: T, message?: string): ServiceResult<T> {
    return {
      success: true,
      data,
      message,
    };
  }

  /**
   * Create a failed service result
   */
  protected failure(error: string, data?: any): ServiceResult {
    return {
      success: false,
      error,
      data,
    };
  }

  /**
   * Handle errors consistently across all services
   */
  protected handleError(error: unknown, context: string): ServiceResult {
    this._logger.error(
      `Error in ${context}:`,
      error instanceof Error ? error : new Error(String(error))
    );

    // Handle known error types
    if (error instanceof Error) {
      // Prisma errors
      if (error.name === "PrismaClientKnownRequestError") {
        return this.failure(`Database operation failed: ${error.message}`);
      }

      if (error.name === "PrismaClientInitializationError") {
        return this.failure("Database is temporarily unavailable");
      }

      // Validation errors
      if (error.name === "ValidationError") {
        return this.failure(`Validation failed: ${error.message}`);
      }

      // Generic error
      return this.failure(error.message);
    }

    // Unknown error type
    return this.failure("An unexpected error occurred");
  }

  /**
   * Validate required parameters
   */
  protected validateRequired(
    params: Record<string, any>,
    requiredFields: string[]
  ): string | null {
    for (const field of requiredFields) {
      if (!params[field]) {
        return `${field} is required`;
      }
    }
    return null;
  }

  /**
   * Validate user ID format (assuming CUID format)
   */
  protected validateUserId(userId: string): boolean {
    if (!userId || typeof userId !== "string") {
      return false;
    }

    // Basic CUID validation (starts with 'c', 25 characters total)
    return /^c[a-z0-9]{24}$/.test(userId);
  }

  /**
   * Validate ID format (CUID)
   */
  protected validateId(id: string): boolean {
    if (!id || typeof id !== "string") {
      return false;
    }

    // Basic CUID validation - starts with 'c' followed by 24 alphanumeric characters
    return /^c[a-z0-9]{24}$/.test(id);
  }

  /**
   * Sanitize string input to prevent XSS and other attacks
   */
  protected sanitizeString(input: string, maxLength?: number): string {
    if (!input) return "";

    // HTML escape potentially dangerous characters instead of removing them
    let sanitized = input
      .replace(/&/g, "&amp;") // Must be first to avoid double-escaping
      .replace(/</g, "&lt;") // Escape less-than
      .replace(/>/g, "&gt;") // Escape greater-than
      .replace(/"/g, "&quot;") // Escape double quotes
      .replace(/'/g, "&#x27;") // Escape single quotes
      .replace(/\0/g, "") // Remove null bytes
      .trim();

    // Truncate if max length specified
    if (maxLength && sanitized.length > maxLength) {
      sanitized = sanitized.substring(0, maxLength);
    }

    return sanitized;
  }

  /**
   * Sanitize object for metadata storage
   */
  protected sanitizeMetadata(
    metadata: Record<string, any>
  ): Record<string, any> {
    const sanitized: Record<string, any> = {};

    for (const [key, value] of Object.entries(metadata)) {
      // Skip functions and undefined values
      if (typeof value === "function" || value === undefined) {
        continue;
      }

      // Sanitize key
      const sanitizedKey = this.sanitizeString(key, 100);
      if (!sanitizedKey) continue;

      // Sanitize value based on type
      if (typeof value === "string") {
        sanitized[sanitizedKey] = this.sanitizeString(value, 1000);
      } else if (typeof value === "number" || typeof value === "boolean") {
        sanitized[sanitizedKey] = value;
      } else if (value instanceof Date) {
        sanitized[sanitizedKey] = value.toISOString();
      } else if (Array.isArray(value)) {
        sanitized[sanitizedKey] = value.slice(0, 100); // Limit array size
      } else if (typeof value === "object" && value !== null) {
        // Recursively sanitize nested objects (limit depth)
        sanitized[sanitizedKey] = this.sanitizeMetadata(value);
      }
    }

    return sanitized;
  }

  /**
   * Execute database operation with retry logic
   */
  protected async executeWithRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    context: string = "database operation"
  ): Promise<T> {
    let lastError: Error | null = null;
    const maxDelayMs = 30000; // Maximum delay of 30 seconds

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        // Enhanced error detection - check for non-retryable errors
        if (this.isNonRetryableError(lastError)) {
          throw lastError;
        }

        // Log retry attempt
        this._logger.warn(
          `${context} failed on attempt ${attempt}/${maxRetries}:`,
          lastError
        );

        // Wait before retry (exponential backoff with maximum delay cap)
        if (attempt < maxRetries) {
          const baseDelay = 2 ** attempt * 1000;
          const delayMs = Math.min(baseDelay, maxDelayMs);
          await new Promise((resolve) => setTimeout(resolve, delayMs));
        }
      }
    }

    throw lastError;
  }

  /**
   * Check if an error is non-retryable
   */
  private isNonRetryableError(error: Error): boolean {
    // Check error type/constructor
    if (error.name === "ValidationError" || error.name === "TypeError") {
      return true;
    }

    // Check for specific error codes (common database/API error codes)
    const errorCode = (error as any).code;
    if (errorCode) {
      // Non-retryable database error codes
      const nonRetryableCodes = [
        "P2002", // Unique constraint violation
        "P2003", // Foreign key constraint violation
        "P2025", // Record not found
        "P2014", // Required relation violation
        "P2015", // Related record not found
        "P2016", // Query interpretation error
        "P2017", // Records not connected
        "P2018", // Required connected records not found
        "P2019", // Input error
        "P2020", // Value out of range
        "P2021", // Table not found
        "P2022", // Column not found
        "P2023", // Inconsistent column data
        "P2024", // Timed out fetching connection
        "P2027", // Multiple errors
        "EAUTH", // Authentication error
        "EACCES", // Permission denied
        "ENOTFOUND", // Not found
      ];

      if (nonRetryableCodes.includes(errorCode)) {
        return true;
      }
    }

    // Check error message for patterns
    const message = error.message.toLowerCase();
    const nonRetryablePatterns = [
      "validation",
      "constraint",
      "unauthorized",
      "forbidden",
      "not found",
      "invalid",
      "malformed",
      "syntax error",
      "permission denied",
      "access denied",
    ];

    return nonRetryablePatterns.some((pattern) => message.includes(pattern));
  }

  /**
   * Check if user has permission to access a resource
   */
  protected async checkUserPermission(
    userId: string,
    resourceType: string,
    resourceId: string
  ): Promise<boolean> {
    try {
      // This is a simplified permission check - in a real application,
      // you might have more complex permission logic
      switch (resourceType) {
        case "monitored_account": {
          const account = await this._prisma.monitoredAccount.findFirst({
            where: { id: resourceId, userId },
          });
          return !!account;
        }

        case "mention": {
          const mention = await this._prisma.mention.findFirst({
            where: {
              id: resourceId,
              account: { userId },
            },
          });
          return !!mention;
        }

        default:
          return false;
      }
    } catch (error) {
      this._logger.error(
        `Permission check failed for ${resourceType}:${resourceId}:`,
        error as Error
      );
      return false;
    }
  }

  /**
   * Log security event
   */
  protected logSecurityEvent(
    userId: string,
    eventType: string,
    details: string,
    severity: "low" | "medium" | "high" = "medium"
  ): void {
    this._logger.warn(`Security event [${severity}] - ${eventType}:`, {
      userId,
      details,
      timestamp: new Date().toISOString(),
    });
  }
}

/**
 * Account Service
 *
 * Handles all business logic related to monitored accounts including
 * addition, removal, status management, and smart account discovery.
 */

import { TRPCError } from "@trpc/server";
import type { FeatureType, PrismaClient } from "../../prisma/generated";
import { cookieClient } from "../lib/cookie-client";
import { checkRateLimit, recordUsage } from "../lib/db-utils";
import { JobHelpers } from "../lib/job-manager";
import { MentionSyncService } from "../lib/mention-sync-service";
import {
  logSecurityEvent,
  sanitizeTwitterHandle,
  validateCUID,
} from "../lib/security-utils";
import { twitterClient } from "../lib/twitter-client";
import { canUserUseFeature } from "../lib/user-service";
import { BaseService } from "./base.service";
import type {
  AccountSyncSettings,
  IAccountService,
  ILogger,
  PaginationOptions,
  ServiceResult,
  SmartAccountDiscoveryOptions,
} from "./interfaces";

export class AccountService extends BaseService implements IAccountService {
  private readonly mentionSyncService: MentionSyncService;

  constructor(prisma: PrismaClient, logger: ILogger) {
    super(prisma, logger);
    this.mentionSyncService = new MentionSyncService(prisma);
  }

  /**
   * Get all monitored accounts for a user
   */
  async getMonitoredAccounts(
    userId: string,
    options?: PaginationOptions
  ): Promise<ServiceResult> {
    try {
      this.logger.info(`Fetching monitored accounts for user: ${userId}`);

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      const accounts = await this.executeWithRetry(async () => {
        return await this.prisma.monitoredAccount.findMany({
          where: { userId },
          orderBy: { createdAt: "desc" },
          include: {
            _count: {
              select: { mentions: true },
            },
          },
        });
      });

      const mappedAccounts = accounts.map((account) => ({
        id: account.id,
        handle: account.twitterHandle,
        displayName: account.displayName,
        avatar: account.avatarUrl,
        isActive: account.isActive,
        platform: "twitter",
        followerCount: 0,
        createdAt: account.createdAt,
        lastSyncAt: account.lastCheckedAt,
        mentionsCount: account._count.mentions,
        syncMentions: account.syncMentions,
        syncUserTweets: account.syncUserTweets,
        syncReplies: account.syncReplies,
        syncRetweets: account.syncRetweets,
      }));

      this.logger.info(`Returning ${mappedAccounts.length} accounts`);
      return this.success({ accounts: mappedAccounts });
    } catch (error) {
      return this.handleError(error, "getMonitoredAccounts");
    }
  }

  /**
   * Add a new monitored account
   */
  async addAccount(
    userId: string,
    handle: string,
    syncSettings?: AccountSyncSettings
  ): Promise<ServiceResult> {
    try {
      this.logger.info(`Adding account @${handle} for user: ${userId}`);

      if (!this.validateUserId(userId)) {
        return this.failure("Invalid user ID");
      }

      // Check feature limits
      const featureCheck = await canUserUseFeature(
        userId,
        "MONITORED_ACCOUNTS"
      );
      if (!featureCheck.allowed) {
        return this.failure(
          `You've reached your limit of ${featureCheck.limit} monitored accounts`
        );
      }

      // Clean and validate handle
      const cleanHandle = handle.replace("@", "").toLowerCase();
      if (!twitterClient.validateTwitterHandle(cleanHandle)) {
        return this.failure("Invalid Twitter handle format");
      }

      // Check if already monitored
      const existingAccount = await this.prisma.monitoredAccount.findFirst({
        where: { userId, twitterHandle: cleanHandle },
      });

      if (existingAccount) {
        return this.failure(`You're already monitoring @${cleanHandle}`);
      }

      // Fetch Twitter user info
      let userInfo;
      try {
        userInfo = await twitterClient.getUserInfo(cleanHandle);
      } catch (error: any) {
        this.logger.error("Twitter API error:", error as Error);
        return this.failure(this.getTwitterApiErrorMessage(error, cleanHandle));
      }

      // Create account with sync settings
      const defaultSyncSettings: AccountSyncSettings = {
        syncMentions: true,
        syncUserTweets: false,
        syncReplies: false,
        syncRetweets: true,
      };

      const finalSyncSettings = syncSettings || defaultSyncSettings;

      const account = await this.executeWithRetry(async () => {
        return await this.prisma.monitoredAccount.create({
          data: {
            userId,
            twitterHandle: cleanHandle,
            twitterId: userInfo.id,
            displayName: userInfo.name || cleanHandle,
            avatarUrl: userInfo.profilePicture,
            isActive: true,
            lastCheckedAt: new Date(),
            ...finalSyncSettings,
          },
        });
      });

      // Schedule background sync
      try {
        await JobHelpers.createMentionSyncJob(
          this.prisma as PrismaClient,
          account.id,
          userId,
          cleanHandle
        );
      } catch (jobError) {
        this.logger.warn(`Failed to create background sync job: ${jobError}`, {
          accountId: account.id,
          userId,
          handle: cleanHandle,
          error: jobError instanceof Error ? jobError.message : String(jobError),
          stack: jobError instanceof Error ? jobError.stack : undefined,
          timestamp: new Date().toISOString()
        });
        
        // Consider retrying the job creation after a delay
        // For now, we'll continue with the account creation but track the sync failure
        setTimeout(async () => {
          try {
            await JobHelpers.createMentionSyncJob(
              this.prisma as PrismaClient,
              account.id,
              userId,
              cleanHandle
            );
            this.logger.info(`Successfully created background sync job on retry for account ${account.id}`);
          } catch (retryError) {
            this.logger.error(`Failed to create background sync job on retry`, retryError instanceof Error ? retryError : new Error(String(retryError)), {
              accountId: account.id,
              userId,
              handle: cleanHandle,
              originalError: jobError instanceof Error ? jobError.message : String(jobError),
              retryError: retryError instanceof Error ? retryError.message : String(retryError),
              timestamp: new Date().toISOString()
            });
          }
        }, 5000); // Retry after 5 seconds
      }

      const result = {
        account: {
          id: account.id,
          handle: account.twitterHandle,
          displayName: account.displayName,
          avatar: account.avatarUrl,
          isActive: account.isActive,
          platform: "twitter",
          followerCount: 0,
          createdAt: account.createdAt,
          lastSyncAt: account.lastCheckedAt,
          mentionsCount: 0,
        },
        message: `Now monitoring @${cleanHandle}`,
      };

      this.logger.info(`Successfully added account @${cleanHandle}`);
      return this.success(result);
    } catch (error) {
      return this.handleError(error, "addAccount");
    }
  }

  /**
   * Remove a monitored account
   */
  async removeAccount(
    userId: string,
    accountId: string
  ): Promise<ServiceResult> {
    try {
      this.logger.info(`Removing account ${accountId} for user: ${userId}`);

      if (!this.validateUserId(userId) || !this.validateId(accountId)) {
        return this.failure("Invalid user ID or account ID");
      }

      // Verify ownership
      const account = await this.prisma.monitoredAccount.findFirst({
        where: { id: accountId, userId },
      });

      if (!account) {
        this.logSecurityEvent(
          userId,
          "UNAUTHORIZED_ACCESS",
          `Attempted to remove account ${accountId}`
        );
        return this.failure("Account not found or access denied");
      }

      // Delete account and related mentions
      await this.executeWithRetry(async () => {
        await this.prisma.monitoredAccount.delete({
          where: { id: accountId },
        });
      });

      this.logger.info(
        `Successfully removed account @${account.twitterHandle}`
      );
      return this.success({
        message: `Stopped monitoring @${account.twitterHandle}`,
      });
    } catch (error) {
      return this.handleError(error, "removeAccount");
    }
  }

  /**
   * Toggle account monitoring status
   */
  async toggleAccountStatus(
    userId: string,
    accountId: string,
    isActive: boolean
  ): Promise<ServiceResult> {
    try {
      this.logger.info(`Toggling account ${accountId} status to ${isActive}`);

      if (!(await this.validateAccountOwnership(userId, accountId))) {
        return this.failure("Account not found or access denied");
      }

      const updatedAccount = await this.executeWithRetry(async () => {
        return await this.prisma.monitoredAccount.update({
          where: { id: accountId },
          data: { isActive },
        });
      });

      return this.success({
        account: updatedAccount,
        message: `@${updatedAccount.twitterHandle} monitoring ${isActive ? "enabled" : "disabled"}`,
      });
    } catch (error) {
      return this.handleError(error, "toggleAccountStatus");
    }
  }

  /**
   * Update sync settings for an account
   */
  async updateSyncSettings(
    userId: string,
    accountId: string,
    settings: AccountSyncSettings
  ): Promise<ServiceResult> {
    try {
      this.logger.info(`Updating sync settings for account ${accountId}`);

      if (!(await this.validateAccountOwnership(userId, accountId))) {
        return this.failure("Account not found or access denied");
      }

      const updatedAccount = await this.executeWithRetry(async () => {
        return await this.prisma.monitoredAccount.update({
          where: { id: accountId },
          data: {
            syncMentions: settings.syncMentions,
            syncUserTweets: settings.syncUserTweets,
            syncReplies: settings.syncReplies,
            syncRetweets: settings.syncRetweets,
          },
        });
      });

      // Trigger sync with new settings if account is active
      if (updatedAccount.isActive) {
        try {
          await this.mentionSyncService.syncAccountMentions(accountId, userId);
        } catch (syncError) {
          this.logger.warn("Sync after settings update failed:", syncError);
        }
      }

      return this.success({
        account: updatedAccount,
        message: `Sync settings updated for @${updatedAccount.twitterHandle}`,
      });
    } catch (error) {
      return this.handleError(error, "updateSyncSettings");
    }
  }

  /**
   * Discover smart accounts to monitor
   */
  async discoverSmartAccounts(
    userId: string,
    options: SmartAccountDiscoveryOptions
  ): Promise<ServiceResult> {
    try {
      this.logger.info(`Discovering smart accounts for user: ${userId}`);

      // Check rate limits
      const rateLimit = await checkRateLimit(
        userId,
        "COOKIE_API_CALLS" as FeatureType,
        2
      );
      if (!rateLimit.allowed) {
        return this.failure(
          `Cookie.fun API calls limit exceeded. ${rateLimit.remaining} remaining this month.`
        );
      }

      let discoveredAccounts: any[] = [];

      // Strategy 1: Smart followers
      if (options.seedAccount) {
        const cleanHandle = sanitizeTwitterHandle(options.seedAccount);
        const smartFollowersResponse = await cookieClient.getSmartFollowers({
          username: cleanHandle,
          limit: (options.limit || 10) * 2,
        });

        discoveredAccounts =
          smartFollowersResponse.data?.map((follower) => ({
            handle: follower.username,
            displayName: follower.displayName,
            avatarUrl: follower.profileImageUrl,
            followerCount: follower.followerCount,
            smartScore: follower.smartScore,
            influence: follower.influence,
            sector: follower.sector || options.targetSector,
            source: "smart_followers",
            seedAccount: cleanHandle,
          })) || [];
      }

      // Strategy 2: Trending projects
      if (
        options.targetSector &&
        discoveredAccounts.length < (options.limit || 10)
      ) {
        try {
          const trendingProjects = await cookieClient.getTrendingProjects(
            options.targetSector,
            "_7Days"
          );

          const sectorAccounts = trendingProjects
            .filter((project) => project.twitterUrl)
            .map((project) => {
              const handle = project.twitterUrl
                ? twitterClient.extractUsername(project.twitterUrl)
                : null;
              if (!handle) return null;

              return {
                handle,
                displayName: project.name,
                avatarUrl: undefined,
                followerCount: undefined,
                smartScore: project.mindshare,
                influence: project.smartEngagementPoints,
                sector: project.sector || options.targetSector,
                source: "trending_projects",
                projectName: project.name,
              };
            })
            .filter(Boolean)
            .slice(0, (options.limit || 10) - discoveredAccounts.length);

          discoveredAccounts.push(...sectorAccounts);
        } catch (error) {
          this.logger.warn("Failed to get trending projects:", error);
        }
      }

      const limitedAccounts = discoveredAccounts.slice(0, options.limit || 10);

      // Enrich with Twitter data
      const enrichedAccounts = await Promise.allSettled(
        limitedAccounts.map(async (account) => {
          try {
            if (!account.followerCount || !account.displayName) {
              const twitterInfo = await twitterClient.getUserInfo(
                account.handle
              );
              return {
                ...account,
                displayName: account.displayName || twitterInfo.name,
                avatarUrl: account.avatarUrl || twitterInfo.profilePicture,
                followerCount: account.followerCount || twitterInfo.followers,
                verified: twitterInfo.isVerified,
              };
            }
            return account;
          } catch (error) {
            this.logger.warn(
              `Failed to enrich account @${account.handle}:`,
              error
            );
            return account;
          }
        })
      );

      const successfulAccounts = enrichedAccounts
        .filter(
          (result): result is PromiseFulfilledResult<any> =>
            result.status === "fulfilled"
        )
        .map((result) => result.value)
        .sort((a, b) => {
          const scoreA = a.smartScore || a.influence || 0;
          const scoreB = b.smartScore || b.influence || 0;
          if (scoreA !== scoreB) return scoreB - scoreA;
          return (b.followerCount || 0) - (a.followerCount || 0);
        });

      // Record usage
      await recordUsage(userId, "COOKIE_API_CALLS" as FeatureType, 2, {
        feature: "smart_account_discovery",
        sector: options.targetSector,
        seedAccount: options.seedAccount,
        resultCount: successfulAccounts.length,
        timestamp: new Date().toISOString(),
      });

      return this.success({
        accounts: successfulAccounts,
        totalFound: successfulAccounts.length,
        strategies: {
          smartFollowers: !!options.seedAccount,
          sectorTrending: !!options.targetSector,
        },
      });
    } catch (error) {
      return this.handleError(error, "discoverSmartAccounts");
    }
  }

  /**
   * Get sector recommendations
   */
  async getSectorRecommendations(
    userId: string,
    sector: string,
    limit: number = 5
  ): Promise<ServiceResult> {
    try {
      this.logger.info(`Getting sector recommendations for: ${sector}`);

      // Check rate limits
      const rateLimit = await checkRateLimit(
        userId,
        "COOKIE_API_CALLS" as FeatureType,
        1
      );
      if (!rateLimit.allowed) {
        return this.failure(
          `Cookie.fun API calls limit exceeded. ${rateLimit.remaining} remaining this month.`
        );
      }

      const trendingProjects = await cookieClient.getTrendingProjects(
        sector,
        "_7Days"
      );

      const recommendations = trendingProjects
        .slice(0, limit)
        .map((project) => ({
          projectName: project.name,
          projectSlug: project.slug,
          symbol: project.symbol,
          description: project.description,
          sector: project.sector,
          mindshare: project.mindshare,
          twitterHandle: project.twitterUrl
            ? twitterClient.extractUsername(project.twitterUrl)
            : null,
          websiteUrl: project.websiteUrl,
          reasoning: `Trending in ${project.sector} with ${project.mindshare || 0} mindshare points`,
        }))
        .filter((rec) => rec.twitterHandle);

      // Record usage
      await recordUsage(userId, "COOKIE_API_CALLS" as FeatureType, 1, {
        feature: "sector_recommendations",
        sector,
        resultCount: recommendations.length,
        timestamp: new Date().toISOString(),
      });

      return this.success({
        sector,
        recommendations,
        totalFound: recommendations.length,
        message:
          recommendations.length > 0
            ? `Found ${recommendations.length} trending projects in ${sector} sector`
            : `No trending projects found in ${sector} sector`,
      });
    } catch (error) {
      return this.handleError(error, "getSectorRecommendations");
    }
  }

  /**
   * Validate account ownership
   */
  async validateAccountOwnership(
    userId: string,
    accountId: string
  ): Promise<boolean> {
    try {
      if (!this.validateUserId(userId) || !this.validateId(accountId)) {
        return false;
      }

      const account = await this.prisma.monitoredAccount.findFirst({
        where: { id: accountId, userId },
      });

      return !!account;
    } catch (error) {
      this.logger.error("Account ownership validation failed:", error as Error);
      return false;
    }
  }

  /**
   * Get Twitter API error message
   */
  private getTwitterApiErrorMessage(error: any, handle: string): string {
    if (error?.response?.status === 404) {
      return `Twitter account @${handle} not found. Please check the handle is correct.`;
    }
    if (error?.response?.status === 403) {
      return `Twitter account @${handle} is private or suspended. We can only monitor public accounts.`;
    }
    if (error?.response?.status === 429) {
      return `Rate limit reached when fetching @${handle}. Please try again in a few minutes.`;
    }
    if (error?.response?.status === 401) {
      return `Twitter API authentication failed. Please contact support if this persists.`;
    }
    if (error?.message?.includes("timeout")) {
      return `Request timed out when fetching @${handle}. Twitter may be experiencing issues, please try again.`;
    }
    if (error?.message?.includes("network")) {
      return `Network error when fetching @${handle}. Please check your connection and try again.`;
    }
    return `Twitter account @${handle} could not be verified. It may be private, suspended, or the handle may be incorrect.`;
  }
}

/**
 * Service Factory
 *
 * Provides dependency injection for service classes, ensuring proper
 * initialization and singleton patterns where appropriate.
 */

import type { PrismaClient } from "../../prisma/generated";
import type { ExtendedPrismaClient } from "../lib/prisma-config";
import { AccountService } from "./account.service";
import { BenjiService } from "./benji.service";
import { CryptoService } from "./crypto.service";
import type {
  IAccountService,
  IBenjiService,
  ICryptoService,
  ILogger,
  IMentionService,
  IServiceFactory,
  ISubscriptionService,
  ITwitterService,
  IUserService,
} from "./interfaces";
import { createLogger } from "./logger.service";
import { MentionService } from "./mention.service";
import { SubscriptionService } from "./subscription.service";
import { TwitterService } from "./twitter.service";
import { UserService } from "./user.service";

export class ServiceFactory implements IServiceFactory {
  private readonly prisma: PrismaClient | ExtendedPrismaClient;
  private readonly logger: ILogger;

  // Service singletons
  private accountService?: IAccountService;
  private userService?: IUserService;
  private cryptoService?: ICryptoService;
  private mentionService?: IMentionService;
  private twitterService?: ITwitterService;
  private benjiService?: IBenjiService;
  private subscriptionService?: ISubscriptionService;

  constructor(prisma: PrismaClient | ExtendedPrismaClient, logger?: ILogger) {
    this.prisma = prisma;
    this.logger = logger || createLogger("ServiceFactory");
  }

  /**
   * Create or get account service instance
   */
  createAccountService(): IAccountService {
    if (!this.accountService) {
      this.accountService = new AccountService(
        this.prisma as PrismaClient,
        createLogger("AccountService")
      );
    }
    return this.accountService as IAccountService;
  }

  /**
   * Create or get user service instance
   */
  createUserService(): IUserService {
    if (!this.userService) {
      this.userService = new UserService(
        this.prisma as PrismaClient,
        createLogger("UserService")
      );
    }
    return this.userService as IUserService;
  }

  /**
   * Create or get crypto service instance
   */
  createCryptoService(): ICryptoService {
    if (!this.cryptoService) {
      this.cryptoService = new CryptoService(
        this.prisma as PrismaClient,
        createLogger("CryptoService")
      );
    }
    return this.cryptoService as ICryptoService;
  }

  /**
   * Create or get mention service instance
   */
  createMentionService(): IMentionService {
    if (!this.mentionService) {
      this.mentionService = new MentionService(
        this.prisma as PrismaClient,
        createLogger("MentionService")
      );
    }
    return this.mentionService as IMentionService;
  }

  /**
   * Create or get Twitter service instance
   */
  createTwitterService(): ITwitterService {
    if (!this.twitterService) {
      this.twitterService = new TwitterService(
        this.prisma as PrismaClient,
        createLogger("TwitterService")
      );
    }
    return this.twitterService as ITwitterService;
  }

  /**
   * Create or get Benji service instance
   */
  createBenjiService(): IBenjiService {
    if (!this.benjiService) {
      this.benjiService = new BenjiService(
        this.prisma as PrismaClient,
        createLogger("BenjiService")
      );
    }
    return this.benjiService as IBenjiService;
  }

  /**
   * Create or get subscription service instance
   */
  createSubscriptionService(): ISubscriptionService {
    if (!this.subscriptionService) {
      this.subscriptionService = new SubscriptionService(
        this.prisma as PrismaClient,
        createLogger("SubscriptionService")
      );
    }
    return this.subscriptionService as ISubscriptionService;
  }

  /**
   * Reset all service instances (useful for testing)
   */
  resetServices(): void {
    this.accountService = undefined;
    this.userService = undefined;
    this.cryptoService = undefined;
    this.mentionService = undefined;
    this.twitterService = undefined;
    this.benjiService = undefined;
    this.subscriptionService = undefined;
  }

  /**
   * Get all service instances (for debugging)
   */
  getAllServices(): {
    account?: IAccountService;
    user?: IUserService;
    crypto?: ICryptoService;
    mention?: IMentionService;
    twitter?: ITwitterService;
    benji?: IBenjiService;
    subscription?: ISubscriptionService;
  } {
    return {
      account: this.accountService,
      user: this.userService,
      crypto: this.cryptoService,
      mention: this.mentionService,
      twitter: this.twitterService,
      benji: this.benjiService,
      subscription: this.subscriptionService,
    };
  }
}

// Default factory instance
let defaultFactory: ServiceFactory | null = null;

/**
 * Get default service factory instance
 */
export function getServiceFactory(prisma?: PrismaClient | ExtendedPrismaClient): ServiceFactory {
  if (!defaultFactory) {
    if (!prisma) {
      throw new Error("PrismaClient is required to initialize ServiceFactory");
    }
    defaultFactory = new ServiceFactory(prisma);
  }
  return defaultFactory;
}

/**
 * Reset default factory (useful for testing)
 */
export function resetServiceFactory(): void {
  if (defaultFactory) {
    defaultFactory.resetServices();
    defaultFactory = null;
  }
}

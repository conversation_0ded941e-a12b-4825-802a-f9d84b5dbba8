/**
 * Twitter Service
 *
 * Handles all Twitter-related operations including:
 * - Tweet fetching and validation
 * - URL parsing and normalization
 * - Account information retrieval
 * - Twitter API integration
 */

import type { TwitterTweet } from "../lib/twitter-client";
import { twitterClient } from "../lib/twitter-client";
import { BaseService } from "./base.service";
import type { ServiceResult } from "./interfaces";

export interface TwitterUserInfo {
  id: string;
  username: string;
  displayName: string;
  avatarUrl?: string;
  isVerified: boolean;
  followers: number;
  following: number;
  description?: string;
}

export interface TwitterTweetData {
  id: string;
  text: string;
  author: TwitterUserInfo;
  createdAt: string;
  url?: string;
  replyCount: number;
  retweetCount: number;
  likeCount: number;
  isReply: boolean;
  parentTweetId?: string;
  isUserTweet: boolean;
}

export interface TwitterFetchOptions {
  limit?: number;
  includeReplies?: boolean;
  includeRetweets?: boolean;
  cursor?: string;
}

export interface TwitterMentionOptions {
  limit?: number;
  sinceId?: string;
  maxId?: string;
}

export class TwitterService extends BaseService {
  /**
   * Validate if a URL is a valid Twitter/X URL
   */
  validateTwitterUrl(url: string): boolean {
    try {
      this.logger.debug("Validating Twitter URL", { url });
      return twitterClient.validateTwitterUrl(url);
    } catch (error) {
      this.logger.error("Error validating Twitter URL", error as Error, {
        url,
      });
      return false;
    }
  }

  /**
   * Extract tweet ID from Twitter URL
   */
  extractTweetId(url: string): string | null {
    try {
      this.logger.debug("Extracting tweet ID from URL", { url });
      return twitterClient.extractTweetId(url);
    } catch (error) {
      this.logger.error("Error extracting tweet ID", error as Error, { url });
      return null;
    }
  }

  /**
   * Normalize Twitter URL to standard format
   */
  normalizeTwitterUrl(url: string): string {
    try {
      this.logger.debug("Normalizing Twitter URL", { url });
      return twitterClient.normalizeTwitterUrl(url);
    } catch (error) {
      this.logger.error("Error normalizing Twitter URL", error as Error, {
        url,
      });
      return url; // Return original URL if normalization fails
    }
  }

  /**
   * Get tweet data from Twitter URL
   */
  async getTweetFromUrl(url: string): Promise<ServiceResult<TwitterTweetData>> {
    try {
      this.logger.info("Getting tweet from URL", { url });

      if (!this.validateTwitterUrl(url)) {
        return {
          success: false,
          error: "Invalid Twitter URL",
        };
      }

      const tweet = await twitterClient.getTweetFromUrl(url);
      if (!tweet) {
        return {
          success: false,
          error: "Tweet not found or inaccessible",
        };
      }

      return {
        success: true,
        data: this.transformTwitterTweet(tweet),
      };
    } catch (error) {
      this.logger.error("Error getting tweet from URL", error as Error, {
        url,
      });
      return {
        success: false,
        error: "Failed to fetch tweet",
      };
    }
  }

  /**
   * Get user information by username
   */
  async getUserInfo(username: string): Promise<ServiceResult<TwitterUserInfo>> {
    try {
      this.logger.info("Getting user info", { username });

      // Remove @ symbol if present
      const cleanUsername = username.replace(/^@/, "");

      const userInfo = await twitterClient.getUserInfo(cleanUsername);
      if (!userInfo) {
        return {
          success: false,
          error: "User not found",
        };
      }

      return {
        success: true,
        data: {
          id: userInfo.id,
          username: userInfo.userName,
          displayName: userInfo.name,
          avatarUrl: userInfo.profilePicture ?? undefined,
          isVerified: userInfo.isBlueVerified || false,
          followers: userInfo.followers || 0,
          following: userInfo.following || 0,
          description: userInfo.description,
        },
      };
    } catch (error) {
      this.logger.error("Error getting user info", error as Error, {
        username,
      });
      return {
        success: false,
        error: "Failed to fetch user information",
      };
    }
  }

  /**
   * Get mentions for a user
   */
  async getUserMentions(
    username: string,
    options: TwitterMentionOptions = {}
  ): Promise<
    ServiceResult<{
      tweets: TwitterTweetData[];
      nextCursor?: string;
      hasNextPage: boolean;
    }>
  > {
    try {
      this.logger.info("Getting user mentions", { username, options });

      const cleanUsername = username.replace(/^@/, "");
      const response = await twitterClient.getUserMentions(cleanUsername, {
        limit: options.limit || 20,
      });

      if (!response.tweets) {
        return {
          success: true,
          data: {
            tweets: [],
            hasNextPage: false,
          },
        };
      }

      return {
        success: true,
        data: {
          tweets: response.tweets.map((tweet) =>
            this.transformTwitterTweet(tweet, false)
          ),
          nextCursor: response.next_cursor,
          hasNextPage: response.has_next_page || false,
        },
      };
    } catch (error) {
      this.logger.error("Error getting user mentions", error as Error, {
        username,
        options,
      });
      return {
        success: false,
        error: "Failed to fetch mentions",
      };
    }
  }

  /**
   * Get user's recent tweets
   */
  async getUserTweets(
    username: string,
    options: TwitterFetchOptions = {}
  ): Promise<
    ServiceResult<{
      tweets: TwitterTweetData[];
      nextCursor?: string;
      hasNextPage: boolean;
    }>
  > {
    try {
      this.logger.info("Getting user tweets", { username, options });

      const cleanUsername = username.replace(/^@/, "");
      const response = await twitterClient.getUserLastTweets(cleanUsername, {
        limit: options.limit || 20,
        includeReplies: options.includeReplies ?? false,
        includeRetweets: options.includeRetweets ?? true,
        cursor: options.cursor,
      });

      if (!response.tweets) {
        return {
          success: true,
          data: {
            tweets: [],
            hasNextPage: false,
          },
        };
      }

      return {
        success: true,
        data: {
          tweets: response.tweets.map((tweet) =>
            this.transformTwitterTweet(tweet, true)
          ),
          nextCursor: response.next_cursor,
          hasNextPage: response.has_next_page || false,
        },
      };
    } catch (error) {
      this.logger.error("Error getting user tweets", error as Error, {
        username,
        options,
      });
      return {
        success: false,
        error: "Failed to fetch user tweets",
      };
    }
  }

  /**
   * Validate Twitter handle format
   */
  validateHandle(handle: string): boolean {
    try {
      this.logger.debug("Validating Twitter handle", { handle });

      // Remove @ symbol if present
      const cleanHandle = handle.replace(/^@/, "");

      // Twitter handle rules: 1-15 characters, alphanumeric and underscores only
      const handleRegex = /^[a-zA-Z0-9_]{1,15}$/;
      return handleRegex.test(cleanHandle);
    } catch (error) {
      this.logger.error("Error validating handle", error as Error, { handle });
      return false;
    }
  }

  /**
   * Extract username from Twitter URL
   */
  extractUsername(url: string): string | null {
    try {
      this.logger.debug("Extracting username from URL", { url });

      // Match Twitter/X.com URLs and extract username
      const patterns = [
        /(?:https?:\/\/)?(?:www\.)?twitter\.com\/([a-zA-Z0-9_]+)/,
        /(?:https?:\/\/)?(?:www\.)?x\.com\/([a-zA-Z0-9_]+)/,
      ];

      for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match && match[1]) {
          return match[1];
        }
      }

      return null;
    } catch (error) {
      this.logger.error("Error extracting username", error as Error, { url });
      return null;
    }
  }

  /**
   * Check if Twitter API is available
   */
  async checkTwitterApiHealth(): Promise<
    ServiceResult<{ healthy: boolean; message: string }>
  > {
    try {
      this.logger.info("Checking Twitter API health");

      // Try to get a known public account to test API connectivity
      const testResult = await this.getUserInfo("twitter");

      if (testResult.success) {
        return {
          success: true,
          data: {
            healthy: true,
            message: "Twitter API is functioning correctly",
          },
        };
      } else {
        return {
          success: false,
          data: {
            healthy: false,
            message: "Twitter API is not responding correctly",
          },
        };
      }
    } catch (error) {
      this.logger.error("Error checking Twitter API health", error as Error);
      return {
        success: false,
        data: {
          healthy: false,
          message: "Unable to connect to Twitter API",
        },
      };
    }
  }

  /**
   * Get trending topics (if supported by Twitter client)
   */
  async getTrendingTopics(): Promise<ServiceResult<string[]>> {
    try {
      this.logger.info("Getting trending topics");

      // This would need to be implemented in the Twitter client
      // For now, return empty array
      return {
        success: true,
        data: [],
        message: "Trending topics not yet implemented",
      };
    } catch (error) {
      this.logger.error("Error getting trending topics", error as Error);
      return {
        success: false,
        error: "Failed to fetch trending topics",
      };
    }
  }

  /**
   * Transform Twitter API tweet data to our format
   */
  private transformTwitterTweet(
    tweet: TwitterTweet,
    isUserTweet: boolean = false
  ): TwitterTweetData {
    return {
      id: tweet.id,
      text: tweet.text,
      author: {
        id: tweet.author.id,
        username: tweet.author.userName,
        displayName: tweet.author.name,
        avatarUrl: tweet.author.profilePicture ?? undefined,
        isVerified: tweet.author.isBlueVerified || false,
        followers: tweet.author.followers || 0,
        following: tweet.author.following || 0,
        description: tweet.author.description,
      },
      createdAt: tweet.createdAt,
      url:
        tweet.url ||
        `https://x.com/${tweet.author.userName}/status/${tweet.id}`,
      replyCount: tweet.replyCount || 0,
      retweetCount: tweet.retweetCount || 0,
      likeCount: tweet.likeCount || 0,
      isReply: tweet.isReply || Boolean(tweet.inReplyToUserId),
      parentTweetId: tweet.inReplyToUserId,
      isUserTweet,
    };
  }
}

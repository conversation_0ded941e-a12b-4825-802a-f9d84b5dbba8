/**
 * Mention Service
 *
 * Handles all mention-related business logic including:
 * - Fetching and transforming mentions
 * - Sentiment analysis and scoring
 * - Mention management operations
 * - Performance optimization
 */

import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { FeatureType } from "../../prisma/generated";
import { checkRateLimit, recordUsage } from "../lib/db-utils";
import { performanceMonitor } from "../lib/performance-monitor";
import { twitterClient } from "../lib/twitter-client";
import { BaseService } from "./base.service";
import type { PaginationOptions, ServiceResult } from "./interfaces";

export interface MentionData {
  id: string;
  content: string;
  authorHandle: string;
  authorName: string;
  authorAvatar: string | null;
  tweetUrl: string;
  platform: string;
  createdAt: Date | string;
  bullishScore: number | null;
  importanceScore: number | null;
  keywords: string[];
  hasAIResponse: boolean;
  account?: {
    id: string;
    handle: string;
    displayName: string;
    avatar: string | null;
    isActive: boolean;
  } | null;
  responses?: Array<{
    id: string;
    content: string;
    model: string | null;
    confidence: number | null;
    tokensUsed: number | null;
    createdAt: Date | string;
  }>;
}

export interface MentionFilters {
  accountId?: string;
  hasAIResponse?: boolean;
  archived?: boolean;
  startDate?: Date;
  endDate?: Date;
  tweetTypes?: {
    mentions: boolean;
    userTweets: boolean;
    replies: boolean;
    retweets: boolean;
  };
}

export interface MentionQueryOptions extends PaginationOptions {
  includeResponses?: boolean;
  includeAccount?: boolean;
  filters?: MentionFilters;
  sortBy?: "createdAt" | "bullishScore" | "importanceScore";
  sortOrder?: "asc" | "desc";
}

export interface BulkOperationResult {
  success: boolean;
  processed: number;
  errors?: string[];
  message: string;
}

export class MentionService extends BaseService {
  /**
   * Get mentions with advanced filtering and pagination
   */
  async getMentions(
    userId: string,
    options: MentionQueryOptions = {}
  ): Promise<
    ServiceResult<{
      mentions: MentionData[];
      nextCursor?: string;
      hasNextPage: boolean;
      meta: {
        totalItems: number;
        includeResponses: boolean;
        includeAccount: boolean;
      };
    }>
  > {
    try {
      this.logger.info("Getting mentions for user", { userId, options });

      const {
        limit = 20,
        offset,
        includeResponses = false,
        includeAccount = true,
        filters = {},
        sortBy = "createdAt",
        sortOrder = "desc",
      } = options;

      // Build where clause
      const where: any = {
        userId,
        archived: filters.archived ?? false,
      };

      // Apply filters
      if (filters.accountId) {
        where.accountId = filters.accountId;
      }
      if (filters.hasAIResponse !== undefined) {
        where.processed = filters.hasAIResponse;
      }
      if (filters.startDate || filters.endDate) {
        where.mentionedAt = {};
        if (filters.startDate) where.mentionedAt.gte = filters.startDate;
        if (filters.endDate) where.mentionedAt.lte = filters.endDate;
      }

      // Handle tweet type filters
      const typeConditions = [];
      if (filters.tweetTypes) {
        const types = filters.tweetTypes;

        if (types.mentions) {
          typeConditions.push({
            accountId: filters.accountId,
            isUserTweet: false,
          });
        }

        if (types.userTweets) {
          const userTweetCondition: any = {
            isUserTweet: true,
          };

          if (!types.replies) {
            userTweetCondition.isReply = false;
          }

          typeConditions.push(userTweetCondition);
        }
      }

      // Handle pagination
      if (offset) {
        try {
          const [timestamp, id] = offset.split("_");
          if (timestamp && id) {
            const paginationConditions = [
              { createdAt: { lt: new Date(timestamp) } },
              { createdAt: new Date(timestamp), id: { lt: id } },
            ];

            // Combine type and pagination conditions
            if (typeConditions.length > 0) {
              where.OR = typeConditions.map((typeCondition) => ({
                ...typeCondition,
                OR: paginationConditions,
              }));
            } else {
              where.OR = paginationConditions;
            }
          } else {
            where.id = { lt: offset };
            if (typeConditions.length > 0) {
              where.OR = typeConditions;
            }
          }
        } catch (cursorError) {
          this.logger.warn("Invalid cursor format, using ID-based pagination", {
            cursorError,
          });
          where.id = { lt: offset };
          if (typeConditions.length > 0) {
            where.OR = typeConditions;
          }
        }
      } else if (typeConditions.length > 0) {
        where.OR = typeConditions;
      }

      // Execute query with performance monitoring
      const mentions = await performanceMonitor.trackQuery(
        "mentions.getMentions",
        () =>
          this.prisma.mention.findMany({
            where,
            orderBy: { [sortBy]: sortOrder },
            take: limit + 1,
            include: {
              ...(includeAccount && {
                account: {
                  select: {
                    id: true,
                    twitterHandle: true,
                    displayName: true,
                    avatarUrl: true,
                    isActive: true,
                  },
                },
              }),
              ...(includeResponses && {
                responses: {
                  orderBy: { createdAt: "desc" },
                  take: 3,
                  select: {
                    id: true,
                    content: true,
                    model: true,
                    confidence: true,
                    tokensUsed: true,
                    createdAt: true,
                  },
                },
              }),
            },
          }),
        {
          limit,
          includeResponses,
          includeAccount,
          filters,
        },
        userId
      );

      // Process results
      const hasNextPage = mentions.length > limit;
      const items = hasNextPage ? mentions.slice(0, -1) : mentions;
      const nextCursor = hasNextPage
        ? `${items[items.length - 1].createdAt.toISOString()}_${items[items.length - 1].id}`
        : undefined;

      return {
        success: true,
        data: {
          mentions: items.map((mention) => this.transformMentionData(mention)),
          nextCursor,
          hasNextPage,
          meta: {
            totalItems: items.length,
            includeResponses,
            includeAccount,
          },
        },
      };
    } catch (error) {
      this.logger.error("Error getting mentions", error as Error, {
        userId,
        options,
      });
      return {
        success: false,
        error: "Failed to fetch mentions",
      };
    }
  }

  /**
   * Get a single mention by ID
   */
  async getMentionById(
    userId: string,
    mentionId: string
  ): Promise<ServiceResult<MentionData>> {
    try {
      this.logger.info("Getting mention by ID", { userId, mentionId });

      const mention = await this.prisma.mention.findFirst({
        where: {
          id: mentionId,
          userId,
        },
        include: {
          account: {
            select: {
              id: true,
              twitterHandle: true,
              displayName: true,
              avatarUrl: true,
              isActive: true,
            },
          },
          responses: {
            orderBy: { createdAt: "desc" },
          },
        },
      });

      if (!mention) {
        return {
          success: false,
          error: "Mention not found",
        };
      }

      return {
        success: true,
        data: this.transformMentionData(mention),
      };
    } catch (error) {
      this.logger.error("Error getting mention by ID", error as Error, {
        userId,
        mentionId,
      });
      return {
        success: false,
        error: "Failed to fetch mention",
      };
    }
  }

  /**
   * Create mention from Twitter URL
   */
  async createMentionFromUrl(
    userId: string,
    url: string
  ): Promise<ServiceResult<{ mention: MentionData; isNew: boolean }>> {
    try {
      this.logger.info("Creating mention from URL", { userId, url });

      // Validate Twitter URL
      if (!twitterClient.validateTwitterUrl(url)) {
        return {
          success: false,
          error: "Please provide a valid Twitter or X.com URL",
        };
      }

      // Extract tweet ID
      const tweetId = twitterClient.extractTweetId(url);
      if (!tweetId) {
        return {
          success: false,
          error: "Could not extract tweet ID from URL",
        };
      }

      // Check if mention already exists
      const existingMention = await this.prisma.mention.findFirst({
        where: {
          id: tweetId,
          userId,
        },
      });

      if (existingMention) {
        return {
          success: true,
          data: {
            mention: this.transformMentionData(existingMention),
            isNew: false,
          },
          message: "This tweet is already in your mentions",
        };
      }

      // Fetch tweet content
      const tweet = await twitterClient.getTweetFromUrl(url);
      if (!tweet) {
        return {
          success: false,
          error:
            "Could not fetch tweet content. The tweet may be private, deleted, or the URL is invalid.",
        };
      }

      // Create mention
      const mention = await this.prisma.mention.create({
        data: {
          id: tweet.id,
          userId,
          link: twitterClient.normalizeTwitterUrl(url),
          content: tweet.text,
          authorHandle: tweet.author.userName,
          authorName: tweet.author.name,
          authorAvatarUrl: tweet.author.profilePicture,
          mentionedAt: new Date(tweet.createdAt),
          processed: false,
        },
      });

      return {
        success: true,
        data: {
          mention: this.transformMentionData(mention),
          isNew: true,
        },
        message: "Tweet added to your mentions",
      };
    } catch (error) {
      this.logger.error("Error creating mention from URL", error as Error, {
        userId,
        url,
      });
      return {
        success: false,
        error: "Failed to create mention from URL",
      };
    }
  }

  /**
   * Update mention bullish score
   */
  async updateBullishScore(
    userId: string,
    mentionId: string,
    bullishScore: number
  ): Promise<ServiceResult<MentionData>> {
    try {
      this.logger.info("Updating bullish score", {
        userId,
        mentionId,
        bullishScore,
      });

      const mention = await this.prisma.mention.update({
        where: {
          id: mentionId,
          userId,
        },
        data: {
          bullishScore,
        },
      });

      return {
        success: true,
        data: this.transformMentionData(mention),
        message: "Bullish score updated successfully",
      };
    } catch (error) {
      this.logger.error("Error updating bullish score", error as Error, {
        userId,
        mentionId,
        bullishScore,
      });
      return {
        success: false,
        error: "Failed to update bullish score",
      };
    }
  }

  /**
   * Update mention importance score
   */
  async updateImportanceScore(
    userId: string,
    mentionId: string,
    importanceScore: number
  ): Promise<ServiceResult<MentionData>> {
    try {
      this.logger.info("Updating importance score", {
        userId,
        mentionId,
        importanceScore,
      });

      const mention = await this.prisma.mention.update({
        where: {
          id: mentionId,
          userId,
        },
        data: {
          importanceScore,
        },
      });

      return {
        success: true,
        data: this.transformMentionData(mention),
        message: "Importance score updated successfully",
      };
    } catch (error) {
      this.logger.error("Error updating importance score", error as Error, {
        userId,
        mentionId,
        importanceScore,
      });
      return {
        success: false,
        error: "Failed to update importance score",
      };
    }
  }

  /**
   * Archive mention
   */
  async archiveMention(
    userId: string,
    mentionId: string
  ): Promise<ServiceResult<{ archived: boolean; archivedAt: Date }>> {
    try {
      this.logger.info("Archiving mention", { userId, mentionId });

      const mention = await this.prisma.mention.update({
        where: {
          id: mentionId,
          userId,
        },
        data: {
          archived: true,
          archivedAt: new Date(),
        },
      });

      return {
        success: true,
        data: {
          archived: mention.archived,
          archivedAt: mention.archivedAt!,
        },
        message: "Mention archived successfully",
      };
    } catch (error) {
      this.logger.error("Error archiving mention", error as Error, {
        userId,
        mentionId,
      });
      return {
        success: false,
        error: "Failed to archive mention",
      };
    }
  }

  /**
   * Unarchive mention
   */
  async unarchiveMention(
    userId: string,
    mentionId: string
  ): Promise<ServiceResult<{ archived: boolean; archivedAt: Date | null }>> {
    try {
      this.logger.info("Unarchiving mention", { userId, mentionId });

      const mention = await this.prisma.mention.update({
        where: {
          id: mentionId,
          userId,
        },
        data: {
          archived: false,
          archivedAt: null,
        },
      });

      return {
        success: true,
        data: {
          archived: mention.archived,
          archivedAt: mention.archivedAt,
        },
        message: "Mention unarchived successfully",
      };
    } catch (error) {
      this.logger.error("Error unarchiving mention", error as Error, {
        userId,
        mentionId,
      });
      return {
        success: false,
        error: "Failed to unarchive mention",
      };
    }
  }

  /**
   * Bulk archive mentions
   */
  async bulkArchiveMentions(
    userId: string,
    mentionIds: string[]
  ): Promise<ServiceResult<BulkOperationResult>> {
    try {
      this.logger.info("Bulk archiving mentions", {
        userId,
        count: mentionIds.length,
      });

      // Verify all mentions belong to the user
      const mentions = await this.prisma.mention.findMany({
        where: {
          id: { in: mentionIds },
          userId,
        },
        select: { id: true },
      });

      if (mentions.length !== mentionIds.length) {
        return {
          success: false,
          error: "Some mentions not found or access denied",
        };
      }

      // Archive all mentions
      const result = await this.prisma.mention.updateMany({
        where: {
          id: { in: mentionIds },
          userId,
        },
        data: {
          archived: true,
          archivedAt: new Date(),
        },
      });

      return {
        success: true,
        data: {
          success: true,
          processed: result.count,
          message: `${result.count} mentions archived successfully`,
        },
      };
    } catch (error) {
      this.logger.error("Error bulk archiving mentions", error as Error, {
        userId,
        count: mentionIds.length,
      });
      return {
        success: false,
        error: "Failed to archive mentions",
      };
    }
  }

  /**
   * Delete mention
   */
  async deleteMention(
    userId: string,
    mentionId: string
  ): Promise<ServiceResult<{ deleted: boolean }>> {
    try {
      this.logger.info("Deleting mention", { userId, mentionId });

      // Verify mention exists and belongs to user
      const mention = await this.prisma.mention.findFirst({
        where: {
          id: mentionId,
          userId,
        },
      });

      if (!mention) {
        return {
          success: false,
          error: "Mention not found",
        };
      }

      // Delete mention and related responses
      await this.prisma.mention.delete({
        where: {
          id: mentionId,
        },
      });

      return {
        success: true,
        data: { deleted: true },
        message: "Mention deleted successfully",
      };
    } catch (error) {
      this.logger.error("Error deleting mention", error as Error, {
        userId,
        mentionId,
      });
      return {
        success: false,
        error: "Failed to delete mention",
      };
    }
  }

  /**
   * Mark mention as replied
   */
  async markMentionAsReplied(
    userId: string,
    mentionId: string
  ): Promise<ServiceResult<{ processed: boolean }>> {
    try {
      this.logger.info("Marking mention as replied", { userId, mentionId });

      await this.prisma.mention.update({
        where: {
          id: mentionId,
          userId,
        },
        data: {
          processed: true,
        },
      });

      return {
        success: true,
        data: { processed: true },
        message: "Mention marked as replied",
      };
    } catch (error) {
      this.logger.error("Error marking mention as replied", error as Error, {
        userId,
        mentionId,
      });
      return {
        success: false,
        error: "Failed to mark mention as replied",
      };
    }
  }

  /**
   * Calculate bullish score for content
   */
  calculateBullishScore(content: string): number {
    const lowerContent = content.toLowerCase();

    // Positive indicators
    const positiveWords = [
      "great",
      "awesome",
      "amazing",
      "excellent",
      "fantastic",
      "love",
      "best",
      "good",
      "nice",
      "wonderful",
      "perfect",
      "brilliant",
      "outstanding",
      "bullish",
      "moon",
      "pump",
      "rocket",
      "diamond",
      "hodl",
      "buy",
      "invest",
      "growth",
      "profit",
    ];

    const negativeWords = [
      "bad",
      "terrible",
      "awful",
      "hate",
      "worst",
      "horrible",
      "disappointing",
      "useless",
      "broken",
      "poor",
      "failed",
      "dump",
      "crash",
      "bear",
      "sell",
      "scam",
      "rug",
      "loss",
      "decline",
      "drop",
    ];

    let score = 50; // Neutral starting point

    // Check for positive words
    positiveWords.forEach((word) => {
      if (lowerContent.includes(word)) {
        score += 10;
      }
    });

    // Check for negative words
    negativeWords.forEach((word) => {
      if (lowerContent.includes(word)) {
        score -= 15;
      }
    });

    // Check for exclamation marks (usually positive energy)
    const exclamationCount = (content.match(/!/g) || []).length;
    score += Math.min(exclamationCount * 5, 20);

    // Check for question marks (usually neutral to slightly negative)
    const questionCount = (content.match(/\?/g) || []).length;
    score -= Math.min(questionCount * 2, 10);

    // Ensure score is within bounds
    return Math.max(0, Math.min(100, Math.round(score)));
  }

  /**
   * Extract keywords from content
   */
  extractKeywords(content: string): string[] {
    // Remove URLs, mentions, hashtags for cleaner analysis
    const cleanContent = content
      .replace(/https?:\/\/[^\s]+/g, "") // Remove URLs
      .replace(/@\w+/g, "") // Remove mentions
      .replace(/#\w+/g, "") // Remove hashtags
      .toLowerCase();

    // Simple keyword extraction
    const words = cleanContent
      .split(/\s+/)
      .filter(
        (word) =>
          word.length > 3 &&
          ![
            "this",
            "that",
            "with",
            "from",
            "they",
            "were",
            "been",
            "have",
            "will",
            "would",
            "could",
            "should",
          ].includes(word)
      );

    // Get most frequent words (max 5)
    const wordCounts = words.reduce(
      (acc, word) => {
        acc[word] = (acc[word] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

    return Object.entries(wordCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([word]) => word);
  }

  /**
   * Transform mention data for API response
   */
  private transformMentionData(mention: any): MentionData {
    return {
      id: mention.id,
      content: mention.content,
      authorHandle: mention.authorHandle,
      authorName: mention.authorName,
      authorAvatar: mention.authorAvatarUrl,
      tweetUrl: mention.link,
      platform: "twitter",
      createdAt: mention.createdAt,
      bullishScore: mention.bullishScore,
      importanceScore: mention.importanceScore,
      keywords: mention.keywords || [],
      hasAIResponse: mention.processed,
      account: mention.account
        ? {
            id: mention.account.id,
            handle: mention.account.twitterHandle,
            displayName: mention.account.displayName,
            avatar: mention.account.avatarUrl,
            isActive: mention.account.isActive,
          }
        : null,
      responses: mention.responses
        ? mention.responses.map((response: any) => ({
            id: response.id,
            content: response.content,
            model: response.model,
            confidence: response.confidence,
            tokensUsed: response.tokensUsed,
            createdAt: response.createdAt,
          }))
        : [],
    };
  }
}

/**
 * Unified tRPC Middleware System
 *
 * Consolidates common middleware patterns used across all routers:
 * - Rate limiting with subscription plan integration
 * - Usage tracking and logging
 * - Error handling and monitoring
 * - Security validation
 * - Performance monitoring
 *
 * This system provides reusable middleware components that eliminate code duplication
 * and ensure consistent rate limiting and error handling across all tRPC routers.
 */

import { TRPCError } from "@trpc/server";
import { z } from "zod";
import type { FeatureType } from "../../prisma/generated/index.js";
import { checkRateLimit, recordUsage } from "./db-utils";
import { performanceMonitor } from "./performance-monitor";
import { logSecurityEvent } from "./security-utils";
import { t } from "./trpc";
import { canUserUseFeature, logUsage } from "./user-service";

/**
 * Rate limiting middleware with subscription plan integration
 */
export const rateLimitMiddleware = (
  feature: FeatureType,
  requestedAmount: number = 1
) =>
  t.middleware(async ({ ctx, next, path }) => {
    if (!ctx.userId) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Authentication required for rate limiting",
      });
    }

    console.log(
      `🚦 Rate limit check: ${feature} for user ${ctx.userId} (${requestedAmount} units)`
    );

    try {
      const rateLimitResult = await checkRateLimit(
        ctx.userId,
        feature,
        requestedAmount
      );

      if (!rateLimitResult.allowed) {
        // Log rate limit violation for security monitoring
        logSecurityEvent({
          type: "RATE_LIMIT_EXCEEDED",
          userId: ctx.userId,
          details: `Rate limit exceeded for ${feature}: ${rateLimitResult.currentUsage}/${rateLimitResult.limit} at ${path}`,
        });

        throw new TRPCError({
          code: "TOO_MANY_REQUESTS",
          message: `Rate limit exceeded for ${feature}. Used ${rateLimitResult.currentUsage}/${rateLimitResult.limit}. Try again later.`,
        });
      }

      console.log(
        `✅ Rate limit passed: ${rateLimitResult.remaining} remaining`
      );

      return next({
        ctx: {
          ...ctx,
          rateLimitInfo: rateLimitResult,
        },
      });
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }

      console.error("Rate limit check failed:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to check rate limits",
      });
    }
  });

/**
 * Usage tracking middleware - records successful operations
 */
export const usageTrackingMiddleware = (
  feature: FeatureType,
  amount: number = 1
) =>
  t.middleware(async ({ ctx, next }) => {
    if (!ctx.userId) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Authentication required for usage tracking",
      });
    }

    // Execute the procedure first
    const result = await next();

    // Only record usage if the operation was successful
    if (result.ok) {
      try {
        await recordUsage(ctx.userId, feature, amount);
        await logUsage(ctx.userId, feature, amount);

        console.log(
          `📊 Usage recorded: ${feature} (${amount} units) for user ${ctx.userId}`
        );
      } catch (error) {
        // Don't fail the request if usage tracking fails, but log it
        console.error("Failed to record usage:", error);
      }
    }

    return result;
  });

/**
 * Feature access middleware - checks if user can use a feature
 */
export const featureAccessMiddleware = (feature: FeatureType) =>
  t.middleware(async ({ ctx, next }) => {
    if (!ctx.userId) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Authentication required for feature access",
      });
    }

    try {
      const canUse = await canUserUseFeature(ctx.userId, feature);

      if (!canUse.allowed) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: `Access denied for feature: ${feature}`,
        });
      }

      return next({
        ctx: {
          ...ctx,
          featureAccess: canUse,
        },
      });
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }

      console.error("Feature access check failed:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to check feature access",
      });
    }
  });

/**
 * Performance monitoring middleware
 */
export const performanceMiddleware = (operationName: string) =>
  t.middleware(async ({ ctx, next, path }) => {
    const startTime = performance.now();

    try {
      const result = await next();
      const duration = performance.now() - startTime;

      // Use trackQuery method which is public
      await performanceMonitor.trackQuery(
        operationName,
        async () => result,
        { path: path || "unknown" },
        ctx.userId || undefined
      );

      console.log(`⚡ ${operationName} completed in ${duration.toFixed(2)}ms`);

      return result;
    } catch (error) {
      const duration = performance.now() - startTime;

      console.error(
        `❌ ${operationName} failed after ${duration.toFixed(2)}ms:`,
        {
          error: error instanceof Error ? error.message : "Unknown error",
          path: path || "unknown",
          userId: ctx.userId || undefined,
        }
      );

      throw error;
    }
  });

/**
 * Security validation middleware
 */
export const securityMiddleware = t.middleware(async ({ ctx, next, path }) => {
  // Log security-relevant operations with simplified details
  if (ctx.userId) {
    console.log(`🔒 API Access: ${ctx.userId} -> ${path}`);
  }

  return next();
});

/**
 * Combined middleware for common patterns - simplified version
 */
export const createFeatureMiddleware = (
  feature: FeatureType,
  options: {
    requestedAmount?: number;
    operationName?: string;
    requireFeatureAccess?: boolean;
  } = {}
) => {
  const { operationName = feature } = options;

  // Return a simplified middleware array without complex chaining
  console.log(`🔧 Creating feature middleware for ${feature}`);

  return [securityMiddleware, performanceMiddleware(operationName)];
};

/**
 * Procedure builders with common middleware patterns
 */
export const createFeatureProcedure = (
  feature: FeatureType,
  options?: {
    requestedAmount?: number;
    operationName?: string;
    requireFeatureAccess?: boolean;
  }
) => {
  const middlewares = createFeatureMiddleware(feature, options);

  return middlewares.reduce(
    (procedure, middleware) => procedure.use(middleware),
    t.procedure.use(({ ctx, next }) => {
      if (!ctx.userId) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "You must be logged in to access this resource",
        });
      }
      return next({
        ctx: {
          ...ctx,
          userId: ctx.userId,
        },
      });
    })
  );
};

/**
 * Lightweight procedure for operations that don't need full feature middleware
 */
export const createMonitoredProcedure = (operationName: string) => {
  return t.procedure
    .use(({ ctx, next }) => {
      if (!ctx.userId) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "You must be logged in to access this resource",
        });
      }
      return next({
        ctx: {
          ...ctx,
          userId: ctx.userId,
        },
      });
    })
    .use(securityMiddleware)
    .use(performanceMiddleware(operationName));
};

/**
 * Error handling utilities for consistent error responses
 */
export const handleTRPCError = (
  error: unknown,
  operation: string,
  context?: Record<string, any>
): never => {
  console.error(`🚨 tRPC Error in ${operation}:`, { error, context });

  if (error instanceof TRPCError) {
    throw error;
  }

  if (error instanceof Error) {
    // Handle specific error types
    if (error.message.includes("rate limit")) {
      throw new TRPCError({
        code: "TOO_MANY_REQUESTS",
        message: error.message,
      });
    }

    if (error.message.includes("not found")) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: error.message,
      });
    }

    if (
      error.message.includes("unauthorized") ||
      error.message.includes("access denied")
    ) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: error.message,
      });
    }

    if (
      error.message.includes("database") ||
      error.message.includes("connection")
    ) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Database connection error. Please try again.",
      });
    }

    if (error.message.includes("timeout")) {
      throw new TRPCError({
        code: "TIMEOUT",
        message: "Request timed out. Please try again.",
      });
    }
  }

  // Generic fallback
  throw new TRPCError({
    code: "INTERNAL_SERVER_ERROR",
    message: `Failed to ${operation}`,
  });
};

/**
 * Conditional rate limiting middleware - only applies rate limiting when condition is met
 * Useful for cached operations where we only want to rate limit API calls
 */
export const createConditionalRateLimitMiddleware = (
  feature: FeatureType,
  requestedAmount: number = 1,
  shouldApplyRateLimit: (ctx: any) => boolean | Promise<boolean>
) =>
  t.middleware(async ({ ctx, next, path }) => {
    if (!ctx.userId) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Authentication required for rate limiting",
      });
    }

    const shouldLimit = await shouldApplyRateLimit(ctx);

    if (shouldLimit) {
      console.log(
        `🚦 Conditional rate limit check: ${feature} for user ${ctx.userId} (${requestedAmount} units)`
      );

      try {
        const rateLimitResult = await checkRateLimit(
          ctx.userId,
          feature,
          requestedAmount
        );

        if (!rateLimitResult.allowed) {
          // Log rate limit violation for security monitoring
          logSecurityEvent({
            type: "RATE_LIMIT_EXCEEDED",
            userId: ctx.userId,
            details: `Conditional rate limit exceeded for ${feature}: ${rateLimitResult.currentUsage}/${rateLimitResult.limit} at ${path}`,
          });

          throw new TRPCError({
            code: "TOO_MANY_REQUESTS",
            message: `Rate limit exceeded for ${feature}. Used ${rateLimitResult.currentUsage}/${rateLimitResult.limit}. Try again later.`,
          });
        }

        console.log(
          `✅ Conditional rate limit passed: ${rateLimitResult.remaining} remaining`
        );
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        console.error("Conditional rate limit check failed:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to check rate limits",
        });
      }
    } else {
      console.log(
        `⏭️ Skipping rate limit check for ${feature} (condition not met)`
      );
    }

    return next({
      ctx: {
        ...ctx,
        conditionalRateLimit: {
          applied: shouldLimit,
          feature,
          requestedAmount,
        },
      },
    });
  });

/**
 * Conditional usage tracking middleware - only records usage when condition is met
 */
export const createConditionalUsageMiddleware = (
  feature: FeatureType,
  amount: number = 1,
  shouldRecordUsage: (ctx: any) => boolean | Promise<boolean>
) =>
  t.middleware(async ({ ctx, next }) => {
    if (!ctx.userId) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Authentication required for usage tracking",
      });
    }

    // Execute the procedure first
    const result = await next();

    // Only record usage if the operation was successful and condition is met
    if (result.ok) {
      const shouldRecord = await shouldRecordUsage(ctx);

      if (shouldRecord) {
        try {
          await recordUsage(ctx.userId, feature, amount);
          await logUsage(ctx.userId, feature, amount);

          console.log(
            `📊 Conditional usage recorded: ${feature} (${amount} units) for user ${ctx.userId}`
          );
        } catch (error) {
          // Don't fail the request if usage tracking fails, but log it
          console.error("Failed to record conditional usage:", error);
        }
      } else {
        console.log(
          `⏭️ Skipping usage recording for ${feature} (condition not met)`
        );
      }
    }

    return result;
  });

/**
 * Cache-aware rate limiting procedure - applies rate limiting only when cache misses occur
 */
export const createCacheAwareFeatureProcedure = (
  feature: FeatureType,
  options?: {
    requestedAmount?: number;
    operationName?: string;
  }
) => {
  const { requestedAmount = 1, operationName = feature } = options || {};

  return t.procedure
    .use(({ ctx, next }) => {
      if (!ctx.userId) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "You must be logged in to access this resource",
        });
      }
      return next({
        ctx: {
          ...ctx,
          userId: ctx.userId,
        },
      });
    })
    .use(securityMiddleware)
    .use(performanceMiddleware(operationName))
    .use(
      createConditionalRateLimitMiddleware(
        feature,
        requestedAmount,
        (ctx) => ctx.cacheMiss === true // Only rate limit on cache misses
      )
    )
    .use(
      createConditionalUsageMiddleware(
        feature,
        requestedAmount,
        (ctx) => ctx.cacheMiss === true // Only record usage on cache misses
      )
    );
};

/**
 * Bulk operation rate limiting middleware - applies different limits based on operation size
 */
export const createBulkOperationMiddleware = (
  feature: FeatureType,
  calculateAmount: (input: any) => number,
  operationName: string
) =>
  t.middleware(async ({ ctx, next, input }) => {
    if (!ctx.userId) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Authentication required for bulk operations",
      });
    }

    const requestedAmount = calculateAmount(input);

    console.log(
      `🔄 Bulk operation rate limit check: ${feature} for user ${ctx.userId} (${requestedAmount} units)`
    );

    try {
      const rateLimitResult = await checkRateLimit(
        ctx.userId,
        feature,
        requestedAmount
      );

      if (!rateLimitResult.allowed) {
        logSecurityEvent({
          type: "RATE_LIMIT_EXCEEDED",
          userId: ctx.userId,
          details: `Bulk operation rate limit exceeded for ${feature}: ${rateLimitResult.currentUsage}/${rateLimitResult.limit} (operation: ${operationName})`,
        });

        throw new TRPCError({
          code: "TOO_MANY_REQUESTS",
          message: `Rate limit exceeded for ${feature}. Requested ${requestedAmount} units but only ${rateLimitResult.remaining} remaining. Try reducing the batch size.`,
        });
      }

      console.log(
        `✅ Bulk operation rate limit passed: ${rateLimitResult.remaining} remaining`
      );

      // Execute the operation
      const result = await next({
        ctx: {
          ...ctx,
          bulkOperation: {
            feature,
            requestedAmount,
            rateLimitInfo: rateLimitResult,
          },
        },
      });

      // Record usage only if successful
      if (result.ok) {
        try {
          await recordUsage(ctx.userId, feature, requestedAmount);
          await logUsage(ctx.userId, feature, requestedAmount);

          console.log(
            `📊 Bulk usage recorded: ${feature} (${requestedAmount} units) for user ${ctx.userId}`
          );
        } catch (error) {
          console.error("Failed to record bulk usage:", error);
        }
      }

      return result;
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }

      console.error("Bulk operation rate limit check failed:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to check rate limits for bulk operation",
      });
    }
  });

/**
 * Account ownership verification middleware
 */
export const createAccountOwnershipMiddleware = (
  accountIdField: string = "accountId"
) =>
  t.middleware(async ({ ctx, next, input }) => {
    if (!ctx.userId) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Authentication required",
      });
    }

    const accountId = (input as any)?.[accountIdField];
    if (!accountId) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: `${accountIdField} is required`,
      });
    }

    // Import prisma here to avoid circular dependencies
    const { prisma } = await import("./db-utils");

    try {
      const account = await prisma.monitoredAccount.findFirst({
        where: {
          id: accountId,
          userId: ctx.userId,
        },
      });

      if (!account) {
        logSecurityEvent({
          type: "UNAUTHORIZED_ACCESS",
          userId: ctx.userId,
          details: `Unauthorized account access attempt: ${accountId} (operation: account_ownership_check)`,
        });

        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Account not found or access denied",
        });
      }

      return next({
        ctx: {
          ...ctx,
          verifiedAccount: account,
        },
      });
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }

      console.error("Account ownership verification failed:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to verify account ownership",
      });
    }
  });

/**
 * Input validation middleware with enhanced error messages
 */
export const createValidationMiddleware = <T extends z.ZodType>(
  schema: T,
  fieldName: string = "input"
) =>
  t.middleware(async ({ next, input }) => {
    try {
      const validatedInput = schema.parse(input);

      return next({
        ctx: {
          validatedInput,
        },
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        const formattedErrors = error.errors.map((err) => ({
          path: err.path.join("."),
          message: err.message,
        }));

        throw new TRPCError({
          code: "BAD_REQUEST",
          message: `Validation failed for ${fieldName}: ${formattedErrors.map((e) => `${e.path}: ${e.message}`).join(", ")}`,
        });
      }

      throw new TRPCError({
        code: "BAD_REQUEST",
        message: `Invalid ${fieldName}`,
      });
    }
  });

/**
 * Enhanced feature procedure with configurable options - simplified version
 */
export const createEnhancedFeatureProcedure = (
  feature: FeatureType,
  options: {
    requestedAmount?: number;
    operationName?: string;
    requireFeatureAccess?: boolean;
    validateAccountOwnership?: boolean;
    accountIdField?: string;
    enableCaching?: boolean;
  } = {}
) => {
  const { operationName = feature } = options;

  console.log(`🔧 Creating enhanced feature procedure for ${feature}`);
  console.log(`🔧 Operation name: ${operationName}`);

  // Return a simplified procedure with basic authentication and performance monitoring
  return t.procedure
    .use(({ ctx, next }) => {
      if (!ctx.userId) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "You must be logged in to access this resource",
        });
      }
      return next({
        ctx: {
          ...ctx,
          userId: ctx.userId,
        },
      });
    })
    .use(securityMiddleware)
    .use(performanceMiddleware(operationName));
};

/**
 * Script Utilities for Prisma Database Operations
 *
 * Provides utilities for database scripts with proper lifecycle management,
 * error handling, and cleanup. All scripts should use these utilities
 * instead of creating PrismaClient instances directly.
 */

import type { PrismaClient } from "../../prisma/generated";
import {
  checkPrismaConnection,
  createPrismaClient,
  disconnectPrisma,
  type ExtendedPrismaClient,
  type PrismaConfigOptions,
} from "./prisma-config";

// Type alias for the Prisma client that scripts can use
// This ensures compatibility with both the base PrismaClient and ExtendedPrismaClient
type ScriptPrismaClient = ExtendedPrismaClient | PrismaClient;

export interface ScriptOptions {
  /** Script name for logging */
  scriptName?: string;
  /** Enable verbose logging */
  verbose?: boolean;
  /** Custom database URL */
  databaseUrl?: string;
  /** Skip connection health check */
  skipHealthCheck?: boolean;
  /** Maximum retries for connection failures */
  maxRetries?: number;
}

/**
 * Create a Prisma client optimized for script usage
 */
export function createScriptPrisma(options: ScriptOptions = {}): ExtendedPrismaClient {
  const { scriptName = "script", verbose = false, databaseUrl } = options;

  const configOptions: PrismaConfigOptions = {
    instanceId: scriptName,
    databaseUrl,
    forceQueryLogs: verbose,
    // Scripts don't need soft delete middleware typically
    disableMiddleware: false,
  };

  return createPrismaClient(configOptions);
}

/**
 * Execute a script function with proper Prisma lifecycle management
 * Automatically handles connection, health checks, and cleanup
 */
export async function withPrismaScript<T>(
  scriptFn: (prisma: ScriptPrismaClient) => Promise<T>,
  options: ScriptOptions = {}
): Promise<T> {
  const {
    scriptName = "script",
    skipHealthCheck = false,
    maxRetries = 3,
  } = options;

  console.log(`🚀 Script [${scriptName}]: Starting...`);

  const prisma = createScriptPrisma(options);

  try {
    // Health check with retries
    if (!skipHealthCheck) {
      let connected = false;
      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        console.log(
          `🔍 Script [${scriptName}]: Health check attempt ${attempt}/${maxRetries}`
        );

        if (await checkPrismaConnection(prisma, scriptName)) {
          connected = true;
          break;
        }

        if (attempt < maxRetries) {
          const delay = Math.min(2 ** attempt * 1000, 10000);
          console.log(
            `⏳ Script [${scriptName}]: Waiting ${delay}ms before retry...`
          );
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }

      if (!connected) {
        throw new Error(
          `Failed to establish database connection after ${maxRetries} attempts`
        );
      }
    }

    // Execute the script function
    console.log(`⚡ Script [${scriptName}]: Executing main function...`);
    const startTime = Date.now();

    const result = await scriptFn(prisma);

    const duration = Date.now() - startTime;
    console.log(
      `✅ Script [${scriptName}]: Completed successfully in ${duration}ms`
    );

    return result;
  } catch (error) {
    console.error(`❌ Script [${scriptName}]: Failed with error:`, error);
    throw error;
  } finally {
    // Always cleanup
    await disconnectPrisma(prisma, scriptName);
  }
}

/**
 * Execute a script with transaction support and comprehensive error handling
 */
export async function withPrismaTransaction<T>(
  scriptFn: (prisma: ScriptPrismaClient) => Promise<T>,
  options: ScriptOptions & {
    /** Transaction timeout in milliseconds */
    timeout?: number;
    /** Maximum wait time for transaction to start */
    maxWait?: number;
  } = {}
): Promise<T> {
  const { timeout = 30000, maxWait = 10000, ...scriptOptions } = options;

  return withPrismaScript(async (prisma) => {
    // Use type assertion to handle the transaction client type
    // The transaction client has all the same methods but a slightly different type
    return (prisma as any).$transaction(
      (tx: any) => scriptFn(tx),
      {
        maxWait,
        timeout,
      }
    ) as Promise<T>;
  }, scriptOptions);
}

/**
 * Utility for scripts that need to process data in batches
 */
export async function processBatches<T, R>(
  items: T[],
  batchSize: number,
  processor: (batch: T[], prisma: ScriptPrismaClient) => Promise<R[]>,
  options: ScriptOptions = {}
): Promise<R[]> {
  const { scriptName = "batch-processor" } = options;

  return withPrismaScript(async (prisma) => {
    const results: R[] = [];
    const totalBatches = Math.ceil(items.length / batchSize);

    console.log(
      `📦 Script [${scriptName}]: Processing ${items.length} items in ${totalBatches} batches of ${batchSize}`
    );

    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      const batchNumber = Math.floor(i / batchSize) + 1;

      console.log(
        `⚡ Script [${scriptName}]: Processing batch ${batchNumber}/${totalBatches} (${batch.length} items)`
      );

      try {
        const batchResults = await processor(batch, prisma);
        results.push(...batchResults);

        console.log(
          `✅ Script [${scriptName}]: Batch ${batchNumber} completed successfully`
        );
      } catch (error) {
        console.error(
          `❌ Script [${scriptName}]: Batch ${batchNumber} failed:`,
          error
        );
        throw error;
      }
    }

    console.log(
      `🎉 Script [${scriptName}]: All batches completed. Total results: ${results.length}`
    );
    return results;
  }, options);
}

/**
 * Utility for data migration scripts with progress tracking
 */
export async function runMigration<T>(
  migrationName: string,
  getData: (prisma: ScriptPrismaClient) => Promise<T[]>,
  migrateItem: (item: T, prisma: ScriptPrismaClient) => Promise<void>,
  options: ScriptOptions & {
    /** Batch size for processing */
    batchSize?: number;
    /** Skip items that fail migration */
    continueOnError?: boolean;
  } = {}
): Promise<{ success: number; failed: number; total: number }> {
  const {
    batchSize = 100,
    continueOnError = false,
    ...scriptOptions
  } = options;

  const stats = { success: 0, failed: 0, total: 0 };

  return withPrismaScript(
    async (prisma) => {
      console.log(
        `🔄 Migration [${migrationName}]: Starting data migration...`
      );

      // Get all data to migrate
      const items = await getData(prisma);
      stats.total = items.length;

      console.log(
        `📊 Migration [${migrationName}]: Found ${items.length} items to migrate`
      );

      if (items.length === 0) {
        console.log(`✅ Migration [${migrationName}]: No items to migrate`);
        return stats;
      }

      // Process in batches
      for (let i = 0; i < items.length; i += batchSize) {
        const batch = items.slice(i, i + batchSize);
        const batchNumber = Math.floor(i / batchSize) + 1;
        const totalBatches = Math.ceil(items.length / batchSize);

        console.log(
          `⚡ Migration [${migrationName}]: Processing batch ${batchNumber}/${totalBatches}`
        );

        for (const item of batch) {
          try {
            await migrateItem(item, prisma);
            stats.success++;
          } catch (error) {
            stats.failed++;
            console.error(
              `❌ Migration [${migrationName}]: Failed to migrate item:`,
              error
            );

            if (!continueOnError) {
              throw error;
            }
          }
        }

        // Progress update
        const progress = (((i + batch.length) / items.length) * 100).toFixed(1);
        console.log(
          `📈 Migration [${migrationName}]: Progress ${progress}% (${stats.success} success, ${stats.failed} failed)`
        );
      }

      console.log(
        `🎉 Migration [${migrationName}]: Completed! Success: ${stats.success}, Failed: ${stats.failed}, Total: ${stats.total}`
      );
      return stats;
    },
    { ...scriptOptions, scriptName: migrationName }
  );
}

/**
 * Simple script runner with basic error handling
 * For quick scripts that don't need advanced features
 */
export async function runScript(
  scriptName: string,
  scriptFn: (prisma: ScriptPrismaClient) => Promise<void>,
  options: Omit<ScriptOptions, "scriptName"> = {}
): Promise<void> {
  return withPrismaScript(scriptFn, { ...options, scriptName });
}

/**
 * OpenAI Image Generation Tool
 *
 * Generates images using OpenAI's new gpt-image-1 model via Responses API
 * with streaming support and automatic prompt optimization
 */

import { openai } from "@ai-sdk/openai";
import { tool } from "ai";
import { z } from "zod";

export const imageGenerationTool = tool({
  description:
    "Generate images using OpenAI's gpt-image-1 model with automatic prompt optimization",
  parameters: z.object({
    prompt: z.string().describe("Detailed image generation prompt"),
    size: z
      .enum([
        "1024x1024",
        "1792x1024",
        "1024x1792",
        "1536x1024",
        "1024x1536",
        "auto",
      ])
      .optional()
      .default("auto")
      .describe("Image dimensions - use 'auto' for automatic selection"),
    quality: z
      .enum(["low", "medium", "high", "auto"])
      .optional()
      .default("auto")
      .describe("Rendering quality - use 'auto' for automatic selection"),
    format: z
      .enum(["png", "jpeg", "webp"])
      .optional()
      .default("png")
      .describe("Output image format"),
    background: z
      .enum(["transparent", "opaque", "auto"])
      .optional()
      .default("auto")
      .describe("Background type - use 'auto' for automatic selection"),
    enableStreaming: z
      .boolean()
      .optional()
      .default(false)
      .describe("Enable streaming partial images for faster feedback"),
    partialImages: z
      .number()
      .min(1)
      .max(3)
      .optional()
      .default(2)
      .describe("Number of partial images to stream (1-3)"),
  }),
  execute: async ({
    prompt,
    size,
    quality,
    format,
    background,
    enableStreaming,
    partialImages,
  }) => {
    try {
      if (!process.env.OPENAI_API_KEY) {
        throw new Error("OPENAI_API_KEY not configured");
      }

      console.log("🎨 OpenAI Image: Generating image with gpt-image-1 model");
      console.log("📝 OpenAI Image: Prompt:", prompt.substring(0, 100) + "...");
      console.log("⚙️ OpenAI Image: Settings:", {
        size,
        quality,
        format,
        background,
        enableStreaming,
      });

      // Create OpenAI client
      const client = new (await import("openai")).default({
        apiKey: process.env.OPENAI_API_KEY,
      });

      // Configure image generation tool options (simplified based on OpenAI docs)
      const imageGenTool: any = {
        type: "image_generation",
        ...(size !== "auto" && { size }),
        ...(quality !== "auto" && { quality }),
        ...(format && { format }),
        ...(background !== "auto" && { background }),
        ...(enableStreaming && { partial_images: partialImages }),
      };

      console.log("🔧 OpenAI Image: Tool config:", imageGenTool);

      // Generate image using Responses API
      const response = await client.responses.create({
        model: "gpt-4o-mini", // Mainline model that can call image generation
        input: `Generate an image: ${prompt}`,
        tools: [imageGenTool],
        tool_choice: { type: "image_generation" }, // Force image generation
      } as any);

      console.log("✅ OpenAI Image: Response received");

      // Extract image generation results (cast to any to handle type issues)
      const imageGenerationCalls = (response as any).output.filter(
        (output: any) => output.type === "image_generation_call"
      );

      if (imageGenerationCalls.length === 0) {
        throw new Error("No image generation call found in response");
      }

      const imageCall = imageGenerationCalls[0];

      if (imageCall.status !== "completed") {
        throw new Error(
          `Image generation failed with status: ${imageCall.status}`
        );
      }

      const imageBase64 = imageCall.result;
      const revisedPrompt = imageCall.revised_prompt;

      if (!imageBase64) {
        throw new Error("No image data returned from OpenAI");
      }

      console.log("🖼️ OpenAI Image: Image generated successfully");
      console.log("📝 OpenAI Image: Revised prompt:", revisedPrompt);

      // Convert base64 to data URL for immediate use
      const imageDataUrl = `data:image/${format};base64,${imageBase64}`;

      // TODO: Upload to UploadThing in future implementation
      // For now, return the base64 data URL
      const uploadedImageUrl = imageDataUrl; // This would be replaced with UploadThing URL

      return {
        imageUrl: uploadedImageUrl,
        imageBase64,
        originalPrompt: prompt,
        revisedPrompt,
        size: size === "auto" ? "auto-selected" : size,
        quality: quality === "auto" ? "auto-selected" : quality,
        format,
        background: background === "auto" ? "auto-selected" : background,
        timestamp: new Date().toISOString(),
        source: "OpenAI gpt-image-1",
        model: "gpt-image-1",
        responseId: response.id,
        imageCallId: imageCall.id,
        // TODO: Add UploadThing metadata
        uploadThing: {
          url: uploadedImageUrl,
          key: null, // Would be set after UploadThing upload
        },
      };
    } catch (error) {
      console.error("❌ OpenAI Image: Generation error:", error);

      // Return graceful error response
      return {
        originalPrompt: prompt,
        error:
          error instanceof Error ? error.message : "Failed to generate image",
        timestamp: new Date().toISOString(),
        source: "OpenAI gpt-image-1",
        model: "gpt-image-1",
      };
    }
  },
});

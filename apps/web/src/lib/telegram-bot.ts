/**
 * Telegram Bot Service
 *
 * Refactored Telegram bot implementation using new modular architecture
 * Maintains backward compatibility while using the new handler system
 */

import TelegramBot from "node-telegram-bot-api";
import { TelegramBotCore, type TelegramBotConfig as CoreConfig } from "./telegram/core/telegram-bot-core";
import { TelegramBotRouter } from "./telegram/core/telegram-bot-router";
import { telegramLogger } from "./telegram-logger";

export interface TelegramBotConfig {
  token: string;
  webhookUrl?: string;
  enablePolling?: boolean;
}

/**
 * Main Telegram Bot Service
 * Now uses the new modular architecture internally while maintaining the same public API
 */
export class TelegramBotService {
  private core: TelegramBotCore;
  private router: TelegramBotRouter;
  private config: TelegramBotConfig;

  // Legacy properties for backward compatibility
  private bot: TelegramBot;

  constructor(config: TelegramBotConfig) {
    this.config = config;

    // Initialize new modular architecture
    const coreConfig: CoreConfig = {
      token: config.token,
      enablePolling: config.enablePolling,
      webhookUrl: config.webhookUrl,
    };

    this.core = new TelegramBotCore(coreConfig);
    this.router = new TelegramBotRouter(this.core.getContext());

    // Get bot instance for backward compatibility
    this.bot = this.core.getBot();

    // Register router with core
    this.core.onUpdate(async (update) => {
      await this.router.routeUpdate(update);
    });

    telegramLogger.info("TelegramBotService initialized with new architecture", {
      metadata: {
        enablePolling: config.enablePolling,
        webhookUrl: config.webhookUrl,
      },
    });
  }

  /**
   * Initialize the bot using new architecture
   */
  async initialize() {
    try {
      telegramLogger.info("Initializing TelegramBotService with new architecture");

      // Initialize the core bot
      await this.core.initialize();

      // Get bot info for logging
      const botInfo = await this.bot.getMe();
      telegramLogger.info("Bot initialized successfully", {
        metadata: {
          botId: botInfo.id,
          botUsername: botInfo.username,
          botFirstName: botInfo.first_name,
          architecture: "modular",
        },
      });

      telegramLogger.info("TelegramBotService initialization completed");
    } catch (error) {
      telegramLogger.error("Failed to initialize TelegramBotService", {
        metadata: { error: error instanceof Error ? error.message : String(error) },
      });
      throw error;
    }
  }

  /**
   * Set up webhook for production use
   */
  async setWebhook(url: string) {
    try {
      await this.bot.setWebHook(url);
      telegramLogger.info("Webhook set successfully", {
        metadata: { webhookUrl: url },
      });
    } catch (error) {
      telegramLogger.error("Failed to set webhook", {
        metadata: { 
          webhookUrl: url,
          error: error instanceof Error ? error.message : String(error),
        },
      });
      throw error;
    }
  }

  /**
   * Get current webhook info from Telegram
   */
  async getWebHookInfo() {
    try {
      return await this.bot.getWebHookInfo();
    } catch (error) {
      telegramLogger.error("Failed to get webhook info", {
        metadata: { error: error instanceof Error ? error.message : String(error) },
      });
      throw error;
    }
  }

  /**
   * Process webhook update using new architecture
   */
  async processUpdate(update: any) {
    try {
      telegramLogger.debug("Processing webhook update", {
        metadata: {
          updateId: update.update_id,
          hasMessage: !!update.message,
          hasCallbackQuery: !!update.callback_query,
        },
      });

      // Use the core's webhook processing
      await this.core.processWebhookUpdate(update);
    } catch (error) {
      telegramLogger.error("Error processing webhook update", {
        metadata: {
          updateId: update.update_id,
          error: error instanceof Error ? error.message : String(error),
        },
      });
      throw error;
    }
  }

  /**
   * Get bot statistics (new architecture)
   */
  async getStats() {
    const coreStats = await this.core.getStats();
    const routerStats = this.router.getStats();
    
    return {
      ...coreStats,
      router: routerStats,
      architecture: "modular",
    };
  }

  /**
   * Shutdown the bot gracefully
   */
  async shutdown() {
    await this.core.shutdown();
  }

  /**
   * Get the bot instance for backward compatibility
   */
  getBot(): TelegramBot {
    return this.bot;
  }

  /**
   * Check if bot is ready
   */
  isReady(): boolean {
    return this.core.isReady();
  }

  /**
   * Get the router for adding custom handlers
   */
  getRouter(): TelegramBotRouter {
    return this.router;
  }

  /**
   * Get the core for advanced operations
   */
  getCore(): TelegramBotCore {
    return this.core;
  }
}

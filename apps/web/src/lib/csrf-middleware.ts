/**
 * CSRF Protection Middleware
 * 
 * Provides Cross-Site Request Forgery protection for API endpoints
 */

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import {
  createCSRFTokenWithExpiry,
  validateCSRFTokenWithExpiry,
  logSecurityEvent,
} from "./security-utils";

const CSRF_TOKEN_HEADER = "x-csrf-token";
const CSRF_COOKIE_NAME = "csrf-token";

/**
 * Generate CSRF token and set it in response headers
 */
export function generateCSRFResponse(): NextResponse {
  const { combined } = createCSRFTokenWithExpiry(60); // 1 hour expiry
  
  const response = NextResponse.json({
    success: true,
    csrfToken: combined,
  });
  
  // Set CSRF token in httpOnly cookie
  response.cookies.set(CSRF_COOKIE_NAME, combined, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
    maxAge: 60 * 60 * 1000, // 1 hour
    path: "/",
  });
  
  return response;
}

/**
 * CSRF protection middleware for API routes
 */
export function csrfProtection(handler: Function) {
  return async (req: NextRequest) => {
    // Skip CSRF protection for GET, HEAD, OPTIONS (safe methods)
    if (["GET", "HEAD", "OPTIONS"].includes(req.method)) {
      return handler(req);
    }
    
    // Skip CSRF for webhook endpoints (they have their own security)
    if (req.nextUrl.pathname.includes("/webhook")) {
      return handler(req);
    }
    
    try {
      // Get CSRF token from header
      const tokenFromHeader = req.headers.get(CSRF_TOKEN_HEADER);
      
      // Get CSRF token from cookie
      const tokenFromCookie = req.cookies.get(CSRF_COOKIE_NAME)?.value;
      
      if (!tokenFromHeader) {
        logSecurityEvent({
          type: "INVALID_INPUT",
          details: "Missing CSRF token in header",
          ip: req.headers.get("x-forwarded-for") || "unknown",
        });
        
        return NextResponse.json(
          { 
            error: "CSRF token required",
            code: "MISSING_CSRF_TOKEN" 
          },
          { status: 403 }
        );
      }
      
      if (!tokenFromCookie) {
        logSecurityEvent({
          type: "INVALID_INPUT",
          details: "Missing CSRF token in cookie",
          ip: req.headers.get("x-forwarded-for") || "unknown",
        });
        
        return NextResponse.json(
          { 
            error: "CSRF token not found",
            code: "MISSING_CSRF_COOKIE" 
          },
          { status: 403 }
        );
      }
      
      // Validate that header token matches cookie token
      if (tokenFromHeader !== tokenFromCookie) {
        logSecurityEvent({
          type: "INVALID_INPUT",
          details: "CSRF token mismatch between header and cookie",
          ip: req.headers.get("x-forwarded-for") || "unknown",
        });
        
        return NextResponse.json(
          { 
            error: "CSRF token mismatch",
            code: "CSRF_TOKEN_MISMATCH" 
          },
          { status: 403 }
        );
      }
      
      // Validate token format and expiration
      const validation = validateCSRFTokenWithExpiry(tokenFromHeader);
      
      if (!validation.isValid) {
        logSecurityEvent({
          type: "INVALID_INPUT",
          details: `CSRF token validation failed: ${validation.error}`,
          ip: req.headers.get("x-forwarded-for") || "unknown",
        });
        
        return NextResponse.json(
          { 
            error: validation.isExpired ? "CSRF token expired" : "Invalid CSRF token",
            code: validation.isExpired ? "CSRF_TOKEN_EXPIRED" : "INVALID_CSRF_TOKEN",
            ...(validation.isExpired && { generateNew: true })
          },
          { status: 403 }
        );
      }
      
      // CSRF protection passed, proceed with the handler
      return handler(req);
      
    } catch (error) {
      console.error("CSRF middleware error:", error);
      
      logSecurityEvent({
        type: "AUTH_FAILURE",
        details: "CSRF middleware error",
        ip: req.headers.get("x-forwarded-for") || "unknown",
      });
      
      return NextResponse.json(
        { 
          error: "Security validation failed",
          code: "CSRF_VALIDATION_ERROR" 
        },
        { status: 500 }
      );
    }
  };
}

/**
 * Middleware to add CSRF token to authenticated users' responses
 */
export async function addCSRFTokenToResponse(response: NextResponse, userId?: string | null): Promise<NextResponse> {
  // If userId is provided (from middleware context), use it directly
  // Otherwise, try to get it from auth() (for API routes)
  let authenticatedUserId = userId;

  if (authenticatedUserId === undefined) {
    try {
      const authResult = await auth();
      authenticatedUserId = authResult.userId;
    } catch (error) {
      // If auth() fails (e.g., outside middleware context), skip CSRF token
      console.warn("🔒 CSRF: Could not get auth context, skipping CSRF token generation");
      return response;
    }
  }

  if (authenticatedUserId) {
    const { combined } = createCSRFTokenWithExpiry(60);

    // Add CSRF token to response header
    response.headers.set("X-CSRF-Token", combined);

    // Set CSRF token in httpOnly cookie
    response.cookies.set(CSRF_COOKIE_NAME, combined, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 60 * 60 * 1000, // 1 hour
      path: "/",
    });
  }

  return response;
}

/**
 * Get CSRF token for client-side use
 */
export async function getCSRFTokenAPI(req: NextRequest): Promise<NextResponse> {
  try {
    // Ensure user is authenticated
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    return generateCSRFResponse();
  } catch (error) {
    console.error("🔒 CSRF API: Auth error:", error);
    return NextResponse.json(
      { error: "Authentication error" },
      { status: 500 }
    );
  }
}

/**
 * Utility to extract and validate CSRF token from request
 */
export function extractCSRFToken(req: NextRequest): {
  isValid: boolean;
  error?: string;
  token?: string;
} {
  const tokenFromHeader = req.headers.get(CSRF_TOKEN_HEADER);
  const tokenFromCookie = req.cookies.get(CSRF_COOKIE_NAME)?.value;
  
  if (!tokenFromHeader || !tokenFromCookie) {
    return { 
      isValid: false, 
      error: "Missing CSRF token" 
    };
  }
  
  if (tokenFromHeader !== tokenFromCookie) {
    return { 
      isValid: false, 
      error: "CSRF token mismatch" 
    };
  }
  
  const validation = validateCSRFTokenWithExpiry(tokenFromHeader);
  
  if (!validation.isValid) {
    return { 
      isValid: false, 
      error: validation.error || "Invalid CSRF token" 
    };
  }
  
  return { 
    isValid: true, 
    token: tokenFromHeader 
  };
}
/**
 * Input Validation Middleware
 * 
 * Provides comprehensive input validation for API endpoints using Zod schemas
 */

import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { logSecurityEvent, sanitizeInput, sanitizeMetadata } from "./security-utils";

/**
 * Common validation schemas
 */
export const commonSchemas = {
  // Basic types
  nonEmptyString: z.string().min(1).max(1000),
  email: z.string().email().max(254),
  url: z.string().url().max(2048),
  positiveInt: z.number().int().positive(),
  boolean: z.boolean(),
  
  // IDs
  cuid: z.string().regex(/^c[a-z0-9]{24}$/, "Invalid CUID format"),
  uuid: z.string().uuid(),
  
  // Pagination
  pagination: z.object({
    page: z.number().int().min(1).default(1),
    limit: z.number().int().min(1).max(100).default(20),
    offset: z.number().int().min(0).optional(),
  }),
  
  // API Keys
  apiKey: z.string().regex(/^[a-zA-Z0-9]{32,}$/, "Invalid API key format"),
  
  // Content validation
  safeText: z.string().max(10000).transform((val) => sanitizeInput(val)),
  safeHtml: z.string().max(50000).transform((val) => sanitizeInput(val)),
  
  // User input
  userName: z.string().min(1).max(100).transform((val) => sanitizeInput(val)),
  userBio: z.string().max(500).transform((val) => sanitizeInput(val)),
  
  // Twitter-specific
  twitterHandle: z.string()
    .min(1)
    .max(15)
    .regex(/^@?[a-zA-Z0-9_]+$/, "Invalid Twitter handle")
    .transform((val) => val.replace("@", "")),
    
  twitterUrl: z.string()
    .url()
    .refine((url) => url.includes("twitter.com") || url.includes("x.com"), 
             "Must be a Twitter/X URL"),
};

/**
 * Request validation options
 */
interface ValidationOptions {
  body?: z.ZodSchema;
  query?: z.ZodSchema;
  headers?: z.ZodSchema;
  params?: z.ZodSchema;
  sanitizeBody?: boolean;
  maxBodySize?: number;
}

/**
 * Validation result
 */
interface ValidationResult<T = any> {
  success: boolean;
  data?: T;
  errors?: z.ZodError;
  error?: string;
}

/**
 * Validate request body
 */
async function validateBody<T>(
  req: NextRequest, 
  schema: z.ZodSchema<T>,
  maxSize: number = 1024 * 1024 // 1MB default
): Promise<ValidationResult<T>> {
  try {
    // Check content type
    const contentType = req.headers.get("content-type");
    if (contentType && !contentType.includes("application/json")) {
      return {
        success: false,
        error: "Invalid content type. Expected application/json"
      };
    }
    
    // Get raw body first to check size
    const rawBody = await req.text();
    
    // Check body size
    if (rawBody.length > maxSize) {
      logSecurityEvent({
        type: "INVALID_INPUT",
        details: `Request body too large: ${rawBody.length} bytes`,
        ip: req.headers.get("x-forwarded-for") || "unknown",
      });
      
      return {
        success: false,
        error: `Request body too large. Maximum ${maxSize} bytes allowed`
      };
    }
    
    // Parse JSON
    let jsonBody;
    try {
      jsonBody = JSON.parse(rawBody);
    } catch (error) {
      return {
        success: false,
        error: "Invalid JSON format"
      };
    }
    
    // Validate with schema
    const result = schema.safeParse(jsonBody);
    
    if (!result.success) {
      logSecurityEvent({
        type: "INVALID_INPUT",
        details: "Request body validation failed",
        ip: req.headers.get("x-forwarded-for") || "unknown",
      });
      
      return {
        success: false,
        errors: result.error
      };
    }
    
    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Validation failed"
    };
  }
}

/**
 * Validate URL query parameters
 */
function validateQuery<T>(
  req: NextRequest,
  schema: z.ZodSchema<T>
): ValidationResult<T> {
  try {
    const url = new URL(req.url);
    const params = Object.fromEntries(url.searchParams.entries());
    
    // Convert numeric strings to numbers
    const processedParams: any = {};
    for (const [key, value] of Object.entries(params)) {
      if (typeof value === "string" && !isNaN(Number(value)) && value !== "") {
        processedParams[key] = Number(value);
      } else if (value === "true" || value === "false") {
        processedParams[key] = value === "true";
      } else {
        processedParams[key] = sanitizeInput(value);
      }
    }
    
    const result = schema.safeParse(processedParams);
    
    if (!result.success) {
      logSecurityEvent({
        type: "INVALID_INPUT",
        details: "Query parameters validation failed",
        ip: req.headers.get("x-forwarded-for") || "unknown",
      });
      
      return {
        success: false,
        errors: result.error
      };
    }
    
    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Query validation failed"
    };
  }
}

/**
 * Validate request headers
 */
function validateHeaders<T>(
  req: NextRequest,
  schema: z.ZodSchema<T>
): ValidationResult<T> {
  try {
    const headers: Record<string, string> = {};
    req.headers.forEach((value, key) => {
      headers[key] = value;
    });
    
    const result = schema.safeParse(headers);
    
    if (!result.success) {
      logSecurityEvent({
        type: "INVALID_INPUT",
        details: "Request headers validation failed",
        ip: req.headers.get("x-forwarded-for") || "unknown",
      });
      
      return {
        success: false,
        errors: result.error
      };
    }
    
    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Header validation failed"
    };
  }
}

/**
 * Create validation middleware
 */
export function validateRequest(options: ValidationOptions = {}) {
  return function validationMiddleware(handler: Function) {
    return async (req: NextRequest, context?: any) => {
      try {
        const validatedData: any = {};
        
        // Validate body if schema provided
        if (options.body && ["POST", "PUT", "PATCH"].includes(req.method)) {
          const bodyResult = await validateBody(
            req, 
            options.body, 
            options.maxBodySize
          );
          
          if (!bodyResult.success) {
            return NextResponse.json(
              {
                error: bodyResult.error || "Invalid request body",
                details: bodyResult.errors?.format(),
                code: "VALIDATION_ERROR"
              },
              { status: 400 }
            );
          }
          
          validatedData.body = bodyResult.data;
        }
        
        // Validate query parameters if schema provided
        if (options.query) {
          const queryResult = validateQuery(req, options.query);
          
          if (!queryResult.success) {
            return NextResponse.json(
              {
                error: queryResult.error || "Invalid query parameters",
                details: queryResult.errors?.format(),
                code: "VALIDATION_ERROR"
              },
              { status: 400 }
            );
          }
          
          validatedData.query = queryResult.data;
        }
        
        // Validate headers if schema provided
        if (options.headers) {
          const headerResult = validateHeaders(req, options.headers);
          
          if (!headerResult.success) {
            return NextResponse.json(
              {
                error: headerResult.error || "Invalid request headers",
                details: headerResult.errors?.format(),
                code: "VALIDATION_ERROR"
              },
              { status: 400 }
            );
          }
          
          validatedData.headers = headerResult.data;
        }
        
        // Add validated data to request for handler access
        (req as any).validated = validatedData;
        
        // Call the original handler
        return await handler(req, context);
        
      } catch (error) {
        console.error("Validation middleware error:", error);
        
        logSecurityEvent({
          type: "AUTH_FAILURE",
          details: "Validation middleware error",
          ip: req.headers.get("x-forwarded-for") || "unknown",
        });
        
        return NextResponse.json(
          {
            error: "Validation failed",
            code: "VALIDATION_MIDDLEWARE_ERROR"
          },
          { status: 500 }
        );
      }
    };
  };
}

/**
 * Quick validation helpers for common cases
 */
export const validators = {
  // API endpoints with pagination
  withPagination: (additionalQuery?: z.ZodObject<any>) => 
    validateRequest({
      query: additionalQuery 
        ? commonSchemas.pagination.merge(additionalQuery)
        : commonSchemas.pagination
    }),
    
  // API endpoints that require API key auth
  withApiKey: (bodySchema?: z.ZodSchema) =>
    validateRequest({
      headers: z.object({
        authorization: z.string().regex(/^Bearer [a-zA-Z0-9]{32,}$/, "Invalid API key format")
      }),
      body: bodySchema
    }),
    
  // User input endpoints
  withSafeInput: (bodySchema: z.ZodSchema) =>
    validateRequest({
      body: bodySchema,
      sanitizeBody: true,
      maxBodySize: 100 * 1024 // 100KB for user input
    }),
    
  // File upload endpoints
  withFileUpload: (maxSize: number = 10 * 1024 * 1024) =>
    validateRequest({
      maxBodySize: maxSize,
      headers: z.object({
        "content-type": z.string().regex(/^multipart\/form-data/, "Invalid content type for file upload")
      })
    }),
};

/**
 * Schema builder helpers
 */
export const schemaBuilder = {
  // Create schema for user profile updates
  userProfile: () => z.object({
    name: commonSchemas.userName.optional(),
    bio: commonSchemas.userBio.optional(),
    avatar: commonSchemas.url.optional(),
  }),
  
  // Create schema for Twitter-related operations
  twitterOperation: () => z.object({
    handle: commonSchemas.twitterHandle.optional(),
    url: commonSchemas.twitterUrl.optional(),
  }),
  
  // Create schema for AI operations
  aiOperation: () => z.object({
    prompt: commonSchemas.safeText,
    model: z.string().min(1).max(100),
    maxTokens: z.number().int().min(1).max(4000).optional(),
  }),
};
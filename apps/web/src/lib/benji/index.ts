/**
 * Benji AI Agent - Main exports
 *
 * Provides the refactored BenjiAgent and helper functions
 * for creating and managing AI agents.
 */

import { getModelByPlan, type ModelName } from "../ai-providers";
import { prisma } from "../db-utils";
import { BenjiAgent } from "./benji-agent";
import type { BenjiConfig } from "./types";

// Re-export the main agent class
export { BenjiAgent };

// Re-export services for advanced usage
export { ContentAnalyzerService } from "./services/content-analyzer";
export { MarketIntelligenceService } from "./services/market-intelligence";
export { MemoryService } from "./services/memory-service";
export { PersonaAnalyzerService } from "./services/persona-analyzer";
export { PromptBuilderService } from "./services/prompt-builder";
export { ResponseGeneratorService } from "./services/response-generator";
export { ToolService } from "./services/tool-service";
// Re-export tool-related exports
export { ToolRegistry, ToolUsageTracker } from "./services/tools";
// Re-export all types
export * from "./types";

/**
 * Convenience function for quick agent creation
 */
export function createBenjiAgent(config: BenjiConfig = {}) {
  return new BenjiAgent(config);
}

/**
 * Helper function to get agent configured for a specific user
 */
export async function getBenjiForUser(userId: string) {
  console.log("🤖 Benji: Getting agent for user:", userId);

  // Validate environment variables
  const requiredEnvVars = ["OPENROUTER_API_KEY"];
  const missingVars = requiredEnvVars.filter((key) => !process.env[key]);
  if (missingVars.length > 0) {
    console.error("❌ Benji: Missing environment variables:", missingVars);
    throw new Error(
      `Missing required environment variables: ${missingVars.join(", ")}`
    );
  }

  // Get user's plan, personality, and selected model from database
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      plan: true,
      selectedPersonality: true,
      selectedModel: true,
    },
  });

  if (!user) {
    console.error("❌ Benji: User not found:", userId);
    throw new Error("User not found");
  }

  console.log("✅ Benji: User found with plan:", user.plan.name);
  console.log(
    "🎭 Benji: Personality:",
    user.selectedPersonality?.name || "None"
  );
  console.log(
    "🧠 Benji: Selected model:",
    user.selectedModel?.name || "Plan default"
  );
  console.log(
    "📝 Benji: Custom prompt:",
    user.customSystemPrompt ? "Yes" : "No"
  );
  console.log("👤 Benji: First person mode:", user.useFirstPerson ?? true);
  console.log("🔑 Benji: Environment check passed");

  // Determine model to use: user's selected model or plan default
  let modelToUse: ModelName | undefined;
  if (user.selectedModel) {
    // Map database model names to ModelName enum
    const modelNameMap: Record<string, ModelName> = {
      Workhorse: "gemini25Flash",
      Smarty: "gemini25Pro",
      "Big Brain": "openaiO3",
    };
    modelToUse = modelNameMap[user.selectedModel.name];
  }

  return new BenjiAgent({
    userId,
    userPlan: user.plan.name,
    model: modelToUse, // Use selected model or fallback to plan default
    enableTools: true,
    personalityPrompt: user.selectedPersonality?.systemPrompt,
    customSystemPrompt: user.customSystemPrompt || undefined,
    useFirstPerson: user.useFirstPerson ?? true,
  });
}

/**
 * Tool Service - Advanced AI tool management and orchestration
 *
 * Manages AI tools, handles tool usage tracking, limits, and provides
 * a unified interface for all tool operations.
 */

import { exaSearchTool } from "../../tools/exa-search";
import { mem0MemoryTool } from "../../tools/mem0-memory";
import { imageGenerationTool } from "../../tools/openai-image";
import { xaiSearchTool } from "../../tools/xai-search";
import type { BenjiConfig } from "../types";

export interface ToolRegistry {
  searchWeb: typeof xaiSearchTool;
  searchKnowledge: typeof exaSearchTool;
  generateImage: typeof imageGenerationTool;
  manageMemory: typeof mem0MemoryTool;
}

export interface ToolUsageTracker {
  toolName: string;
  usageCount: number;
  lastUsed: Date;
  successRate: number;
  avgResponseTime: number;
}

export interface ToolLimits {
  maxToolsPerRequest: number;
  maxStepsPerConversation: number;
  allowedTools: string[];
}

export class ToolService {
  private config: BenjiConfig;
  private tools: ToolRegistry;
  private usageTrackers: Map<string, ToolUsageTracker> = new Map();
  private limits: ToolLimits;
  private currentRequestToolCount: number = 0;

  constructor(config: BenjiConfig) {
    this.config = config;
    this.tools = {
      searchWeb: xaiSearchTool,
      searchKnowledge: exaSearchTool,
      generateImage: imageGenerationTool,
      manageMemory: mem0MemoryTool,
    };
    this.limits = this.calculateLimits();
  }

  /**
   * Get available tools based on user plan and configuration
   */
  getAvailableTools(): Partial<ToolRegistry> {
    if (!this.config.enableTools) {
      return {};
    }

    const availableTools: Partial<ToolRegistry> = {};

    // Basic tools available to all plans
    availableTools.searchWeb = this.tools.searchWeb;
    availableTools.searchKnowledge = this.tools.searchKnowledge;
    availableTools.manageMemory = this.tools.manageMemory;

    // Premium tools based on plan
    if (this.config.userPlan !== "reply-guy") {
      availableTools.generateImage = this.tools.generateImage;
    }

    // Special model considerations
    const isO3Model = this.config.model === "openaiO3";
    if (isO3Model) {
      // o3 might have tool limitations - provide basic tools only
      return {
        searchWeb: availableTools.searchWeb,
        searchKnowledge: availableTools.searchKnowledge,
      };
    }

    return availableTools;
  }

  /**
   * Calculate tool limits based on user plan
   */
  private calculateLimits(): ToolLimits {
    const plan = this.config.userPlan || "reply-guy";

    switch (plan) {
      case "reply-guy":
        return {
          maxToolsPerRequest: 2,
          maxStepsPerConversation: 3,
          allowedTools: ["searchWeb", "searchKnowledge", "manageMemory"],
        };
      case "pro-plan":
        return {
          maxToolsPerRequest: 3,
          maxStepsPerConversation: 5,
          allowedTools: [
            "searchWeb",
            "searchKnowledge",
            "generateImage",
            "manageMemory",
          ],
        };
      case "team-plan":
        return {
          maxToolsPerRequest: 5,
          maxStepsPerConversation: 10,
          allowedTools: [
            "searchWeb",
            "searchKnowledge",
            "generateImage",
            "manageMemory",
          ],
        };
      default:
        return {
          maxToolsPerRequest: 1,
          maxStepsPerConversation: 1,
          allowedTools: ["searchWeb"],
        };
    }
  }

  /**
   * Track tool usage for analytics and limits
   */
  trackToolUsage(
    toolName: string,
    success: boolean,
    responseTime: number
  ): void {
    const tracker = this.usageTrackers.get(toolName) || {
      toolName,
      usageCount: 0,
      lastUsed: new Date(),
      successRate: 0,
      avgResponseTime: 0,
    };

    tracker.usageCount++;
    tracker.lastUsed = new Date();

    // Increment current request tool count
    this.currentRequestToolCount++;

    // Calculate running average correctly
    tracker.avgResponseTime =
      (tracker.avgResponseTime * (tracker.usageCount - 1) + responseTime) /
      tracker.usageCount;

    // Update success rate
    const currentSuccessRate = tracker.successRate;
    const totalRequests = tracker.usageCount;
    const successfulRequests =
      Math.floor(currentSuccessRate * (totalRequests - 1)) + (success ? 1 : 0);
    tracker.successRate = successfulRequests / totalRequests;

    this.usageTrackers.set(toolName, tracker);
  }

  /**
   * Get tool usage statistics
   */
  getToolUsageStats(): ToolUsageTracker[] {
    return Array.from(this.usageTrackers.values());
  }

  /**
   * Check if tool usage is within limits
   */
  canUseTool(toolName: string): boolean {
    if (!this.limits.allowedTools.includes(toolName)) {
      return false;
    }

    // Check per-request limit
    if (this.currentRequestToolCount >= this.limits.maxToolsPerRequest) {
      return false;
    }

    const tracker = this.usageTrackers.get(toolName);
    if (!tracker) {
      return true; // No previous usage, allow
    }

    // Check if we're within conversation limits
    return tracker.usageCount <= this.limits.maxStepsPerConversation;
  }

  /**
   * Get tool configuration for AI model
   */
  getToolConfiguration() {
    const availableTools = this.getAvailableTools();
    const modelSupportsTools = this.config.model !== "openaiO3";

    if (!modelSupportsTools || !this.config.enableTools) {
      return undefined;
    }

    console.log("🛠️ ToolService: Configuring tools:", {
      availableToolCount: Object.keys(availableTools).length,
      plan: this.config.userPlan,
      limits: this.limits,
    });

    return availableTools;
  }

  /**
   * Get tool recommendations based on context
   */
  getRecommendedTools(context: {
    contentType: "crypto" | "tech" | "general";
    needsResearch: boolean;
    needsVisuals: boolean;
  }): string[] {
    const recommendations: string[] = [];

    if (context.needsResearch) {
      if (context.contentType === "crypto") {
        recommendations.push("searchKnowledge"); // Exa is better for crypto
      } else {
        recommendations.push("searchWeb"); // xAI for real-time
      }
    }

    if (context.needsVisuals && this.canUseTool("generateImage")) {
      recommendations.push("generateImage");
    }

    // Always recommend memory for personalization
    if (this.canUseTool("manageMemory")) {
      recommendations.push("manageMemory");
    }

    return recommendations.filter((tool) => this.canUseTool(tool));
  }

  /**
   * Reset usage tracking for new conversation
   */
  resetConversationUsage(): void {
    this.usageTrackers.clear();
  }

  /**
   * Reset current request tool count
   */
  resetRequestUsage(): void {
    this.currentRequestToolCount = 0;
  }

  /**
   * Get tool health status
   */
  getToolHealth(): Record<string, { available: boolean; reason?: string }> {
    const availableTools = this.getAvailableTools();
    const health: Record<string, { available: boolean; reason?: string }> = {};

    Object.keys(this.tools).forEach((toolName) => {
      const isAvailable = toolName in availableTools;
      const canUse = this.canUseTool(toolName);

      health[toolName] = {
        available: isAvailable && canUse,
        reason: !isAvailable
          ? "Not available in current plan"
          : !canUse
            ? "Usage limit reached"
            : undefined,
      };
    });

    return health;
  }

  /**
   * Update service configuration
   */
  updateConfig(updates: Partial<BenjiConfig>): void {
    this.config = { ...this.config, ...updates };
    this.limits = this.calculateLimits();
  }
}

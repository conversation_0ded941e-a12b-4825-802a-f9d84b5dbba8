/**
 * Tool Usage Tracker - Tracks and manages tool usage limits and analytics
 *
 * Provides usage tracking, rate limiting, and analytics for AI tools
 * to ensure fair usage and prevent abuse.
 */

export interface ToolUsageRecord {
  toolName: string;
  userId: string;
  timestamp: Date;
  success: boolean;
  responseTime: number;
  tokensUsed?: number;
  errorMessage?: string;
  sessionId?: string;
}

export interface UsageStats {
  totalUsage: number;
  successRate: number;
  avgResponseTime: number;
  dailyUsage: number;
  monthlyUsage: number;
  lastUsed: Date;
  errorCount: number;
  mostUsedTool: string;
}

export interface RateLimitConfig {
  maxPerMinute: number;
  maxPerHour: number;
  maxPerDay: number;
  maxPerMonth: number;
  burstLimit: number;
}

export class ToolUsageTracker {
  private usageRecords: Map<string, ToolUsageRecord[]> = new Map();
  private rateLimits: Map<string, RateLimitConfig> = new Map();
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    this.initializeRateLimits();
    // Schedule automatic cleanup every hour
    this.cleanupInterval = setInterval(
      () => this.cleanupOldRecords(),
      60 * 60 * 1000
    );
  }

  /**
   * Initialize rate limits based on plan types
   */
  private initializeRateLimits(): void {
    // Reply Guy Plan
    this.rateLimits.set("reply-guy", {
      maxPerMinute: 5,
      maxPerHour: 50,
      maxPerDay: 200,
      maxPerMonth: 2000,
      burstLimit: 10,
    });

    // Pro Plan
    this.rateLimits.set("pro-plan", {
      maxPerMinute: 10,
      maxPerHour: 100,
      maxPerDay: 500,
      maxPerMonth: 5000,
      burstLimit: 20,
    });

    // Team Plan
    this.rateLimits.set("team-plan", {
      maxPerMinute: 20,
      maxPerHour: 200,
      maxPerDay: 1000,
      maxPerMonth: 10000,
      burstLimit: 50,
    });
  }

  /**
   * Record a tool usage event
   */
  recordUsage(record: ToolUsageRecord): void {
    const key = `${record.userId}:${record.toolName}`;

    if (!this.usageRecords.has(key)) {
      this.usageRecords.set(key, []);
    }

    const records = this.usageRecords.get(key)!;
    records.push(record);

    // Keep only last 1000 records per user-tool combination
    if (records.length > 1000) {
      records.splice(0, records.length - 1000);
    }

    console.log(
      `📊 UsageTracker: Recorded ${record.toolName} usage for user ${record.userId}`
    );
  }

  /**
   * Check if user is within rate limits
   */
  checkRateLimit(
    userId: string,
    toolName: string,
    userPlan: string
  ): {
    allowed: boolean;
    limitType?: string;
    retryAfter?: number;
    usage: {
      perMinute: number;
      perHour: number;
      perDay: number;
      perMonth: number;
    };
  } {
    const limits = this.rateLimits.get(userPlan);
    if (!limits) {
      return {
        allowed: false,
        limitType: "invalid_plan",
        usage: { perMinute: 0, perHour: 0, perDay: 0, perMonth: 0 },
      };
    }

    const key = `${userId}:${toolName}`;
    const records = this.usageRecords.get(key) || [];
    const now = new Date();

    // Calculate usage for different time windows
    const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const usage = {
      perMinute: records.filter((r) => r.timestamp >= oneMinuteAgo).length,
      perHour: records.filter((r) => r.timestamp >= oneHourAgo).length,
      perDay: records.filter((r) => r.timestamp >= oneDayAgo).length,
      perMonth: records.filter((r) => r.timestamp >= oneMonthAgo).length,
    };

    // Check each limit
    if (usage.perMinute >= limits.maxPerMinute) {
      return {
        allowed: false,
        limitType: "per_minute",
        retryAfter: 60,
        usage,
      };
    }

    if (usage.perHour >= limits.maxPerHour) {
      return {
        allowed: false,
        limitType: "per_hour",
        retryAfter: 3600,
        usage,
      };
    }

    if (usage.perDay >= limits.maxPerDay) {
      return {
        allowed: false,
        limitType: "per_day",
        retryAfter: 86400,
        usage,
      };
    }

    if (usage.perMonth >= limits.maxPerMonth) {
      return {
        allowed: false,
        limitType: "per_month",
        retryAfter: 30 * 86400,
        usage,
      };
    }

    return { allowed: true, usage };
  }

  /**
   * Get usage statistics for a user
   */
  getUserStats(userId: string): UsageStats {
    const userRecords = Array.from(this.usageRecords.entries())
      .filter(([key]) => key.startsWith(`${userId}:`))
      .flatMap(([, records]) => records);

    if (userRecords.length === 0) {
      return {
        totalUsage: 0,
        successRate: 0,
        avgResponseTime: 0,
        dailyUsage: 0,
        monthlyUsage: 0,
        lastUsed: new Date(0),
        errorCount: 0,
        mostUsedTool: "",
      };
    }

    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const successfulRecords = userRecords.filter((r) => r.success);
    const dailyRecords = userRecords.filter((r) => r.timestamp >= oneDayAgo);
    const monthlyRecords = userRecords.filter(
      (r) => r.timestamp >= oneMonthAgo
    );

    // Find most used tool
    const toolUsage = new Map<string, number>();
    userRecords.forEach((record) => {
      const count = toolUsage.get(record.toolName) || 0;
      toolUsage.set(record.toolName, count + 1);
    });

    const mostUsedTool =
      Array.from(toolUsage.entries()).sort((a, b) => b[1] - a[1])[0]?.[0] || "";

    return {
      totalUsage: userRecords.length,
      successRate: successfulRecords.length / userRecords.length,
      avgResponseTime:
        userRecords.reduce((sum, r) => sum + r.responseTime, 0) /
        userRecords.length,
      dailyUsage: dailyRecords.length,
      monthlyUsage: monthlyRecords.length,
      lastUsed: new Date(
        Math.max(...userRecords.map((r) => r.timestamp.getTime()))
      ),
      errorCount: userRecords.filter((r) => !r.success).length,
      mostUsedTool,
    };
  }

  /**
   * Get tool-specific statistics
   */
  getToolStats(toolName: string): {
    totalUsage: number;
    uniqueUsers: number;
    avgResponseTime: number;
    successRate: number;
    peakUsageHour: number;
    errorRate: number;
  } {
    const toolRecords = Array.from(this.usageRecords.entries())
      .filter(([key]) => key.endsWith(`:${toolName}`))
      .flatMap(([, records]) => records);

    if (toolRecords.length === 0) {
      return {
        totalUsage: 0,
        uniqueUsers: 0,
        avgResponseTime: 0,
        successRate: 0,
        peakUsageHour: 0,
        errorRate: 0,
      };
    }

    const uniqueUsers = new Set(toolRecords.map((r) => r.userId)).size;
    const successfulRecords = toolRecords.filter((r) => r.success);

    // Calculate peak usage hour
    const hourlyUsage = new Map<number, number>();
    toolRecords.forEach((record) => {
      const hour = record.timestamp.getHours();
      hourlyUsage.set(hour, (hourlyUsage.get(hour) || 0) + 1);
    });

    const peakUsageHour =
      Array.from(hourlyUsage.entries()).sort((a, b) => b[1] - a[1])[0]?.[0] ||
      0;

    return {
      totalUsage: toolRecords.length,
      uniqueUsers,
      avgResponseTime:
        toolRecords.reduce((sum, r) => sum + r.responseTime, 0) /
        toolRecords.length,
      successRate: successfulRecords.length / toolRecords.length,
      peakUsageHour,
      errorRate:
        (toolRecords.length - successfulRecords.length) / toolRecords.length,
    };
  }

  /**
   * Clean up old records to prevent memory leaks
   */
  cleanupOldRecords(): void {
    const oneMonthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    this.usageRecords.forEach((records, key) => {
      const recentRecords = records.filter((r) => r.timestamp >= oneMonthAgo);
      if (recentRecords.length !== records.length) {
        this.usageRecords.set(key, recentRecords);
        console.log(`🧹 UsageTracker: Cleaned up old records for ${key}`);
      }
    });
  }

  /**
   * Get rate limit configuration for a plan
   */
  getRateLimitConfig(userPlan: string): RateLimitConfig | null {
    return this.rateLimits.get(userPlan) || null;
  }

  /**
   * Update rate limits (for admin purposes)
   */
  updateRateLimits(userPlan: string, config: RateLimitConfig): void {
    this.rateLimits.set(userPlan, config);
    console.log(`🔧 UsageTracker: Updated rate limits for ${userPlan}`);
  }

  /**
   * Get current usage for a user across all tools
   */
  getCurrentUsage(userId: string): Record<
    string,
    {
      perMinute: number;
      perHour: number;
      perDay: number;
      perMonth: number;
    }
  > {
    const result: Record<string, any> = {};

    const userKeys = Array.from(this.usageRecords.keys()).filter((key) =>
      key.startsWith(`${userId}:`)
    );

    userKeys.forEach((key) => {
      const toolName = key.split(":")[1];
      const records = this.usageRecords.get(key) || [];
      const now = new Date();

      const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      result[toolName] = {
        perMinute: records.filter((r) => r.timestamp >= oneMinuteAgo).length,
        perHour: records.filter((r) => r.timestamp >= oneHourAgo).length,
        perDay: records.filter((r) => r.timestamp >= oneDayAgo).length,
        perMonth: records.filter((r) => r.timestamp >= oneMonthAgo).length,
      };
    });

    return result;
  }

  /**
   * Export usage data for analytics
   */
  exportUsageData(userId?: string): ToolUsageRecord[] {
    if (userId) {
      return Array.from(this.usageRecords.entries())
        .filter(([key]) => key.startsWith(`${userId}:`))
        .flatMap(([, records]) => records);
    }

    return Array.from(this.usageRecords.values()).flat();
  }

  /**
   * Clean up resources and stop timers
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.usageRecords.clear();
    this.rateLimits.clear();
  }
}

/**
 * Tool Registry - Central registry for all AI tools
 *
 * Provides a unified interface for registering, managing, and executing
 * AI tools with proper error handling and usage tracking.
 */

import { exaSearchTool } from "../../../tools/exa-search";
import { mem0MemoryTool } from "../../../tools/mem0-memory";
import { imageGenerationTool } from "../../../tools/openai-image";
import { xaiSearchTool } from "../../../tools/xai-search";

export interface ToolMetadata {
  name: string;
  description: string;
  category: "search" | "generation" | "memory" | "analysis";
  planRequired: "reply-guy" | "pro-plan" | "team-plan";
  maxUsagePerConversation: number;
  avgResponseTime: number;
  successRate: number;
}

export interface ToolWrapper {
  metadata: ToolMetadata;
  tool: any;
  execute: (params: any) => Promise<any>;
  validateParams: (params: any) => boolean;
}

export class ToolRegistry {
  private tools: Map<string, ToolWrapper> = new Map();
  private static instance: ToolRegistry;

  private constructor() {
    this.initializeTools();
  }

  static getInstance(): ToolRegistry {
    if (!ToolRegistry.instance) {
      ToolRegistry.instance = new ToolRegistry();
    }
    return ToolRegistry.instance;
  }

  /**
   * Initialize all available tools
   */
  private initializeTools(): void {
    // Web Search Tool
    this.registerTool({
      metadata: {
        name: "searchWeb",
        description: "Real-time web search for current information",
        category: "search",
        planRequired: "reply-guy",
        maxUsagePerConversation: 3,
        avgResponseTime: 2000,
        successRate: 0.95,
      },
      tool: xaiSearchTool,
      execute: async (params) => {
        const startTime = Date.now();
        try {
          const result = await xaiSearchTool.execute(params, {
            toolCallId: crypto.randomUUID(),
            messages: [],
          });
          return {
            success: true,
            data: result,
            responseTime: Date.now() - startTime,
          };
        } catch (error) {
          return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error",
            responseTime: Date.now() - startTime,
          };
        }
      },
      validateParams: (params) => {
        return (
          params && typeof params.query === "string" && params.query.length > 0
        );
      },
    });

    // Knowledge Search Tool
    this.registerTool({
      metadata: {
        name: "searchKnowledge",
        description: "Semantic search for in-depth knowledge and research",
        category: "search",
        planRequired: "reply-guy",
        maxUsagePerConversation: 3,
        avgResponseTime: 1500,
        successRate: 0.92,
      },
      tool: exaSearchTool,
      execute: async (params) => {
        const startTime = Date.now();
        try {
          const result = await exaSearchTool.execute(params, {
            toolCallId: crypto.randomUUID(),
            messages: [],
          });
          return {
            success: true,
            data: result,
            responseTime: Date.now() - startTime,
          };
        } catch (error) {
          return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error",
            responseTime: Date.now() - startTime,
          };
        }
      },
      validateParams: (params) => {
        return (
          params && typeof params.query === "string" && params.query.length > 0
        );
      },
    });

    // Image Generation Tool
    this.registerTool({
      metadata: {
        name: "generateImage",
        description: "AI-powered image generation with DALL-E",
        category: "generation",
        planRequired: "pro-plan",
        maxUsagePerConversation: 2,
        avgResponseTime: 8000,
        successRate: 0.88,
      },
      tool: imageGenerationTool,
      execute: async (params) => {
        const startTime = Date.now();
        try {
          const result = await imageGenerationTool.execute(params, {
            toolCallId: crypto.randomUUID(),
            messages: [],
          });
          return {
            success: true,
            data: result,
            responseTime: Date.now() - startTime,
          };
        } catch (error) {
          return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error",
            responseTime: Date.now() - startTime,
          };
        }
      },
      validateParams: (params) => {
        return (
          params &&
          typeof params.prompt === "string" &&
          params.prompt.length > 0
        );
      },
    });

    // Memory Management Tool
    this.registerTool({
      metadata: {
        name: "manageMemory",
        description: "Store and retrieve conversation memories",
        category: "memory",
        planRequired: "reply-guy",
        maxUsagePerConversation: 5,
        avgResponseTime: 1000,
        successRate: 0.97,
      },
      tool: mem0MemoryTool,
      execute: async (params) => {
        const startTime = Date.now();
        try {
          const result = await mem0MemoryTool.execute(params, {
            toolCallId: crypto.randomUUID(),
            messages: [],
          });
          return {
            success: true,
            data: result,
            responseTime: Date.now() - startTime,
          };
        } catch (error) {
          return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error",
            responseTime: Date.now() - startTime,
          };
        }
      },
      validateParams: (params) => {
        return (
          params && (params.action === "store" || params.action === "retrieve")
        );
      },
    });
  }

  /**
   * Register a new tool
   */
  registerTool(wrapper: ToolWrapper): void {
    this.tools.set(wrapper.metadata.name, wrapper);
  }

  /**
   * Get a tool by name
   */
  getTool(name: string): ToolWrapper | undefined {
    return this.tools.get(name);
  }

  /**
   * Get all tools
   */
  getAllTools(): ToolWrapper[] {
    return Array.from(this.tools.values());
  }

  /**
   * Get tools by category
   */
  getToolsByCategory(category: ToolMetadata["category"]): ToolWrapper[] {
    return this.getAllTools().filter(
      (tool) => tool.metadata.category === category
    );
  }

  /**
   * Get tools available for a specific plan
   */
  getToolsForPlan(plan: string): ToolWrapper[] {
    const planHierarchy = {
      "reply-guy": 1,
      "pro-plan": 2,
      "team-plan": 3,
    };

    const userPlanLevel =
      planHierarchy[plan as keyof typeof planHierarchy] || 0;

    return this.getAllTools().filter((tool) => {
      const toolPlanLevel = planHierarchy[tool.metadata.planRequired] || 0;
      return userPlanLevel >= toolPlanLevel;
    });
  }

  /**
   * Execute a tool with error handling and tracking
   */
  async executeTool(
    toolName: string,
    params: any,
    options: {
      timeout?: number;
      retries?: number;
      trackUsage?: boolean;
    } = {}
  ): Promise<any> {
    const tool = this.getTool(toolName);
    if (!tool) {
      throw new Error(`Tool not found: ${toolName}`);
    }

    // Validate parameters
    if (!tool.validateParams(params)) {
      throw new Error(`Invalid parameters for tool: ${toolName}`);
    }

    const { timeout = 30000, retries = 1, trackUsage = true } = options;

    let lastError: Error | null = null;

    for (let attempt = 0; attempt < retries; attempt++) {
      try {
        console.log(
          `🔧 ToolRegistry: Executing ${toolName} (attempt ${attempt + 1}/${retries})`
        );

        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(
            () => reject(new Error(`Tool execution timeout: ${toolName}`)),
            timeout
          );
        });

        const result = await Promise.race([
          tool.execute(params),
          timeoutPromise,
        ]);

        if (trackUsage) {
          console.log(`✅ ToolRegistry: ${toolName} executed successfully`);
        }

        return result;
      } catch (error) {
        lastError =
          error instanceof Error
            ? error
            : new Error(`Unknown error in ${toolName}`);
        console.warn(
          `⚠️ ToolRegistry: ${toolName} failed (attempt ${attempt + 1}/${retries}):`,
          lastError.message
        );

        if (attempt < retries - 1) {
          // Wait before retry
          await new Promise((resolve) =>
            setTimeout(resolve, 1000 * (attempt + 1))
          );
        }
      }
    }

    throw (
      lastError ||
      new Error(`Tool execution failed after ${retries} attempts: ${toolName}`)
    );
  }

  /**
   * Get tool statistics
   */
  getToolStats(): Record<string, ToolMetadata> {
    const stats: Record<string, ToolMetadata> = {};

    this.getAllTools().forEach((tool) => {
      stats[tool.metadata.name] = { ...tool.metadata };
    });

    return stats;
  }

  /**
   * Check if a tool is available
   */
  isToolAvailable(toolName: string, userPlan: string): boolean {
    const tool = this.getTool(toolName);
    if (!tool) return false;

    const availableTools = this.getToolsForPlan(userPlan);
    return availableTools.some((t) => t.metadata.name === toolName);
  }

  /**
   * Get tool health check
   */
  async healthCheck(): Promise<
    Record<string, { healthy: boolean; latency?: number; error?: string }>
  > {
    const health: Record<
      string,
      { healthy: boolean; latency?: number; error?: string }
    > = {};

    const healthChecks = this.getAllTools().map(async (tool) => {
      try {
        const startTime = Date.now();

        // Simple health check - try to validate with dummy params
        const isValid = tool.validateParams({
          query: "test",
          action: "test",
          prompt: "test",
        });
        const latency = Date.now() - startTime;

        health[tool.metadata.name] = {
          healthy: isValid,
          latency,
        };
      } catch (error) {
        health[tool.metadata.name] = {
          healthy: false,
          error: error instanceof Error ? error.message : "Unknown error",
        };
      }
    });

    await Promise.all(healthChecks);
    return health;
  }
}

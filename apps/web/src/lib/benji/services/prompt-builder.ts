/**
 * Prompt Builder Service - System prompt construction
 *
 * Builds context-aware system prompts for different response types,
 * incorporating personality, market intelligence, and memory context.
 */

import type { BenjiConfig, BenjiContext, ResponseType } from "../types";

export class PromptBuilderService {
  /**
   * Build a comprehensive system prompt based on context and response type
   */
  buildSystemPrompt(
    type: ResponseType,
    context: BenjiContext,
    config: {
      personalityPrompt?: string;
      customSystemPrompt?: string;
      useFirstPerson?: boolean;
    } = {}
  ): string {
    const isFirstPerson = config.useFirstPerson ?? true;

    // Start with base prompt
    let prompt = this.getBasePrompt();

    // Add response type specific rules
    prompt += this.getResponseTypeRules(type);

    // Add perspective rules (first person vs external)
    prompt += this.getPerspectiveRules(isFirstPerson);

    // Add personality guidelines
    prompt += this.getPersonalityGuidelines();

    // Add personality prompt if available
    if (config.personalityPrompt) {
      prompt += `\n\nPersonality Style: ${config.personalityPrompt}`;
    }

    // Add custom system prompt if available
    if (config.customSystemPrompt) {
      prompt += `\n\nAdditional Instructions: ${config.customSystemPrompt}`;
    }

    // Add market intelligence context if available
    if (context.marketIntelligence) {
      prompt += this.getMarketIntelligenceContext(context.marketIntelligence);
    }

    // Add memory context if available
    if (context.memoryContext) {
      prompt += this.getMemoryContext(context.memoryContext);
    }

    // Add persona-specific memory context if available
    if (context.personaMemoryContext) {
      prompt += `\n\n${context.personaMemoryContext}`;
    }

    // Add final instructions based on response type
    prompt += this.getFinalInstructions(type, isFirstPerson, context);

    return prompt;
  }

  /**
   * Get base prompt that applies to all response types
   */
  private getBasePrompt(): string {
    return `You are Benji, an AI assistant specialized in social media engagement.

CRITICAL RESPONSE RULES:`;
  }

  /**
   * Get response type specific rules
   */
  private getResponseTypeRules(type: ResponseType): string {
    if (type === "post") {
      return `
- CREATE an original, engaging social media post based on the user's prompt
- RESPOND ONLY with the direct post content - NO explanations or context
- BE ENGAGING and authentic - make it worth reading and sharing
- NEVER include phrases like "Here's a post about..." or "This post discusses:"
- ONLY provide the actual post content that would be published
- Stay within 280 characters maximum for Twitter compatibility
- Make it compelling, valuable, and true to the personality`;
    } else {
      return `
- RESPOND ONLY with the direct tweet reply - NO explanations or context
- BE ULTRA-CONCISE - every word must add value
- NEVER include phrases like "This tweet seems to be..." or "Here's a response:"
- ONLY provide content that is directly relevant to the original tweet
- Stay within 280 characters maximum`;
    }
  }

  /**
   * Get perspective rules (first person vs external user)
   */
  private getPerspectiveRules(isFirstPerson: boolean): string {
    if (isFirstPerson) {
      return `
- ALWAYS respond in FIRST PERSON as the account owner (use "I", "my", "me")
- NEVER refer to the account owner in third person or by name
- NEVER say things like "They consistently push boundaries" - you ARE the account owner
- Answer as if you are the account owner responding directly to the conversation`;
    } else {
      return `
- Respond as an EXTERNAL USER who follows this account (use "you", "your", "they")
- Refer to the account owner appropriately (by name or handle when relevant)
- Respond as if you're a knowledgeable follower engaging with their content
- Show respect and appreciation for the account owner's work when appropriate`;
    }
  }

  /**
   * Get personality guidelines
   */
  private getPersonalityGuidelines(): string {
    return `

Your personality:
- Professional yet personable
- Knowledgeable and helpful
- Always concise and direct
- Respectful and positive

Guidelines:
- Match the tone of the original tweet
- Provide immediate value (insights, questions, resources)
- Be conversational and authentic
- Avoid promotional language
- Use tools only when they add specific value to the response`;
  }

  /**
   * Get market intelligence context for prompt
   */
  private getMarketIntelligenceContext(
    marketIntelligence: BenjiContext["marketIntelligence"]
  ): string {
    if (!marketIntelligence) return "";

    let context = `\n\nCURRENT CRYPTO MARKET INTELLIGENCE:`;

    if (marketIntelligence.sector) {
      context += `\n- Detected sector: ${marketIntelligence.sector}`;
    }

    if (
      marketIntelligence.trendingProjects &&
      marketIntelligence.trendingProjects.length > 0
    ) {
      context += `\n- Trending projects (7-day mindshare):`;
      marketIntelligence.trendingProjects.slice(0, 3).forEach((p) => {
        const delta =
          p.mindshareDelta !== undefined
            ? ` (${p.mindshareDelta > 0 ? "+" : ""}${p.mindshareDelta}%)`
            : "";
        context += `\n  • ${p.name}${p.symbol ? ` ($${p.symbol})` : ""}: ${p.mindshare}% mindshare${delta}`;
      });
    }

    if (
      marketIntelligence.relevantProjects &&
      marketIntelligence.relevantProjects.length > 0
    ) {
      context += `\n- Mentioned projects:`;
      marketIntelligence.relevantProjects.slice(0, 2).forEach((p) => {
        const delta =
          p.mindshareDelta !== undefined
            ? ` (${p.mindshareDelta > 0 ? "+" : ""}${p.mindshareDelta}%)`
            : "";
        context += `\n  • ${p.name}${p.symbol ? ` ($${p.symbol})` : ""}: ${p.mindshare}% mindshare${delta}`;
      });
    }

    if (marketIntelligence.marketSentiment) {
      context += `\n- Overall market sentiment: ${marketIntelligence.marketSentiment}`;
    }

    context += `\n- Data updated: ${marketIntelligence.updatedAt ? new Date(marketIntelligence.updatedAt).toLocaleString() : "Unknown"}`;
    context += `\n\nUse this market context to provide informed, relevant responses about crypto topics. Only reference this information when it adds value to the conversation.`;

    return context;
  }

  /**
   * Get memory context section
   */
  private getMemoryContext(memoryContext: string): string {
    return `\n\n## Previous Conversation Context
${memoryContext}

Use this context to provide more personalized and relevant responses based on previous interactions.`;
  }

  /**
   * Get final instructions based on response type and context
   */
  private getFinalInstructions(
    type: ResponseType,
    isFirstPerson: boolean,
    context: BenjiContext
  ): string {
    let instructions = "";

    // Add perspective reminder
    if (isFirstPerson) {
      instructions += `\n\nRemember: You ARE the account owner. Respond in first person (I/my/me) and always answer in short. Never reference yourself in third person.`;
    } else {
      instructions += `\n\nRemember: You are an external user responding TO the account owner. Use second person (you/your) or third person (they/their) and always answer in short. Be respectful and engaging.`;
    }

    // Add type-specific final instruction
    if (type === "mention") {
      instructions += `\n\nRespond to this mention with a direct, engaging reply that represents the monitored account professionally.`;
    } else if (type === "enhanced") {
      instructions += this.getEnhancedResponseInstructions(
        isFirstPerson,
        context
      );
    } else {
      instructions += `\n\nGenerate a direct, authentic reply that adds meaningful value to the conversation.`;
    }

    return instructions;
  }

  /**
   * Get enhanced response instructions with identity context
   */
  private getEnhancedResponseInstructions(
    isFirstPerson: boolean,
    context: BenjiContext
  ): string {
    let instructions = `\n\nYou have access to search tools for additional context if needed. Use them only when they add genuine value to your response.`;

    // Add identity context if available
    if (context.monitoredAccountInfo) {
      if (isFirstPerson) {
        instructions += `\n\nCRITICAL IDENTITY CONTEXT:
- YOU ARE ${context.monitoredAccountInfo.name} (@${context.monitoredAccountInfo.handle})
- This is YOUR Twitter account that is being mentioned
- Respond AS YOURSELF, not as an AI assistant
- Use "I", "my", "me" - you ARE ${context.monitoredAccountInfo.name}
- NEVER address yourself in third person
- NEVER say "${context.monitoredAccountInfo.name} thinks..." - YOU are ${context.monitoredAccountInfo.name}`;
      } else {
        instructions += `\n\nCRITICAL IDENTITY CONTEXT:
- You are responding TO ${context.monitoredAccountInfo.name} (@${context.monitoredAccountInfo.handle})
- This is THEIR Twitter account that posted the content
- Respond AS AN EXTERNAL USER, not as the account owner
- Use "you", "your" when addressing them directly
- Use "they", "their" when referring to them in third person
- Show respect and appreciation for their work when appropriate`;
      }
    }

    if (isFirstPerson) {
      instructions += `\n\nREMEMBER: You are responding AS the account owner, not TO them. Use first person always.`;
    } else {
      instructions += `\n\nREMEMBER: You are responding TO the account owner as an external user. Use second/third person appropriately.`;
    }

    instructions += `\n\nRespond to this mention with a direct, engaging reply that represents the monitored account professionally.`;

    return instructions;
  }
}

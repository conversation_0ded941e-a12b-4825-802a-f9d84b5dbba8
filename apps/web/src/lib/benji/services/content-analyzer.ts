/**
 * Content Analyzer Service - AI-powered content analysis
 *
 * Handles sentiment analysis, importance scoring, keyword extraction,
 * and comprehensive content analysis for tweets and social media content.
 */

import { streamText } from "ai";
import { getModel, type ModelName } from "../../ai-providers";
import type { FullAnalysisResult } from "../types";

export class ContentAnalyzerService {
  private model: ModelName;

  constructor(model: ModelName = "gemini25Flash") {
    this.model = model;
  }

  /**
   * Calculate bullish score for a tweet (1-100)
   *
   * Score ranges:
   * - 1-20: Very negative, bearish, pessimistic
   * - 21-40: Somewhat negative, skeptical
   * - 41-60: Neutral, mixed sentiment
   * - 61-80: Positive, optimistic
   * - 81-100: Very positive, bullish, enthusiastic
   */
  async calculateBullishScore(content: string): Promise<number> {
    const model = getModel(this.model);

    const result = streamText({
      model,
      messages: [
        {
          role: "system",
          content: `You are a sentiment analysis expert. Analyze the sentiment and positivity of tweets and return a "bullish score" from 1-100 where:
          - 1-20: Very negative, bearish, pessimistic
          - 21-40: Somewhat negative, skeptical
          - 41-60: Neutral, mixed sentiment
          - 61-80: Positive, optimistic
          - 81-100: Very positive, bullish, enthusiastic
          
          Respond with ONLY a number between 1-100.`,
        },
        {
          role: "user",
          content: `Analyze this tweet and give it a bullish score: "${content}"`,
        },
      ],
      maxTokens: 10,
      temperature: 0.3,
    });

    // Extract number from response
    let text = "";
    for await (const chunk of result.textStream) {
      text += chunk;
    }

    const score = parseInt(text.trim().replace(/\D/g, ""));
    return Math.max(1, Math.min(100, score || 50)); // Default to 50 if parsing fails
  }

  /**
   * Calculate importance score for a tweet (1-100)
   *
   * Score ranges:
   * - 1-20: Spam, low-value, irrelevant content
   * - 21-40: Personal posts, casual mentions with little engagement potential
   * - 41-60: Standard social media content, moderate engagement potential
   * - 61-80: Valuable content, questions, industry insights, good engagement opportunity
   * - 81-100: High-value content, trending topics, influential discussions, urgent responses needed
   */
  async calculateImportanceScore(
    content: string,
    authorInfo?: {
      followers?: number;
      verified?: boolean;
      handle?: string;
    }
  ): Promise<number> {
    const model = getModel(this.model);

    // Build context about the author if available
    let authorContext = "";
    if (authorInfo) {
      const followerText = authorInfo.followers
        ? ` with ${authorInfo.followers.toLocaleString()} followers`
        : "";
      const verifiedText = authorInfo.verified ? " (verified account)" : "";
      authorContext = `\nAuthor: @${authorInfo.handle || "unknown"}${followerText}${verifiedText}`;
    }

    const result = streamText({
      model,
      messages: [
        {
          role: "system",
          content: `You are a social media engagement expert. Analyze tweets and return an "importance score" from 1-100 where:
          - 1-20: Spam, low-value, irrelevant content
          - 21-40: Personal posts, casual mentions with little engagement potential
          - 41-60: Standard social media content, moderate engagement potential
          - 61-80: Valuable content, questions, industry insights, good engagement opportunity
          - 81-100: High-value content, trending topics, influential discussions, urgent responses needed
          
          Consider factors like:
          - Content depth and value
          - Engagement potential (questions, requests, discussions)
          - Author influence and follower count
          - Relevance to business/professional interests
          - Urgency of response needed
          - Opportunity for meaningful conversation
          
          Respond with ONLY a number between 1-100.`,
        },
        {
          role: "user",
          content: `Analyze this tweet and give it an importance score: "${content}"${authorContext}`,
        },
      ],
      maxTokens: 10,
      temperature: 0.3,
    });

    // Extract number from response
    let text = "";
    for await (const chunk of result.textStream) {
      text += chunk;
    }

    const score = parseInt(text.trim().replace(/\D/g, ""));
    return Math.max(1, Math.min(100, score || 50)); // Default to 50 if parsing fails
  }

  /**
   * Extract AI-enhanced keywords from tweet content
   * Returns 3-7 keywords that capture the essence of the content
   */
  async extractEnhancedKeywords(content: string): Promise<string[]> {
    const model = getModel(this.model);

    const result = streamText({
      model,
      messages: [
        {
          role: "system",
          content: `You are a content analysis expert. Extract the most important keywords and topics from tweets for categorization and search.
          
          Focus on:
          - Main topics and subjects
          - Industry terms and technical concepts
          - Product names and brand mentions
          - Actionable items and intents
          - Emotional context keywords
          
          Return 3-7 keywords maximum, separated by commas.
          Exclude common words like: the, and, or, but, this, that, have, will, would.
          Prioritize specific, meaningful terms that capture the tweet's essence.
          
          Example: "AI, machine learning, startup, innovation, feedback"`,
        },
        {
          role: "user",
          content: `Extract keywords from this tweet: "${content}"`,
        },
      ],
      maxTokens: 50,
      temperature: 0.3,
    });

    // Extract keywords from response
    let text = "";
    for await (const chunk of result.textStream) {
      text += chunk;
    }

    // Parse comma-separated keywords and clean them
    const keywords = text
      .split(",")
      .map((keyword) => keyword.trim().toLowerCase())
      .filter((keyword) => keyword.length > 2 && keyword.length < 30)
      .slice(0, 7); // Limit to 7 keywords max

    return keywords.length > 0 ? keywords : ["general"]; // Fallback if no keywords extracted
  }

  /**
   * Perform comprehensive AI analysis of a tweet
   * Combines bullish score, importance score, and keyword extraction
   */
  async performFullAnalysis(
    content: string,
    authorInfo?: {
      name?: string;
      handle?: string;
      followers?: number;
      verified?: boolean;
      avatarUrl?: string;
    }
  ): Promise<FullAnalysisResult> {
    const startTime = Date.now();

    try {
      console.log(
        "🔍 ContentAnalyzer: Starting full analysis for tweet content:",
        content.substring(0, 100) + "..."
      );

      // Run analysis in parallel for better performance
      const [bullishScore, importanceScore, keywords] = await Promise.all([
        this.calculateBullishScore(content),
        this.calculateImportanceScore(content, authorInfo),
        this.extractEnhancedKeywords(content),
      ]);

      // Determine sentiment category based on bullish score
      let sentiment: string;
      if (bullishScore >= 81) sentiment = "very_positive";
      else if (bullishScore >= 61) sentiment = "positive";
      else if (bullishScore >= 41) sentiment = "neutral";
      else if (bullishScore >= 21) sentiment = "negative";
      else sentiment = "very_negative";

      // Determine priority based on importance score
      let priority: string;
      if (importanceScore >= 81) priority = "urgent";
      else if (importanceScore >= 61) priority = "high";
      else if (importanceScore >= 41) priority = "medium";
      else if (importanceScore >= 21) priority = "low";
      else priority = "ignore";

      // Determine recommended action
      let recommendedAction: string;
      if (importanceScore >= 81) {
        recommendedAction = "respond_immediately";
      } else if (importanceScore >= 61 && bullishScore >= 61) {
        recommendedAction = "engage_positively";
      } else if (importanceScore >= 61 && bullishScore <= 40) {
        recommendedAction = "address_concerns";
      } else if (importanceScore >= 41) {
        recommendedAction = "monitor_and_consider";
      } else {
        recommendedAction = "archive_or_ignore";
      }

      // Calculate confidence based on content length and author info availability
      let confidence = 0.7; // Base confidence
      if (content.length > 50) confidence += 0.1; // Longer content = more context
      if (content.length > 100) confidence += 0.1;
      if (authorInfo?.followers && authorInfo.followers > 1000)
        confidence += 0.05;
      if (authorInfo?.verified) confidence += 0.05;
      confidence = Math.min(0.95, confidence); // Cap at 95%

      const processingTime = Date.now() - startTime;

      console.log("✅ ContentAnalyzer: Full analysis completed:", {
        bullishScore,
        importanceScore,
        keywordCount: keywords.length,
        sentiment,
        priority,
        processingTime,
      });

      return {
        bullishScore,
        importanceScore,
        keywords,
        analysisData: {
          sentiment,
          priority,
          recommendedAction,
          confidence,
          processingTime,
        },
      };
    } catch (error) {
      console.error("❌ ContentAnalyzer: Full analysis failed:", error);

      // Return fallback analysis if AI fails
      const processingTime = Date.now() - startTime;
      return {
        bullishScore: 50,
        importanceScore: 50,
        keywords: ["general"],
        analysisData: {
          sentiment: "neutral",
          priority: "medium",
          recommendedAction: "monitor_and_consider",
          confidence: 0.3, // Low confidence for fallback
          processingTime,
        },
      };
    }
  }
}

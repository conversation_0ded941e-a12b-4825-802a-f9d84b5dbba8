/**
 * Persona Analyzer Service - Twitter personality analysis
 *
 * Analyzes Twitter user behavior to extract writing style, personality traits,
 * topics of interest, and engagement patterns for creating AI personas.
 */

import { streamText } from "ai";
import { getModel, type ModelName } from "../../ai-providers";
import type {
  EngagementPatternsAnalysis,
  PersonaAnalysis,
  PersonalityTraitsAnalysis,
  TopicsOfInterestAnalysis,
  WritingStyleAnalysis,
} from "../types";

export class PersonaAnalyzerService {
  private model: ModelName;

  constructor(model: ModelName = "gemini25Flash") {
    this.model = model;
  }

  /**
   * Analyze writing style from tweet content
   */
  async analyzeWritingStyle(tweets: string[]): Promise<WritingStyleAnalysis> {
    const model = getModel(this.model);
    const sampleText = tweets.slice(0, 20).join("\n\n"); // Analyze first 20 tweets

    const result = streamText({
      model,
      messages: [
        {
          role: "system",
          content: `You are an expert in writing style analysis. Analyze the writing style of the provided tweets and return a JSON object with these exact fields:

          {
            "tone": "professional|casual|friendly|authoritative|playful|serious",
            "formality": "very_formal|formal|semi_formal|casual|very_casual",
            "humor": "none|subtle|moderate|frequent|heavy",
            "emotionalRange": "reserved|balanced|expressive|very_expressive",
            "vocabulary": "simple|conversational|technical|academic|creative"
          }

          Base your analysis on patterns in word choice, sentence structure, emoji usage, and overall communication style.
          Respond with ONLY the JSON object, no additional text.`,
        },
        {
          role: "user",
          content: `Analyze the writing style of these tweets:\n\n${sampleText}`,
        },
      ],
      maxTokens: 200,
      temperature: 0.3,
    });

    let text = "";
    for await (const chunk of result.textStream) {
      text += chunk;
    }

    try {
      return JSON.parse(text.trim());
    } catch (error) {
      console.error("Failed to parse writing style analysis:", error);
      return {
        tone: "casual",
        formality: "casual",
        humor: "moderate",
        emotionalRange: "balanced",
        vocabulary: "conversational",
      };
    }
  }

  /**
   * Extract personality traits from tweet content
   */
  async extractPersonalityTraits(
    tweets: string[]
  ): Promise<PersonalityTraitsAnalysis> {
    const model = getModel(this.model);
    const sampleText = tweets.slice(0, 30).join("\n\n"); // Analyze first 30 tweets

    const result = streamText({
      model,
      messages: [
        {
          role: "system",
          content: `You are a personality analysis expert. Analyze the personality traits evident in the provided tweets and return a JSON object with these exact fields:

          {
            "primary": ["trait1", "trait2", "trait3"],
            "secondary": ["trait4", "trait5"],
            "communication": ["style1", "style2", "style3"]
          }

          Primary traits: 3 most dominant personality characteristics (e.g., "analytical", "optimistic", "helpful", "curious", "direct", "empathetic")
          Secondary traits: 2 supporting personality characteristics
          Communication traits: 3 communication style characteristics (e.g., "clear", "concise", "supportive", "questioning", "encouraging")

          Respond with ONLY the JSON object, no additional text.`,
        },
        {
          role: "user",
          content: `Analyze the personality traits evident in these tweets:\n\n${sampleText}`,
        },
      ],
      maxTokens: 300,
      temperature: 0.3,
    });

    let text = "";
    for await (const chunk of result.textStream) {
      text += chunk;
    }

    try {
      return JSON.parse(text.trim());
    } catch (error) {
      console.error("Failed to parse personality traits analysis:", error);
      return {
        primary: ["helpful", "analytical", "curious"],
        secondary: ["direct", "supportive"],
        communication: ["clear", "concise", "engaging"],
      };
    }
  }

  /**
   * Identify topics of interest from tweet content
   */
  async identifyTopicsOfInterest(
    tweets: string[]
  ): Promise<TopicsOfInterestAnalysis> {
    const model = getModel(this.model);
    const allText = tweets.join("\n\n");

    const result = streamText({
      model,
      messages: [
        {
          role: "system",
          content: `You are a content analysis expert. Analyze the topics and interests evident in the provided tweets and return a JSON object with these exact fields:

          {
            "primary": ["topic1", "topic2", "topic3"],
            "secondary": ["topic4", "topic5", "topic6"],
            "expertise": ["area1", "area2"]
          }

          Primary topics: 3 most frequently discussed or passionate topics
          Secondary topics: 3-6 regularly mentioned topics
          Expertise areas: 2 areas where the person demonstrates knowledge or authority

          Focus on concrete topics like "technology", "startups", "AI", "crypto", "marketing", "design", etc.
          Respond with ONLY the JSON object, no additional text.`,
        },
        {
          role: "user",
          content: `Identify the topics of interest from these tweets:\n\n${allText.substring(0, 8000)}`, // Limit to avoid token limits
        },
      ],
      maxTokens: 300,
      temperature: 0.3,
    });

    let text = "";
    for await (const chunk of result.textStream) {
      text += chunk;
    }

    try {
      return JSON.parse(text.trim());
    } catch (error) {
      console.error("Failed to parse topics analysis:", error);
      return {
        primary: ["technology", "business", "innovation"],
        secondary: ["productivity", "startups", "AI"],
        expertise: ["software", "entrepreneurship"],
      };
    }
  }

  /**
   * Analyze engagement patterns from replies and interactions
   */
  async analyzeEngagementPatterns(
    replies: string[]
  ): Promise<EngagementPatternsAnalysis> {
    const model = getModel(this.model);
    const sampleReplies = replies.slice(0, 25).join("\n\n"); // Analyze first 25 replies

    const result = streamText({
      model,
      messages: [
        {
          role: "system",
          content: `You are an engagement pattern analyst. Analyze how this person engages with others on social media and return a JSON object with these exact fields:

          {
            "replyStyle": "brief|detailed|conversational|formal|supportive",
            "questionAsking": "rare|occasional|frequent|very_frequent",
            "supportiveness": "low|moderate|high|very_high"
          }

          Base your analysis on:
          - Reply length and depth
          - How often they ask questions
          - How supportive/encouraging they are
          - Their interaction style with others

          Respond with ONLY the JSON object, no additional text.`,
        },
        {
          role: "user",
          content: `Analyze the engagement patterns from these replies:\n\n${sampleReplies}`,
        },
      ],
      maxTokens: 200,
      temperature: 0.3,
    });

    let text = "";
    for await (const chunk of result.textStream) {
      text += chunk;
    }

    try {
      return JSON.parse(text.trim());
    } catch (error) {
      console.error("Failed to parse engagement patterns analysis:", error);
      return {
        replyStyle: "conversational",
        questionAsking: "frequent",
        supportiveness: "high",
      };
    }
  }

  /**
   * Generate a comprehensive system prompt from personality analysis
   */
  async generatePersonaSystemPrompt(
    twitterHandle: string,
    analysis: PersonaAnalysis
  ): Promise<string> {
    const model = getModel(this.model);

    const result = streamText({
      model,
      messages: [
        {
          role: "system",
          content: `You are an expert at creating AI personality prompts. Create a comprehensive system prompt that captures the personality and communication style of @${twitterHandle} based on their Twitter activity analysis.

          The system prompt should:
          1. Define the AI's personality and communication style
          2. Specify how they should respond to different types of content
          3. Include their areas of expertise and interests
          4. Capture their unique voice and approach
          5. Be practical for use in social media responses

          Make it detailed but concise (under 500 words). Write in second person ("You are...").`,
        },
        {
          role: "user",
          content: `Create a system prompt for @${twitterHandle} based on this analysis:

Writing Style: ${JSON.stringify(analysis.writingStyle, null, 2)}
Personality Traits: ${JSON.stringify(analysis.personalityTraits, null, 2)}
Topics of Interest: ${JSON.stringify(analysis.topicsOfInterest, null, 2)}
Engagement Patterns: ${JSON.stringify(analysis.engagementPatterns, null, 2)}`,
        },
      ],
      maxTokens: 800,
      temperature: 0.7,
    });

    let text = "";
    for await (const chunk of result.textStream) {
      text += chunk;
    }

    return text.trim();
  }

  /**
   * Perform complete persona analysis
   */
  async analyzePersona(
    tweets: string[],
    replies: string[] = []
  ): Promise<PersonaAnalysis> {
    console.log("🎭 PersonaAnalyzer: Starting complete persona analysis");

    try {
      // Run all analyses in parallel for efficiency
      const [
        writingStyle,
        personalityTraits,
        topicsOfInterest,
        engagementPatterns,
      ] = await Promise.all([
        this.analyzeWritingStyle(tweets),
        this.extractPersonalityTraits(tweets),
        this.identifyTopicsOfInterest(tweets),
        replies.length > 0
          ? this.analyzeEngagementPatterns(replies)
          : Promise.resolve({
              replyStyle: "conversational" as const,
              questionAsking: "frequent" as const,
              supportiveness: "high" as const,
            }),
      ]);

      console.log("✅ PersonaAnalyzer: Complete persona analysis finished");

      return {
        writingStyle,
        personalityTraits,
        topicsOfInterest,
        engagementPatterns,
      };
    } catch (error) {
      console.error("❌ PersonaAnalyzer: Analysis failed:", error);
      throw new Error(
        `Persona analysis failed: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }
}

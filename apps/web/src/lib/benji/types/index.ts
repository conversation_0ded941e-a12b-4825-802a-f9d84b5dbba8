/**
 * Type definitions for Benji AI Agent
 */

import type { ModelName } from "../../ai-providers";

export interface BenjiConfig {
  model?: ModelName;
  userId?: string;
  userPlan?: string;
  maxTokens?: number;
  temperature?: number;
  enableTools?: boolean;
  maxSteps?: number;
  personalityPrompt?: string;
  customSystemPrompt?: string;
  useFirstPerson?: boolean;
}

export interface BenjiContext {
  mentionId?: string;
  mentionContent?: string;
  authorInfo?: {
    name: string;
    handle: string;
    avatarUrl?: string;
  };
  // New fields for enhanced context
  monitoredAccountInfo?: {
    name: string;
    handle: string;
    avatarUrl?: string;
  };
  mentionAuthorInfo?: {
    name: string;
    handle: string;
    avatarUrl?: string;
  };
  conversationHistory?: Array<{
    role: "user" | "assistant";
    content: string;
  }>;
  // Memory context from mem0
  memoryContext?: string;
  // Persona-specific memory context
  personaMemoryContext?: string;
  sessionId?: string;
  // Market intelligence context
  marketIntelligence?: MarketIntelligence;
}

export interface MarketIntelligence {
  trendingProjects?: MarketProject[];
  relevantProjects?: MarketProject[];
  sector?: string;
  marketSentiment?: "bullish" | "bearish" | "neutral";
  updatedAt?: string;
}

export interface MarketProject {
  name: string;
  symbol?: string;
  sector?: string;
  mindshare?: number;
  mindshareDelta?: number;
}

export interface AnalysisData {
  sentiment: string;
  priority: string;
  recommendedAction: string;
  confidence: number;
  processingTime: number;
}

export interface FullAnalysisResult {
  bullishScore: number;
  importanceScore: number;
  keywords: string[];
  analysisData: AnalysisData;
}

export interface WritingStyleAnalysis {
  tone:
    | "professional"
    | "casual"
    | "friendly"
    | "authoritative"
    | "playful"
    | "serious";
  formality:
    | "very_formal"
    | "formal"
    | "semi_formal"
    | "casual"
    | "very_casual";
  humor: "none" | "subtle" | "moderate" | "frequent" | "heavy";
  emotionalRange: "reserved" | "balanced" | "expressive" | "very_expressive";
  vocabulary:
    | "simple"
    | "conversational"
    | "technical"
    | "academic"
    | "creative";
}

export interface PersonalityTraitsAnalysis {
  primary: string[];
  secondary: string[];
  communication: string[];
}

export interface TopicsOfInterestAnalysis {
  primary: string[];
  secondary: string[];
  expertise: string[];
}

export interface EngagementPatternsAnalysis {
  replyStyle: "brief" | "detailed" | "conversational" | "formal" | "supportive";
  questionAsking: "rare" | "occasional" | "frequent" | "very_frequent";
  supportiveness: "low" | "moderate" | "high" | "very_high";
}

export interface PersonaAnalysis {
  writingStyle: WritingStyleAnalysis;
  personalityTraits: PersonalityTraitsAnalysis;
  topicsOfInterest: TopicsOfInterestAnalysis;
  engagementPatterns: EngagementPatternsAnalysis;
}

export type ResponseType = "mention" | "quick-reply" | "enhanced" | "post";

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
}

export interface ConversationContext {
  userId: string;
  sessionId: string;
  mentionId?: string;
  authorInfo?: BenjiContext["authorInfo"];
  monitoredAccountInfo?: BenjiContext["monitoredAccountInfo"];
}

export interface MemoryOptions {
  memoryType: string;
  metadata: {
    mentionId?: string;
    authorHandle?: string;
    monitoredAccount?: string;
    timestamp: string;
    [key: string]: any;
  };
}

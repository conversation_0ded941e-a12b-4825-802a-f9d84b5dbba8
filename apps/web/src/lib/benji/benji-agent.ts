/**
 * Benji AI Agent - Refactored Core Implementation
 *
 * Orchestrates AI services for social media engagement, content analysis,
 * and personalized responses using modular service architecture.
 */

import { getModelByPlan, type ModelName } from "../ai-providers";
// Import services
import { ContentAnalyzerService } from "./services/content-analyzer";
import { MarketIntelligenceService } from "./services/market-intelligence";
import { MemoryService } from "./services/memory-service";
import { PersonaAnalyzerService } from "./services/persona-analyzer";
import { PromptBuilderService } from "./services/prompt-builder";
import { ResponseGeneratorService } from "./services/response-generator";
import { ToolService } from "./services/tool-service";
import type { BenjiConfig, BenjiContext, FullAnalysisResult } from "./types";

export class BenjiAgent {
  private config: BenjiConfig;

  // Services
  private contentAnalyzer: ContentAnalyzerService;
  private marketIntelligence: MarketIntelligenceService;
  private memoryService: MemoryService;
  private personaAnalyzer: PersonaAnalyzerService;
  private promptBuilder: PromptBuilderService;
  private responseGenerator: ResponseGeneratorService;
  private toolService: ToolService;

  constructor(config: BenjiConfig = {}) {
    this.config = {
      model: config.model || "gemini25Flash",
      userId: config.userId,
      userPlan: config.userPlan || "reply-guy",
      maxTokens: config.maxTokens || 4000,
      temperature: config.temperature || 0.7,
      enableTools: config.enableTools ?? true,
      maxSteps: config.maxSteps || 5,
      personalityPrompt: config.personalityPrompt,
      customSystemPrompt: config.customSystemPrompt,
      useFirstPerson: config.useFirstPerson ?? true,
    };

    // Auto-select model based on user plan if not specified
    if (!config.model && config.userPlan) {
      this.config.model = getModelByPlan(config.userPlan);
    }

    // Initialize services
    this.contentAnalyzer = new ContentAnalyzerService(this.config.model);
    this.marketIntelligence = new MarketIntelligenceService();
    this.memoryService = new MemoryService();
    this.personaAnalyzer = new PersonaAnalyzerService(this.config.model);
    this.promptBuilder = new PromptBuilderService();
    this.responseGenerator = new ResponseGeneratorService(this.config);
    this.toolService = new ToolService(this.config);
  }

  /**
   * Generate a response for a Twitter mention
   */
  async generateMentionResponse(
    mentionContent: string,
    context: BenjiContext = {}
  ) {
    // Enhance context with memory if available
    const enhancedContext = await this.memoryService.enhanceContextWithMemory(
      mentionContent,
      context,
      this.config.userId
    );

    const systemPrompt = this.promptBuilder.buildSystemPrompt(
      "mention",
      enhancedContext,
      {
        personalityPrompt: this.config.personalityPrompt,
        customSystemPrompt: this.config.customSystemPrompt,
        useFirstPerson: this.config.useFirstPerson,
      }
    );

    const messages = [
      {
        role: "system" as const,
        content: systemPrompt,
      },
      {
        role: "user" as const,
        content: `Please analyze this tweet and generate an appropriate response:\n\n"${mentionContent}"`,
      },
    ];

    // Store conversation memory after response
    if (this.config.userId) {
      await this.memoryService.storeConversationMemory(
        mentionContent,
        enhancedContext,
        this.config.userId
      );
    }

    return this.responseGenerator.streamResponse(messages, enhancedContext);
  }

  /**
   * Generate a response for any tweet (Quick Reply feature)
   */
  async generateQuickReply(tweetContent: string, context: BenjiContext = {}) {
    const systemPrompt = this.promptBuilder.buildSystemPrompt(
      "quick-reply",
      context,
      {
        personalityPrompt: this.config.personalityPrompt,
        customSystemPrompt: this.config.customSystemPrompt,
        useFirstPerson: this.config.useFirstPerson,
      }
    );

    const messages = [
      {
        role: "system" as const,
        content: systemPrompt,
      },
      {
        role: "user" as const,
        content: `Generate a thoughtful response to this tweet:\n\n"${tweetContent}"`,
      },
    ];

    return this.responseGenerator.streamResponse(messages, context);
  }

  /**
   * Generate an original post/tweet based on user prompt
   */
  async generatePost(userPrompt: string, context: BenjiContext = {}) {
    console.log(
      "📝 Benji: Generating original post for prompt:",
      userPrompt.substring(0, 100) + "..."
    );

    const systemPrompt = this.promptBuilder.buildSystemPrompt("post", context, {
      personalityPrompt: this.config.personalityPrompt,
      customSystemPrompt: this.config.customSystemPrompt,
      useFirstPerson: this.config.useFirstPerson,
    });

    const messages = [
      {
        role: "system" as const,
        content: systemPrompt,
      },
      {
        role: "user" as const,
        content: `Create an engaging social media post based on this prompt:\n\n"${userPrompt}"`,
      },
    ];

    return this.responseGenerator.streamResponse(messages, context);
  }

  /**
   * Generate an enhanced response for a Twitter mention with tool assistance
   * Always uses OpenAI o3 model for highest quality responses (with fallback)
   */
  async generateEnhancedMentionResponse(
    mentionContent: string,
    context: BenjiContext = {}
  ) {
    console.log(
      "🧠 Benji: Enhanced response - using OpenAI o3 via OpenRouter for maximum quality"
    );

    // Enhance context with memory if available
    const enhancedContext = await this.memoryService.enhanceContextWithMemory(
      mentionContent,
      context,
      this.config.userId
    );

    const systemPrompt = this.promptBuilder.buildSystemPrompt(
      "enhanced",
      enhancedContext,
      {
        personalityPrompt: this.config.personalityPrompt,
        customSystemPrompt: this.config.customSystemPrompt,
        useFirstPerson: this.config.useFirstPerson,
      }
    );

    let userPrompt = `Please respond to this tweet:\n\n"${mentionContent}"`;

    // Add context about who mentioned whom
    if (context.mentionAuthorInfo && context.monitoredAccountInfo) {
      userPrompt += `\n\nContext: @${context.mentionAuthorInfo.handle} mentioned @${context.monitoredAccountInfo.handle}.`;
    }

    const messages = [
      {
        role: "system" as const,
        content: systemPrompt,
      },
      {
        role: "user" as const,
        content: userPrompt,
      },
    ];

    // Store conversation memory after response
    if (this.config.userId) {
      await this.memoryService.storeConversationMemory(
        mentionContent,
        enhancedContext,
        this.config.userId
      );
    }

    // Try o3 first, fallback to Gemini Pro if o3 fails
    return this.responseGenerator.tryO3WithFallback(messages, enhancedContext);
  }

  /**
   * Enhanced mention response with market intelligence
   */
  async generateEnhancedMentionResponseWithIntelligence(
    mentionContent: string,
    context: BenjiContext = {}
  ) {
    console.log(
      "🚀 Benji: Starting enhanced response with market intelligence..."
    );

    // Fetch market intelligence if not already provided
    if (!context.marketIntelligence) {
      console.log("🍪 Benji: Fetching fresh market intelligence...");
      context.marketIntelligence =
        (await this.marketIntelligence.fetchMarketIntelligence(
          mentionContent
        )) || undefined;
    }

    // Log market intelligence status
    if (context.marketIntelligence) {
      console.log("✅ Benji: Market intelligence available:", {
        sector: context.marketIntelligence.sector,
        trendingCount: context.marketIntelligence.trendingProjects?.length || 0,
        relevantCount: context.marketIntelligence.relevantProjects?.length || 0,
        sentiment: context.marketIntelligence.marketSentiment,
      });
    } else {
      console.log("ℹ️ Benji: No market intelligence available for this content");
    }

    // Use the existing enhanced mention response method
    return this.generateEnhancedMentionResponse(mentionContent, context);
  }

  /**
   * Calculate bullish score for a tweet
   */
  async calculateBullishScore(content: string): Promise<number> {
    return this.contentAnalyzer.calculateBullishScore(content);
  }

  /**
   * Calculate importance score for a tweet
   */
  async calculateImportanceScore(
    content: string,
    authorInfo?: {
      followers?: number;
      verified?: boolean;
      handle?: string;
    }
  ): Promise<number> {
    return this.contentAnalyzer.calculateImportanceScore(content, authorInfo);
  }

  /**
   * Extract AI-enhanced keywords from tweet content
   */
  async extractEnhancedKeywords(content: string): Promise<string[]> {
    return this.contentAnalyzer.extractEnhancedKeywords(content);
  }

  /**
   * Perform comprehensive AI analysis of a tweet
   */
  async performFullAnalysis(
    content: string,
    authorInfo?: {
      name?: string;
      handle?: string;
      followers?: number;
      verified?: boolean;
      avatarUrl?: string;
    }
  ): Promise<FullAnalysisResult> {
    return this.contentAnalyzer.performFullAnalysis(content, authorInfo);
  }

  /**
   * Analyze writing style from tweet content
   */
  async analyzeWritingStyle(tweets: string[]) {
    return this.personaAnalyzer.analyzeWritingStyle(tweets);
  }

  /**
   * Extract personality traits from tweet content
   */
  async extractPersonalityTraits(tweets: string[]) {
    return this.personaAnalyzer.extractPersonalityTraits(tweets);
  }

  /**
   * Identify topics of interest from tweet content
   */
  async identifyTopicsOfInterest(tweets: string[]) {
    return this.personaAnalyzer.identifyTopicsOfInterest(tweets);
  }

  /**
   * Analyze engagement patterns from replies and interactions
   */
  async analyzeEngagementPatterns(replies: string[]) {
    return this.personaAnalyzer.analyzeEngagementPatterns(replies);
  }

  /**
   * Generate a comprehensive system prompt from personality analysis
   */
  async generatePersonaSystemPrompt(
    twitterHandle: string,
    analysis: {
      writingStyle: any;
      personalityTraits: any;
      topicsOfInterest: any;
      engagementPatterns: any;
    }
  ) {
    return this.personaAnalyzer.generatePersonaSystemPrompt(
      twitterHandle,
      analysis
    );
  }

  /**
   * Update agent configuration
   */
  updateConfig(updates: Partial<BenjiConfig>): void {
    this.config = { ...this.config, ...updates };

    // Update services that depend on config
    this.responseGenerator.updateConfig(this.config);
    this.toolService.updateConfig(this.config);

    // Recreate services that depend on model if model changed
    if (updates.model) {
      this.contentAnalyzer = new ContentAnalyzerService(this.config.model!);
      this.personaAnalyzer = new PersonaAnalyzerService(this.config.model!);
    }
  }

  /**
   * Get current configuration
   */
  getConfig(): BenjiConfig {
    return { ...this.config };
  }

  /**
   * Get available tools for current configuration
   */
  getAvailableTools() {
    return this.toolService.getAvailableTools();
  }

  /**
   * Get tool usage statistics
   */
  getToolUsageStats() {
    return this.toolService.getToolUsageStats();
  }

  /**
   * Check if a specific tool can be used
   */
  canUseTool(toolName: string): boolean {
    return this.toolService.canUseTool(toolName);
  }

  /**
   * Get tool health status
   */
  getToolHealth() {
    return this.toolService.getToolHealth();
  }

  /**
   * Reset tool usage for new conversation
   */
  resetToolUsage(): void {
    this.toolService.resetConversationUsage();
  }

  /**
   * Get tool recommendations based on context
   */
  getToolRecommendations(context: {
    contentType: "crypto" | "tech" | "general";
    needsResearch: boolean;
    needsVisuals: boolean;
  }): string[] {
    return this.toolService.getRecommendedTools(context);
  }
}

/**
 * New Telegram Security System
 *
 * Simplified, environment-aware security validation for Telegram webhooks
 */

import crypto from "crypto";
import { telegramLogger } from "./telegram-logger";
import {
  type SecurityLevel,
  telegramSecurity,
} from "./telegram-security-config";

export interface SecurityValidationRequest {
  ip?: string;
  headers: Record<string, string | undefined>;
  body: string;
  method: string;
  url: string;
}

export interface SecurityValidationResult {
  allowed: boolean;
  status: number;
  message: string;
  details?: {
    environment: SecurityLevel;
    checks: {
      basicValidation: boolean;
      webhookSecret: boolean;
      ipValidation: boolean;
      contentValidation: boolean;
      rateLimit: boolean;
    };
    bypassed: boolean;
    warnings: string[];
  };
}

export class TelegramSecurityValidator {
  private static instance: TelegramSecurityValidator;
  private requestCounts = new Map<
    string,
    { count: number; resetTime: number }
  >();

  private constructor() {}

  public static getInstance(): TelegramSecurityValidator {
    if (!TelegramSecurityValidator.instance) {
      TelegramSecurityValidator.instance = new TelegramSecurityValidator();
    }
    return TelegramSecurityValidator.instance;
  }

  /**
   * Main security validation entry point
   */
  public validateRequest(
    request: SecurityValidationRequest
  ): SecurityValidationResult {
    const config = telegramSecurity.getConfig();
    const startTime = Date.now();

    telegramLogger.debug("Starting security validation", {
      metadata: {
        environment: config.environment,
        ip: request.ip,
        method: request.method,
        contentLength: request.body.length,
      },
    });

    // Initialize result
    const result: SecurityValidationResult = {
      allowed: false,
      status: 403,
      message: "Security validation failed",
      details: {
        environment: config.environment,
        checks: {
          basicValidation: false,
          webhookSecret: false,
          ipValidation: false,
          contentValidation: false,
          rateLimit: false,
        },
        bypassed: false,
        warnings: [],
      },
    };

    try {
      // Check if configuration is valid
      if (!telegramSecurity.isValid()) {
        const errors = telegramSecurity.getErrors();
        telegramLogger.error("Security configuration invalid", {
          metadata: { errors },
        });

        // In development, allow with warnings
        if (telegramSecurity.canBypassSecurity()) {
          result.allowed = true;
          result.status = 200;
          result.message = "Allowed with configuration warnings";
          result.details!.bypassed = true;
          result.details!.warnings = errors;
          return result;
        }

        result.status = 500;
        result.message = "Security configuration error";
        return result;
      }

      // Step 1: Basic request validation
      const basicValidation = this.validateBasicRequest(request);
      result.details!.checks.basicValidation = basicValidation.valid;

      if (!basicValidation.valid) {
        result.message = basicValidation.reason;
        result.status = 400;
        return result;
      }

      // Step 2: Rate limiting
      if (config.enableRateLimit) {
        const rateLimitCheck = this.checkRateLimit(request.ip || "unknown");
        result.details!.checks.rateLimit = rateLimitCheck.allowed;

        if (!rateLimitCheck.allowed) {
          result.message = "Rate limit exceeded";
          result.status = 429;
          return result;
        }
      } else {
        result.details!.checks.rateLimit = true;
      }

      // Step 3: IP validation
      if (config.enableIPValidation) {
        const ipValidation = this.validateIP(request.ip);
        result.details!.checks.ipValidation = ipValidation.valid;

        if (!ipValidation.valid) {
          result.message = ipValidation.reason;
          result.status = 403;

          // In development, warn but allow
          if (telegramSecurity.canBypassSecurity()) {
            result.details!.warnings.push(
              `IP validation failed: ${ipValidation.reason}`
            );
          } else {
            return result;
          }
        }
      } else {
        result.details!.checks.ipValidation = true;
      }

      // Step 4: Webhook secret validation
      if (config.enableWebhookSecretValidation) {
        const secretValidation = this.validateWebhookSecret(request);
        result.details!.checks.webhookSecret = secretValidation.valid;

        if (!secretValidation.valid) {
          result.message = secretValidation.reason;
          result.status = 403;

          // In development, warn but allow
          if (telegramSecurity.canBypassSecurity()) {
            result.details!.warnings.push(
              `Webhook secret validation failed: ${secretValidation.reason}`
            );
          } else {
            return result;
          }
        }
      } else {
        result.details!.checks.webhookSecret = true;
      }

      // Step 5: Content validation
      if (config.enableContentValidation) {
        const contentValidation = this.validateContent(request);
        result.details!.checks.contentValidation = contentValidation.valid;

        if (!contentValidation.valid) {
          result.message = contentValidation.reason;
          result.status = 400;
          return result;
        }
      } else {
        result.details!.checks.contentValidation = true;
      }

      // All checks passed
      result.allowed = true;
      result.status = 200;
      result.message = "Security validation passed";

      // Check if any bypasses were used
      if (result.details!.warnings.length > 0) {
        result.details!.bypassed = true;
        result.message += " (with development bypasses)";
      }

      const processingTime = Date.now() - startTime;
      telegramLogger.info("Security validation completed", {
        metadata: {
          allowed: result.allowed,
          status: result.status,
          processingTime,
          bypassed: result.details!.bypassed,
          warnings: result.details!.warnings.length,
        },
      });

      return result;
    } catch (error) {
      telegramLogger.error("Security validation error", {
        error: error instanceof Error ? error : new Error(String(error)),
      });

      result.status = 500;
      result.message = "Internal security error";
      return result;
    }
  }

  private validateBasicRequest(request: SecurityValidationRequest): {
    valid: boolean;
    reason: string;
  } {
    // Check method
    if (request.method !== "POST") {
      return { valid: false, reason: "Only POST method allowed" };
    }

    // Check content type
    const contentType = request.headers["content-type"];
    if (!contentType || !contentType.includes("application/json")) {
      return { valid: false, reason: "Content-Type must be application/json" };
    }

    // Check content length
    const config = telegramSecurity.getConfig();
    if (request.body.length > config.maxPayloadSize) {
      return {
        valid: false,
        reason: `Payload too large (max ${config.maxPayloadSize} bytes)`,
      };
    }

    return { valid: true, reason: "Basic validation passed" };
  }

  private checkRateLimit(ip: string): { allowed: boolean; reason: string } {
    const config = telegramSecurity.getConfig();
    const now = Date.now();
    const windowMs = 60 * 1000; // 1 minute

    const key = `rate_limit_${ip}`;
    const current = this.requestCounts.get(key);

    if (!current || now > current.resetTime) {
      this.requestCounts.set(key, { count: 1, resetTime: now + windowMs });
      return { allowed: true, reason: "Rate limit OK" };
    }

    if (current.count >= config.maxRequestsPerMinute) {
      return { allowed: false, reason: "Rate limit exceeded" };
    }

    current.count++;
    return { allowed: true, reason: "Rate limit OK" };
  }

  private validateIP(ip?: string): { valid: boolean; reason: string } {
    if (!ip) {
      return { valid: false, reason: "No IP address provided" };
    }

    const config = telegramSecurity.getConfig();

    // For now, we'll implement a simple check
    // In a full implementation, you'd check against CIDR ranges
    const telegramIPPrefixes = ["149.154.", "91.108."];
    const isFromTelegram = telegramIPPrefixes.some((prefix) =>
      ip.startsWith(prefix)
    );

    if (!isFromTelegram) {
      return { valid: false, reason: `IP ${ip} not in Telegram ranges` };
    }

    return { valid: true, reason: "IP validation passed" };
  }

  private validateWebhookSecret(request: SecurityValidationRequest): {
    valid: boolean;
    reason: string;
  } {
    const config = telegramSecurity.getConfig();

    if (!config.webhookSecret) {
      return { valid: false, reason: "Webhook secret not configured" };
    }

    const receivedSecret = request.headers["x-telegram-bot-api-secret-token"];

    if (!receivedSecret) {
      return { valid: false, reason: "Webhook secret header missing" };
    }

    if (receivedSecret !== config.webhookSecret) {
      return { valid: false, reason: "Webhook secret mismatch" };
    }

    return { valid: true, reason: "Webhook secret validation passed" };
  }

  private validateContent(request: SecurityValidationRequest): {
    valid: boolean;
    reason: string;
  } {
    try {
      const parsed = JSON.parse(request.body);

      // Basic Telegram update structure validation
      if (typeof parsed !== "object" || !parsed.update_id) {
        return { valid: false, reason: "Invalid Telegram update structure" };
      }

      return { valid: true, reason: "Content validation passed" };
    } catch (error) {
      return { valid: false, reason: "Invalid JSON payload" };
    }
  }
}

// Export singleton instance
export const telegramSecurityValidator =
  TelegramSecurityValidator.getInstance();

/**
 * User Analytics Service
 *
 * Tracks user behavior, feature usage, and business metrics for BuddyChip.
 * Provides insights into user engagement, feature adoption, and conversion metrics.
 */

import type { FeatureType } from "../../prisma/generated";
import {
  type BusinessMetric,
  enhancedPerformanceMonitor,
  type UserAnalyticsEvent,
} from "./enhanced-performance-monitor";

// Feature usage tracking
export interface FeatureUsageEvent {
  feature: FeatureType;
  action: "started" | "completed" | "failed" | "cancelled";
  userId?: string;
  sessionId: string;
  duration?: number;
  metadata?: Record<string, any>;
  timestamp: number;
}

// Conversion tracking
export interface ConversionEvent {
  type: "signup" | "subscription" | "feature_unlock" | "retention";
  userId?: string;
  sessionId: string;
  value?: number;
  metadata?: Record<string, any>;
  timestamp: number;
}

// User journey tracking
export interface UserJourneyStep {
  step: string;
  page: string;
  action?: string;
  userId?: string;
  sessionId: string;
  timestamp: number;
  metadata?: Record<string, any>;
}

/**
 * User Analytics Service
 */
class UserAnalyticsService {
  private static instance: UserAnalyticsService;
  private sessionId: string;
  private userId?: string;
  private journeySteps: UserJourneyStep[] = [];
  private eventListeners: Array<{
    element: EventTarget;
    event: string;
    handler: EventListener;
  }> = [];
  private interactionTimer?: NodeJS.Timeout;

  private constructor() {
    this.sessionId = this.generateSessionId();
    this.initializeTracking();
  }

  static getInstance(): UserAnalyticsService {
    if (!UserAnalyticsService.instance) {
      UserAnalyticsService.instance = new UserAnalyticsService();
    }
    return UserAnalyticsService.instance;
  }

  /**
   * Set current user ID for tracking
   */
  setUserId(userId: string) {
    this.userId = userId;
    console.log(`📊 Analytics: User ID set to ${userId}`);
  }

  /**
   * Track feature usage
   */
  trackFeatureUsage(event: Omit<FeatureUsageEvent, "sessionId" | "timestamp">) {
    const fullEvent: FeatureUsageEvent = {
      ...event,
      sessionId: this.sessionId,
      timestamp: Date.now(),
    };

    // Track as user analytics event
    enhancedPerformanceMonitor.trackUserAnalytics({
      type: "feature_usage",
      userId: this.userId,
      sessionId: this.sessionId,
      feature: event.feature,
      action: event.action,
      properties: {
        duration: event.duration,
        ...event.metadata,
      },
      timestamp: fullEvent.timestamp,
      duration: event.duration,
    });

    // Record business metric for feature adoption
    if (event.action === "completed") {
      enhancedPerformanceMonitor.recordBusinessMetric({
        type: "feature_adoption",
        name: `${event.feature}_usage`,
        value: 1,
        unit: "count",
        dimensions: {
          feature: event.feature,
          userId: this.userId || "anonymous",
          action: event.action,
        },
        timestamp: fullEvent.timestamp,
      });
    }

    console.log(
      `🎯 Feature Usage: ${event.feature} ${event.action}`,
      event.metadata
    );
  }

  /**
   * Track conversion events
   */
  trackConversion(event: Omit<ConversionEvent, "sessionId" | "timestamp">) {
    const fullEvent: ConversionEvent = {
      ...event,
      sessionId: this.sessionId,
      timestamp: Date.now(),
    };

    // Track as user analytics event
    enhancedPerformanceMonitor.trackUserAnalytics({
      type: "conversion",
      userId: this.userId,
      sessionId: this.sessionId,
      properties: {
        conversionType: event.type,
        value: event.value,
        ...event.metadata,
      },
      timestamp: fullEvent.timestamp,
    });

    // Record business metric
    enhancedPerformanceMonitor.recordBusinessMetric({
      type: "revenue_metric",
      name: `conversion_${event.type}`,
      value: event.value || 1,
      unit: event.value ? "currency" : "count",
      dimensions: {
        conversionType: event.type,
        userId: this.userId || "anonymous",
      },
      timestamp: fullEvent.timestamp,
    });

    console.log(`💰 Conversion: ${event.type}`, {
      value: event.value,
      ...event.metadata,
    });
  }

  /**
   * Track user journey step
   */
  trackJourneyStep(step: Omit<UserJourneyStep, "sessionId" | "timestamp">) {
    const fullStep: UserJourneyStep = {
      ...step,
      sessionId: this.sessionId,
      timestamp: Date.now(),
    };

    this.journeySteps.push(fullStep);

    // Keep only recent steps
    if (this.journeySteps.length > 100) {
      this.journeySteps = this.journeySteps.slice(-100);
    }

    // Track as user analytics event
    enhancedPerformanceMonitor.trackUserAnalytics({
      type: "user_action",
      userId: this.userId,
      sessionId: this.sessionId,
      page: step.page,
      action: step.action,
      properties: {
        journeyStep: step.step,
        ...step.metadata,
      },
      timestamp: fullStep.timestamp,
    });

    console.log(`🚶 Journey Step: ${step.step} on ${step.page}`);
  }

  /**
   * Track page view with enhanced metadata
   */
  trackPageView(page: string, metadata?: Record<string, any>) {
    enhancedPerformanceMonitor.trackUserAnalytics({
      type: "page_view",
      userId: this.userId,
      sessionId: this.sessionId,
      page,
      properties: {
        referrer: typeof window !== "undefined" ? document.referrer : undefined,
        userAgent:
          typeof window !== "undefined" ? navigator.userAgent : undefined,
        timestamp: Date.now(),
        ...metadata,
      },
      timestamp: Date.now(),
    });

    // Track as journey step
    this.trackJourneyStep({
      step: "page_view",
      page,
      metadata,
    });
  }

  /**
   * Track user engagement metrics
   */
  trackEngagement(
    type: "session_start" | "session_end" | "interaction" | "idle",
    metadata?: Record<string, any>
  ) {
    enhancedPerformanceMonitor.recordBusinessMetric({
      type: "user_engagement",
      name: `engagement_${type}`,
      value: 1,
      unit: "count",
      dimensions: {
        engagementType: type,
        userId: this.userId || "anonymous",
        page:
          typeof window !== "undefined" ? window.location.pathname : "unknown",
      },
      timestamp: Date.now(),
    });

    console.log(`👤 Engagement: ${type}`, metadata);
  }

  /**
   * Track error events with context
   */
  trackError(error: Error | string, context?: Record<string, any>) {
    const errorMessage = error instanceof Error ? error.message : error;
    const errorStack = error instanceof Error ? error.stack : undefined;

    enhancedPerformanceMonitor.trackUserAnalytics({
      type: "error",
      userId: this.userId,
      sessionId: this.sessionId,
      page:
        typeof window !== "undefined" ? window.location.pathname : "unknown",
      properties: {
        error: errorMessage,
        stack: errorStack,
        ...context,
      },
      timestamp: Date.now(),
    });

    console.error(`🚨 Error Tracked: ${errorMessage}`, context);
  }

  /**
   * Get user journey for current session
   */
  getUserJourney(): UserJourneyStep[] {
    return [...this.journeySteps];
  }

  /**
   * Get session analytics summary
   */
  getSessionSummary() {
    const journey = this.getUserJourney();
    const pages = new Set(journey.map((step) => step.page));
    const actions = journey.filter((step) => step.action);

    return {
      sessionId: this.sessionId,
      userId: this.userId,
      duration: journey.length > 0 ? Date.now() - journey[0].timestamp : 0,
      pageViews: journey.filter((step) => step.step === "page_view").length,
      uniquePages: pages.size,
      actions: actions.length,
      journey: journey.map((step) => ({
        step: step.step,
        page: step.page,
        action: step.action,
        timestamp: step.timestamp,
      })),
    };
  }

  /**
   * Initialize automatic tracking
   */
  private initializeTracking() {
    if (typeof window === "undefined") return;

    // Track session start
    this.trackEngagement("session_start");

    // Track page visibility changes
    const visibilityChangeHandler = () => {
      if (document.hidden) {
        this.trackEngagement("idle");
      } else {
        this.trackEngagement("interaction");
      }
    };
    this.addEventListenerWithTracking(
      document,
      "visibilitychange",
      visibilityChangeHandler
    );

    // Track session end on page unload
    const beforeUnloadHandler = () => {
      this.trackEngagement("session_end");
    };
    this.addEventListenerWithTracking(
      window,
      "beforeunload",
      beforeUnloadHandler
    );

    // Track user interactions
    const trackInteraction = () => {
      if (this.interactionTimer) {
        clearTimeout(this.interactionTimer);
      }
      this.trackEngagement("interaction");

      // Set idle timer
      this.interactionTimer = setTimeout(() => {
        this.trackEngagement("idle");
      }, 30000); // 30 seconds of inactivity
    };

    this.addEventListenerWithTracking(document, "click", trackInteraction);
    this.addEventListenerWithTracking(document, "keydown", trackInteraction);
    this.addEventListenerWithTracking(document, "scroll", trackInteraction);
    this.addEventListenerWithTracking(document, "mousemove", trackInteraction);
  }

  /**
   * Add event listener with tracking for cleanup
   */
  private addEventListenerWithTracking(
    element: EventTarget,
    event: string,
    handler: EventListener
  ): void {
    element.addEventListener(event, handler);
    this.eventListeners.push({ element, event, handler });
  }

  /**
   * Clean up event listeners and timers
   */
  public cleanup(): void {
    // Remove all event listeners
    this.eventListeners.forEach(({ element, event, handler }) => {
      element.removeEventListener(event, handler);
    });
    this.eventListeners = [];

    // Clear interaction timer
    if (this.interactionTimer) {
      clearTimeout(this.interactionTimer);
      this.interactionTimer = undefined;
    }
  }

  /**
   * Utility functions
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Export singleton instance
export const userAnalytics = UserAnalyticsService.getInstance();

// Convenience functions for common tracking scenarios
export const analytics = {
  // Feature tracking
  featureStarted: (feature: FeatureType, metadata?: Record<string, any>) => {
    userAnalytics.trackFeatureUsage({ feature, action: "started", metadata });
  },

  featureCompleted: (
    feature: FeatureType,
    duration?: number,
    metadata?: Record<string, any>
  ) => {
    userAnalytics.trackFeatureUsage({
      feature,
      action: "completed",
      duration,
      metadata,
    });
  },

  featureFailed: (
    feature: FeatureType,
    error?: string,
    metadata?: Record<string, any>
  ) => {
    userAnalytics.trackFeatureUsage({
      feature,
      action: "failed",
      metadata: { error, ...metadata },
    });
  },

  // Conversion tracking
  userSignedUp: (userId: string, metadata?: Record<string, any>) => {
    userAnalytics.setUserId(userId);
    userAnalytics.trackConversion({ type: "signup", metadata });
  },

  userSubscribed: (
    planId: string,
    value: number,
    metadata?: Record<string, any>
  ) => {
    userAnalytics.trackConversion({
      type: "subscription",
      value,
      metadata: { planId, ...metadata },
    });
  },

  // Page tracking
  pageViewed: (page: string, metadata?: Record<string, any>) => {
    userAnalytics.trackPageView(page, metadata);
  },

  // Error tracking
  errorOccurred: (error: Error | string, context?: Record<string, any>) => {
    userAnalytics.trackError(error, context);
  },

  // User identification
  identifyUser: (userId: string) => {
    userAnalytics.setUserId(userId);
  },

  // Get insights
  getSessionSummary: () => {
    return userAnalytics.getSessionSummary();
  },
};

// Types are already exported as interfaces above

/**
 * Tests for StartCommandHandler
 */

import { describe, it, expect, beforeEach, vi } from "vitest";
import type TelegramBot from "node-telegram-bot-api";
import { StartCommandHandler } from "../../commands/start-command.handler";
import type { TelegramHandlerContext } from "../../core/telegram-handler-context";

// Mock dependencies
const mockBot = {
  sendMessage: vi.fn(),
} as any;

const mockUserService = {
  getOrCreateTelegramUser: vi.fn(),
} as any;

const mockLogger = {
  logCommand: vi.fn(),
  info: vi.fn(),
  debug: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
} as any;

const mockContext: TelegramHandlerContext = {
  bot: mockBot,
  userService: mockUserService,
  sessionService: {} as any,
  securityService: {} as any,
  logger: mockLogger,
  config: {
    token: "test-token",
    enablePolling: true,
  },
};

describe("StartCommandHandler", () => {
  let handler: StartCommandHandler;

  beforeEach(() => {
    handler = new StartCommandHandler();
    vi.clearAllMocks();
  });

  it("should have correct command and description", () => {
    expect(handler.getCommand()).toBe("start");
    expect(handler.getDescription()).toBe("Welcome message and account linking");
  });

  it("should handle start command for new user", async () => {
    const message: TelegramBot.Message = {
      message_id: 1,
      date: Date.now(),
      chat: { id: 123, type: "private" },
      from: {
        id: 456,
        is_bot: false,
        first_name: "John",
        username: "john_doe",
      },
      text: "/start",
    };

    const mockTelegramUser = {
      id: "tg-user-1",
      telegramId: "456",
      userId: null, // Not linked
      createdAt: new Date(),
      lastActiveAt: new Date(),
    };

    mockUserService.getOrCreateTelegramUser.mockResolvedValue(mockTelegramUser);

    await handler.handle(message, mockContext);

    expect(mockLogger.logCommand).toHaveBeenCalledWith(
      "start",
      "456",
      "123",
      true
    );

    expect(mockUserService.getOrCreateTelegramUser).toHaveBeenCalledWith(
      message.from
    );

    expect(mockBot.sendMessage).toHaveBeenCalledWith(
      123,
      expect.stringContaining("Welcome to BuddyChip AI, John!"),
      expect.objectContaining({
        parse_mode: "Markdown",
        reply_markup: expect.objectContaining({
          inline_keyboard: expect.arrayContaining([
            expect.arrayContaining([
              expect.objectContaining({
                text: "🔗 Link Account",
                callback_data: "link_account",
              }),
            ]),
          ]),
        }),
      })
    );
  });

  it("should handle start command for linked user", async () => {
    const message: TelegramBot.Message = {
      message_id: 1,
      date: Date.now(),
      chat: { id: 123, type: "private" },
      from: {
        id: 456,
        is_bot: false,
        first_name: "John",
        username: "john_doe",
      },
      text: "/start",
    };

    const mockTelegramUser = {
      id: "tg-user-1",
      telegramId: "456",
      userId: "user-123", // Linked
      createdAt: new Date(),
      lastActiveAt: new Date(),
    };

    mockUserService.getOrCreateTelegramUser.mockResolvedValue(mockTelegramUser);

    await handler.handle(message, mockContext);

    expect(mockBot.sendMessage).toHaveBeenCalledWith(
      123,
      expect.stringContaining("Welcome back, John!"),
      expect.objectContaining({
        parse_mode: "Markdown",
        reply_markup: expect.objectContaining({
          inline_keyboard: expect.arrayContaining([
            expect.arrayContaining([
              expect.objectContaining({
                text: "🎨 Create Post",
                callback_data: "quick_create",
              }),
            ]),
          ]),
        }),
      })
    );
  });

  it("should handle missing user information", async () => {
    const message: TelegramBot.Message = {
      message_id: 1,
      date: Date.now(),
      chat: { id: 123, type: "private" },
      from: undefined, // Missing user
      text: "/start",
    };

    await handler.handle(message, mockContext);

    expect(mockBot.sendMessage).toHaveBeenCalledWith(
      123,
      "❌ Unable to identify user. Please try again."
    );

    expect(mockUserService.getOrCreateTelegramUser).not.toHaveBeenCalled();
  });

  it("should handle user service errors", async () => {
    const message: TelegramBot.Message = {
      message_id: 1,
      date: Date.now(),
      chat: { id: 123, type: "private" },
      from: {
        id: 456,
        is_bot: false,
        first_name: "John",
        username: "john_doe",
      },
      text: "/start",
    };

    const error = new Error("Database connection failed");
    mockUserService.getOrCreateTelegramUser.mockRejectedValue(error);

    await handler.handle(message, mockContext);

    expect(mockLogger.error).toHaveBeenCalledWith(
      "Error in /start command",
      expect.objectContaining({
        chatId: "123",
        telegramUserId: "456",
        metadata: expect.objectContaining({
          error: "Database connection failed",
        }),
      })
    );

    expect(mockBot.sendMessage).toHaveBeenCalledWith(
      123,
      "❌ Sorry, something went wrong. Please try again."
    );
  });

  it("should handle user without first name", async () => {
    const message: TelegramBot.Message = {
      message_id: 1,
      date: Date.now(),
      chat: { id: 123, type: "private" },
      from: {
        id: 456,
        is_bot: false,
        first_name: undefined, // No first name
        username: "john_doe",
      },
      text: "/start",
    };

    const mockTelegramUser = {
      id: "tg-user-1",
      telegramId: "456",
      userId: null,
      createdAt: new Date(),
      lastActiveAt: new Date(),
    };

    mockUserService.getOrCreateTelegramUser.mockResolvedValue(mockTelegramUser);

    await handler.handle(message, mockContext);

    expect(mockBot.sendMessage).toHaveBeenCalledWith(
      123,
      expect.stringContaining("Welcome to BuddyChip AI, there!"),
      expect.any(Object)
    );
  });
});

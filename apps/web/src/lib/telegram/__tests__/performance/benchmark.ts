/**
 * Benchmark script to compare old vs new Telegram bot architecture
 * Run with: bun run benchmark
 */

import { performance } from "perf_hooks";

// Mock dependencies to focus on architecture performance
const mockBot = {
  getMe: () => Promise.resolve({
    id: 123456789,
    username: "test_bot",
    first_name: "Test Bot",
    is_bot: true,
  }),
  setWebHook: () => Promise.resolve(true),
  startPolling: () => Promise.resolve(),
  stopPolling: () => Promise.resolve(),
  close: () => Promise.resolve(),
  on: () => {},
  sendMessage: () => Promise.resolve({
    message_id: 1,
    date: Date.now(),
    chat: { id: 123, type: "private" },
    text: "Response",
  }),
  answerCallbackQuery: () => Promise.resolve(true),
};

// Mock external services
const mockServices = {
  userService: {
    getOrCreateTelegramUser: () => Promise.resolve({
      id: "tg-user-1",
      telegramId: "456",
      userId: "user-123",
      createdAt: new Date(),
      lastActiveAt: new Date(),
    }),
  },
  sessionService: {
    storeSessionData: () => Promise.resolve(),
    getSessionData: () => Promise.resolve(null),
  },
  securityService: {
    checkMessageSecurity: () => Promise.resolve({
      allowed: true,
      reason: null,
      securityLevel: "safe",
      issues: [],
    }),
    checkRateLimit: () => Promise.resolve({
      allowed: true,
      remaining: 10,
      resetTime: Date.now() + 60000,
    }),
  },
};

interface BenchmarkResult {
  name: string;
  iterations: number;
  totalTime: number;
  avgTime: number;
  minTime: number;
  maxTime: number;
  opsPerSecond: number;
}

class Benchmark {
  private results: BenchmarkResult[] = [];

  async run(name: string, fn: () => Promise<void>, iterations: number = 1000): Promise<BenchmarkResult> {
    console.log(`\n🏃 Running benchmark: ${name} (${iterations} iterations)`);
    
    const times: number[] = [];
    let totalTime = 0;

    // Warm up
    for (let i = 0; i < 10; i++) {
      await fn();
    }

    // Actual benchmark
    for (let i = 0; i < iterations; i++) {
      const start = performance.now();
      await fn();
      const end = performance.now();
      const time = end - start;
      times.push(time);
      totalTime += time;

      if (i % 100 === 0) {
        process.stdout.write(`\r  Progress: ${i}/${iterations} (${((i / iterations) * 100).toFixed(1)}%)`);
      }
    }

    const avgTime = totalTime / iterations;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    const opsPerSecond = 1000 / avgTime;

    const result: BenchmarkResult = {
      name,
      iterations,
      totalTime,
      avgTime,
      minTime,
      maxTime,
      opsPerSecond,
    };

    this.results.push(result);
    
    console.log(`\r  ✅ Completed: ${avgTime.toFixed(3)}ms avg, ${opsPerSecond.toFixed(0)} ops/sec`);
    
    return result;
  }

  printResults(): void {
    console.log("\n📊 Benchmark Results:");
    console.log("=" .repeat(80));
    console.log(
      "Name".padEnd(30) +
      "Avg (ms)".padEnd(12) +
      "Min (ms)".padEnd(12) +
      "Max (ms)".padEnd(12) +
      "Ops/sec".padEnd(12)
    );
    console.log("-".repeat(80));

    for (const result of this.results) {
      console.log(
        result.name.padEnd(30) +
        result.avgTime.toFixed(3).padEnd(12) +
        result.minTime.toFixed(3).padEnd(12) +
        result.maxTime.toFixed(3).padEnd(12) +
        result.opsPerSecond.toFixed(0).padEnd(12)
      );
    }

    console.log("=" .repeat(80));
  }

  compareResults(baseline: string, comparison: string): void {
    const baselineResult = this.results.find(r => r.name === baseline);
    const comparisonResult = this.results.find(r => r.name === comparison);

    if (!baselineResult || !comparisonResult) {
      console.log("❌ Cannot compare: missing results");
      return;
    }

    const speedup = baselineResult.avgTime / comparisonResult.avgTime;
    const improvement = ((baselineResult.avgTime - comparisonResult.avgTime) / baselineResult.avgTime) * 100;

    console.log(`\n🔍 Comparison: ${comparison} vs ${baseline}`);
    console.log(`  Speed: ${speedup.toFixed(2)}x ${speedup > 1 ? "faster" : "slower"}`);
    console.log(`  Improvement: ${improvement.toFixed(1)}% ${improvement > 0 ? "better" : "worse"}`);
  }
}

// Simulate old monolithic architecture
class OldTelegramBot {
  private bot = mockBot;

  async processUpdate(update: any): Promise<void> {
    // Simulate the old monolithic processing
    if (update.message?.text?.startsWith("/")) {
      await this.handleCommand(update.message);
    } else if (update.message?.text?.includes("twitter.com")) {
      await this.handleTwitterUrl(update.message);
    } else if (update.message) {
      await this.handleGeneralMessage(update.message);
    } else if (update.callback_query) {
      await this.handleCallbackQuery(update.callback_query);
    }
  }

  private async handleCommand(message: any): Promise<void> {
    // Simulate command processing overhead
    await new Promise(resolve => setTimeout(resolve, 1));
    await this.bot.sendMessage(message.chat.id, "Command response");
  }

  private async handleTwitterUrl(message: any): Promise<void> {
    // Simulate Twitter URL processing overhead
    await new Promise(resolve => setTimeout(resolve, 2));
    await this.bot.sendMessage(message.chat.id, "Twitter response");
  }

  private async handleGeneralMessage(message: any): Promise<void> {
    // Simulate general message processing overhead
    await new Promise(resolve => setTimeout(resolve, 1.5));
    await this.bot.sendMessage(message.chat.id, "General response");
  }

  private async handleCallbackQuery(query: any): Promise<void> {
    // Simulate callback processing overhead
    await new Promise(resolve => setTimeout(resolve, 0.5));
    await this.bot.answerCallbackQuery(query.id);
  }
}

// Simulate new modular architecture
class NewTelegramBot {
  private bot = mockBot;
  private router = new ModularRouter();

  async processUpdate(update: any): Promise<void> {
    await this.router.routeUpdate(update);
  }
}

class ModularRouter {
  private commandHandlers = new Map([
    ["start", { handle: async () => { await new Promise(resolve => setTimeout(resolve, 0.8)); } }],
    ["help", { handle: async () => { await new Promise(resolve => setTimeout(resolve, 0.8)); } }],
  ]);

  private messageProcessors = [
    {
      canHandle: (msg: any) => msg.text?.includes("twitter.com"),
      process: async () => { await new Promise(resolve => setTimeout(resolve, 1.8)); },
    },
    {
      canHandle: (msg: any) => !!msg.text,
      process: async () => { await new Promise(resolve => setTimeout(resolve, 1.2)); },
    },
  ];

  private callbackHandlers = new Map([
    ["copy", { handle: async () => { await new Promise(resolve => setTimeout(resolve, 0.4)); } }],
    ["regenerate", { handle: async () => { await new Promise(resolve => setTimeout(resolve, 0.4)); } }],
  ]);

  async routeUpdate(update: any): Promise<void> {
    if (update.message?.text?.startsWith("/")) {
      const command = update.message.text.slice(1).split(" ")[0];
      const handler = this.commandHandlers.get(command);
      if (handler) {
        await handler.handle();
      }
    } else if (update.message) {
      for (const processor of this.messageProcessors) {
        if (processor.canHandle(update.message)) {
          await processor.process();
          break;
        }
      }
    } else if (update.callback_query) {
      const [action] = update.callback_query.data.split(":");
      const handler = this.callbackHandlers.get(action);
      if (handler) {
        await handler.handle();
      }
    }
  }
}

async function main(): Promise<void> {
  console.log("🚀 Telegram Bot Architecture Benchmark");
  console.log("Comparing old monolithic vs new modular architecture");

  const benchmark = new Benchmark();
  const oldBot = new OldTelegramBot();
  const newBot = new NewTelegramBot();

  // Test data
  const commandUpdate = {
    update_id: 1,
    message: {
      message_id: 1,
      date: Math.floor(Date.now() / 1000),
      chat: { id: 123, type: "private" },
      from: { id: 456, is_bot: false, first_name: "John" },
      text: "/start",
    },
  };

  const twitterUpdate = {
    update_id: 2,
    message: {
      message_id: 2,
      date: Math.floor(Date.now() / 1000),
      chat: { id: 123, type: "private" },
      from: { id: 456, is_bot: false, first_name: "John" },
      text: "Check this out: https://twitter.com/user/status/123",
    },
  };

  const generalUpdate = {
    update_id: 3,
    message: {
      message_id: 3,
      date: Math.floor(Date.now() / 1000),
      chat: { id: 123, type: "private" },
      from: { id: 456, is_bot: false, first_name: "John" },
      text: "Hello, how are you?",
    },
  };

  const callbackUpdate = {
    update_id: 4,
    callback_query: {
      id: "callback-1",
      from: { id: 456, is_bot: false, first_name: "John" },
      message: {
        message_id: 1,
        date: Math.floor(Date.now() / 1000),
        chat: { id: 123, type: "private" },
      },
      data: "copy:user123",
    },
  };

  // Run benchmarks
  await benchmark.run("Old: Command Processing", () => oldBot.processUpdate(commandUpdate), 1000);
  await benchmark.run("New: Command Processing", () => newBot.processUpdate(commandUpdate), 1000);

  await benchmark.run("Old: Twitter URL Processing", () => oldBot.processUpdate(twitterUpdate), 1000);
  await benchmark.run("New: Twitter URL Processing", () => newBot.processUpdate(twitterUpdate), 1000);

  await benchmark.run("Old: General Message", () => oldBot.processUpdate(generalUpdate), 1000);
  await benchmark.run("New: General Message", () => newBot.processUpdate(generalUpdate), 1000);

  await benchmark.run("Old: Callback Query", () => oldBot.processUpdate(callbackUpdate), 1000);
  await benchmark.run("New: Callback Query", () => newBot.processUpdate(callbackUpdate), 1000);

  // Mixed workload
  const mixedUpdates = [commandUpdate, twitterUpdate, generalUpdate, callbackUpdate];
  let mixedIndex = 0;
  
  await benchmark.run("Old: Mixed Workload", async () => {
    await oldBot.processUpdate(mixedUpdates[mixedIndex % mixedUpdates.length]);
    mixedIndex++;
  }, 1000);

  mixedIndex = 0;
  await benchmark.run("New: Mixed Workload", async () => {
    await newBot.processUpdate(mixedUpdates[mixedIndex % mixedUpdates.length]);
    mixedIndex++;
  }, 1000);

  // Print results
  benchmark.printResults();

  // Compare architectures
  benchmark.compareResults("Old: Command Processing", "New: Command Processing");
  benchmark.compareResults("Old: Twitter URL Processing", "New: Twitter URL Processing");
  benchmark.compareResults("Old: General Message", "New: General Message");
  benchmark.compareResults("Old: Callback Query", "New: Callback Query");
  benchmark.compareResults("Old: Mixed Workload", "New: Mixed Workload");

  console.log("\n✅ Benchmark completed!");
}

if (require.main === module) {
  main().catch(console.error);
}

export { Benchmark, OldTelegramBot, NewTelegramBot };

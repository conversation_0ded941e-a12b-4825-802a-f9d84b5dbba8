/**
 * Tests for TelegramBotRouter
 */

import { describe, it, expect, beforeEach, vi } from "vitest";
import type TelegramBot from "node-telegram-bot-api";
import { TelegramBotRouter } from "../../core/telegram-bot-router";
import type { TelegramHandlerContext } from "../../core/telegram-handler-context";

// Mock dependencies
const mockBot = {
  sendMessage: vi.fn(),
  answerCallbackQuery: vi.fn(),
  answerInlineQuery: vi.fn(),
} as any;

const mockMessageRouter = {
  routeMessage: vi.fn(),
  getStats: vi.fn().mockReturnValue({ processorCount: 2 }),
} as any;

const mockCallbackRouter = {
  routeCallback: vi.fn(),
  getStats: vi.fn().mockReturnValue({ handlerCount: 3 }),
} as any;

const mockLogger = {
  info: vi.fn(),
  debug: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
} as any;

const mockContext: TelegramHandlerContext = {
  bot: mockBot,
  userService: {} as any,
  sessionService: {} as any,
  securityService: {} as any,
  logger: mockLogger,
  config: {
    token: "test-token",
    enablePolling: true,
  },
};

// Mock the routers
vi.mock("../../processors/message-router", () => ({
  TelegramMessageRouter: vi.fn().mockImplementation(() => mockMessageRouter),
}));

vi.mock("../../callbacks/callback-router", () => ({
  TelegramCallbackRouter: vi.fn().mockImplementation(() => mockCallbackRouter),
}));

describe("TelegramBotRouter", () => {
  let router: TelegramBotRouter;

  beforeEach(() => {
    router = new TelegramBotRouter(mockContext);
    vi.clearAllMocks();
  });

  it("should initialize with command handlers and routers", () => {
    expect(mockLogger.info).toHaveBeenCalledWith(
      "TelegramBotRouter initialized",
      expect.objectContaining({
        metadata: expect.objectContaining({
          commandHandlerCount: 5, // start, help, settings, status, create
          commands: expect.arrayContaining(["start", "help", "settings", "status", "create"]),
          messageProcessors: 2,
          callbackHandlers: 3,
        }),
      })
    );
  });

  it("should route message updates", async () => {
    const update = {
      update_id: 1,
      message: {
        message_id: 1,
        date: Date.now(),
        chat: { id: 123, type: "private" },
        from: {
          id: 456,
          is_bot: false,
          first_name: "John",
          username: "john_doe",
        },
        text: "Hello, bot!",
      },
    };

    mockMessageRouter.routeMessage.mockResolvedValue(true);

    await router.routeUpdate(update);

    expect(mockLogger.debug).toHaveBeenCalledWith(
      "Routing update",
      expect.objectContaining({
        metadata: expect.objectContaining({
          updateId: 1,
          hasMessage: true,
          hasCallbackQuery: false,
        }),
      })
    );

    expect(mockMessageRouter.routeMessage).toHaveBeenCalledWith(
      update.message,
      mockContext
    );
  });

  it("should route callback query updates", async () => {
    const update = {
      update_id: 2,
      callback_query: {
        id: "callback-1",
        from: {
          id: 456,
          is_bot: false,
          first_name: "John",
          username: "john_doe",
        },
        message: {
          message_id: 1,
          date: Date.now(),
          chat: { id: 123, type: "private" },
        },
        data: "copy:user123",
      },
    };

    mockCallbackRouter.routeCallback.mockResolvedValue(true);

    await router.routeUpdate(update);

    expect(mockCallbackRouter.routeCallback).toHaveBeenCalledWith(
      update.callback_query,
      mockContext
    );
  });

  it("should handle command messages", async () => {
    const update = {
      update_id: 3,
      message: {
        message_id: 1,
        date: Date.now(),
        chat: { id: 123, type: "private" },
        from: {
          id: 456,
          is_bot: false,
          first_name: "John",
          username: "john_doe",
        },
        text: "/start",
      },
    };

    await router.routeUpdate(update);

    expect(mockLogger.debug).toHaveBeenCalledWith(
      "Routing to command handler",
      expect.objectContaining({
        metadata: expect.objectContaining({
          commandName: "start",
        }),
      })
    );

    // Should not route to message router for commands
    expect(mockMessageRouter.routeMessage).not.toHaveBeenCalled();
  });

  it("should handle unknown commands", async () => {
    const update = {
      update_id: 4,
      message: {
        message_id: 1,
        date: Date.now(),
        chat: { id: 123, type: "private" },
        from: {
          id: 456,
          is_bot: false,
          first_name: "John",
          username: "john_doe",
        },
        text: "/unknown",
      },
    };

    await router.routeUpdate(update);

    expect(mockLogger.warn).toHaveBeenCalledWith(
      "Unknown command",
      expect.objectContaining({
        metadata: expect.objectContaining({
          commandName: "unknown",
          availableCommands: expect.arrayContaining(["start", "help", "settings", "status", "create"]),
        }),
      })
    );

    expect(mockBot.sendMessage).toHaveBeenCalledWith(
      123,
      "❓ Unknown command: /unknown\n\nUse /help to see available commands."
    );
  });

  it("should handle invalid command format", async () => {
    const update = {
      update_id: 5,
      message: {
        message_id: 1,
        date: Date.now(),
        chat: { id: 123, type: "private" },
        from: {
          id: 456,
          is_bot: false,
          first_name: "John",
          username: "john_doe",
        },
        text: "/", // Invalid command
      },
    };

    await router.routeUpdate(update);

    expect(mockLogger.warn).toHaveBeenCalledWith(
      "Invalid command format",
      expect.objectContaining({
        metadata: expect.objectContaining({
          commandText: "/",
        }),
      })
    );

    // Should not send any message for invalid format
    expect(mockBot.sendMessage).not.toHaveBeenCalled();
  });

  it("should handle inline queries", async () => {
    const update = {
      update_id: 6,
      inline_query: {
        id: "inline-1",
        from: {
          id: 456,
          is_bot: false,
          first_name: "John",
          username: "john_doe",
        },
        query: "search term",
        offset: "",
      },
    };

    await router.routeUpdate(update);

    expect(mockLogger.debug).toHaveBeenCalledWith(
      "Inline query received (not implemented)",
      expect.objectContaining({
        metadata: expect.objectContaining({
          queryId: "inline-1",
          queryText: "search term",
        }),
      })
    );

    expect(mockBot.answerInlineQuery).toHaveBeenCalledWith(
      "inline-1",
      [],
      {
        cache_time: 300,
        is_personal: true,
      }
    );
  });

  it("should handle edited messages", async () => {
    const update = {
      update_id: 7,
      edited_message: {
        message_id: 1,
        date: Date.now(),
        chat: { id: 123, type: "private" },
        from: {
          id: 456,
          is_bot: false,
          first_name: "John",
          username: "john_doe",
        },
        text: "Edited message",
        edit_date: Date.now(),
      },
    };

    await router.routeUpdate(update);

    expect(mockLogger.debug).toHaveBeenCalledWith(
      "Edited message received (ignored)",
      expect.objectContaining({
        metadata: expect.objectContaining({
          messageId: 1,
        }),
      })
    );

    // Should not process edited messages
    expect(mockMessageRouter.routeMessage).not.toHaveBeenCalled();
  });

  it("should handle unknown update types", async () => {
    const update = {
      update_id: 8,
      unknown_field: {
        some: "data",
      },
    };

    await router.routeUpdate(update);

    expect(mockLogger.warn).toHaveBeenCalledWith(
      "Unknown update type received",
      expect.objectContaining({
        metadata: expect.objectContaining({
          updateId: 8,
          updateKeys: ["update_id", "unknown_field"],
        }),
      })
    );
  });

  it("should handle messages without sender", async () => {
    const update = {
      update_id: 9,
      message: {
        message_id: 1,
        date: Date.now(),
        chat: { id: 123, type: "private" },
        // No from field
        text: "Hello",
      },
    };

    await router.routeUpdate(update);

    expect(mockLogger.warn).toHaveBeenCalledWith(
      "Message received without sender information",
      expect.objectContaining({
        chatId: "123",
        metadata: expect.objectContaining({
          messageId: 1,
        }),
      })
    );

    expect(mockMessageRouter.routeMessage).not.toHaveBeenCalled();
  });

  it("should handle command handler errors", async () => {
    const update = {
      update_id: 10,
      message: {
        message_id: 1,
        date: Date.now(),
        chat: { id: 123, type: "private" },
        from: {
          id: 456,
          is_bot: false,
          first_name: "John",
          username: "john_doe",
        },
        text: "/start",
      },
    };

    // Mock command handler to throw error
    const startHandler = router['commandHandlers'].get('start');
    if (startHandler) {
      vi.spyOn(startHandler, 'handle').mockRejectedValue(new Error("Handler error"));
    }

    await router.routeUpdate(update);

    expect(mockLogger.error).toHaveBeenCalledWith(
      "Command handler error",
      expect.objectContaining({
        metadata: expect.objectContaining({
          commandName: "start",
          error: "Handler error",
        }),
      })
    );

    expect(mockBot.sendMessage).toHaveBeenCalledWith(
      123,
      "❌ Sorry, something went wrong processing your command. Please try again."
    );
  });

  it("should provide router statistics", () => {
    const stats = router.getStats();

    expect(stats).toEqual({
      commandHandlers: 5,
      messageProcessors: 2,
      callbackHandlers: 3,
      commands: expect.arrayContaining(["start", "help", "settings", "status", "create"]),
    });
  });

  it("should allow adding custom command handlers", () => {
    const customHandler = {
      getCommand: () => "custom",
      getDescription: () => "Custom command",
      handle: vi.fn(),
    };

    router.addCommandHandler(customHandler);

    expect(mockLogger.info).toHaveBeenCalledWith(
      "Custom command handler added",
      expect.objectContaining({
        metadata: expect.objectContaining({
          command: "custom",
          description: "Custom command",
        }),
      })
    );

    const stats = router.getStats();
    expect(stats.commandHandlers).toBe(6);
    expect(stats.commands).toContain("custom");
  });

  it("should allow removing command handlers", () => {
    const removed = router.removeCommandHandler("help");

    expect(removed).toBe(true);
    expect(mockLogger.info).toHaveBeenCalledWith(
      "Command handler removed",
      expect.objectContaining({
        metadata: expect.objectContaining({
          command: "help",
        }),
      })
    );

    const stats = router.getStats();
    expect(stats.commandHandlers).toBe(4);
    expect(stats.commands).not.toContain("help");
  });
});

/**
 * Test setup for Telegram bot tests
 */

import { vi } from "vitest";

// Mock environment variables
process.env.TELEGRAM_BOT_TOKEN = "test-token";
process.env.NODE_ENV = "test";

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: vi.fn(),
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
};

// Mock Date.now for consistent timestamps in tests
const mockDate = new Date("2024-01-01T00:00:00.000Z");
vi.setSystemTime(mockDate);

// Global test utilities
global.createMockTelegramUser = (overrides = {}) => ({
  id: 456,
  is_bot: false,
  first_name: "<PERSON>",
  username: "john_doe",
  ...overrides,
});

global.createMockMessage = (overrides = {}) => ({
  message_id: 1,
  date: Math.floor(Date.now() / 1000),
  chat: { id: 123, type: "private" as const },
  from: global.createMockTelegramUser(),
  text: "Test message",
  ...overrides,
});

global.createMockCallbackQuery = (overrides = {}) => ({
  id: "callback-1",
  from: global.createMockTelegramUser(),
  message: global.createMockMessage(),
  data: "test:callback",
  ...overrides,
});

global.createMockUpdate = (overrides = {}) => ({
  update_id: 1,
  message: global.createMockMessage(),
  ...overrides,
});

// Mock Prisma client
vi.mock("../../../db-utils", () => ({
  prisma: {
    user: {
      findUnique: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
    },
    telegramUser: {
      findUnique: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
    },
    plan: {
      findUnique: vi.fn(),
    },
  },
}));

// Mock external services
vi.mock("../../../telegram-benji-agent", () => ({
  getTelegramBenjiForUser: vi.fn().mockResolvedValue({
    generateTelegramPost: vi.fn().mockResolvedValue({
      textStream: (async function* () {
        yield "Generated ";
        yield "content ";
        yield "here";
      })(),
      recordUsage: vi.fn(),
    }),
    generateTelegramResponse: vi.fn().mockResolvedValue({
      textStream: (async function* () {
        yield "AI response ";
        yield "content";
      })(),
      recordUsage: vi.fn(),
    }),
    generateTelegramQuickReply: vi.fn().mockResolvedValue({
      textStream: (async function* () {
        yield "Quick reply ";
        yield "content";
      })(),
      recordUsage: vi.fn(),
    }),
    generateTelegramEnhancedResponse: vi.fn().mockResolvedValue({
      textStream: (async function* () {
        yield "Enhanced ";
        yield "response";
      })(),
      recordUsage: vi.fn(),
    }),
  }),
}));

vi.mock("../../../rate-limiting", () => ({
  checkTelegramUserRateLimit: vi.fn().mockResolvedValue({
    allowed: true,
    remaining: 10,
    resetTime: Date.now() + 60000,
  }),
  checkTelegramFeatureLimit: vi.fn().mockResolvedValue({
    allowed: true,
    remaining: 100,
    resetTime: Date.now() + 86400000,
  }),
  recordTelegramFeatureUsage: vi.fn(),
  checkTelegramSpam: vi.fn().mockReturnValue({
    isSpam: false,
    reason: null,
  }),
}));

vi.mock("../../../twitter-client", () => ({
  twitterClient: {
    validateTwitterUrl: vi.fn().mockReturnValue(true),
    getTweetFromUrl: vi.fn().mockResolvedValue({
      id: "1234567890",
      text: "This is a sample tweet content",
      author: {
        username: "testuser",
        name: "Test User",
      },
    }),
  },
}));

// Mock validation utilities
vi.mock("../../utils/telegram-validator", () => ({
  validateTwitterUrl: vi.fn().mockReturnValue({
    isValid: true,
    error: null,
  }),
  sanitizeText: vi.fn().mockImplementation((text) => text),
  validateMessage: vi.fn().mockReturnValue({
    isValid: true,
    errors: [],
  }),
  hasSecurityRisk: vi.fn().mockReturnValue({
    hasRisk: false,
    reason: null,
  }),
  shouldRejectBasedOnContent: vi.fn().mockReturnValue({
    shouldReject: false,
    reason: null,
  }),
  parseCommand: vi.fn().mockImplementation((text) => {
    const match = text.match(/^\/([a-zA-Z0-9_]+)(.*)$/);
    return match ? { command: match[1], args: match[2].trim() } : null;
  }),
  sanitizeCallbackData: vi.fn().mockImplementation((data) => data),
}));

// Mock formatter utilities
vi.mock("../../utils/telegram-formatter", () => ({
  formatForTelegram: vi.fn().mockImplementation((text) => [text]),
}));

// Cleanup after each test
afterEach(() => {
  vi.clearAllMocks();
});

// Global type declarations for test utilities
declare global {
  function createMockTelegramUser(overrides?: any): any;
  function createMockMessage(overrides?: any): any;
  function createMockCallbackQuery(overrides?: any): any;
  function createMockUpdate(overrides?: any): any;
}

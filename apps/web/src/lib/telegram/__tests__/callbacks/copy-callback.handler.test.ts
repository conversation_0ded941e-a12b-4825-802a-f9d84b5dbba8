/**
 * Tests for CopyCallbackHandler
 */

import { describe, it, expect, beforeEach, vi } from "vitest";
import type TelegramBot from "node-telegram-bot-api";
import { CopyCallbackHandler } from "../../callbacks/copy-callback.handler";
import type { TelegramHandlerContext } from "../../core/telegram-handler-context";

// Mock dependencies
const mockBot = {
  sendMessage: vi.fn(),
  answerCallbackQuery: vi.fn(),
} as any;

const mockSessionService = {
  getSessionData: vi.fn(),
} as any;

const mockLogger = {
  logCallback: vi.fn(),
  info: vi.fn(),
  debug: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
} as any;

const mockContext: TelegramHandlerContext = {
  bot: mockBot,
  sessionService: mockSessionService,
  userService: {} as any,
  securityService: {} as any,
  logger: mockLogger,
  config: {
    token: "test-token",
    enablePolling: true,
  },
};

describe("CopyCallbackHandler", () => {
  let handler: CopyCallbackHandler;

  beforeEach(() => {
    handler = new CopyCallbackHandler();
    vi.clearAllMocks();
  });

  it("should have correct callback prefix and description", () => {
    expect(handler.getCallbackPrefix()).toBe("copy");
    expect(handler.getDescription()).toBe("Handle copy button clicks for generated content");
  });

  it("should handle copy callback data", () => {
    expect(handler.canHandle("copy:user123")).toBe(true);
    expect(handler.canHandle("regenerate:user123")).toBe(false);
    expect(handler.canHandle("enhance:user123")).toBe(false);
  });

  it("should handle copy for create command", async () => {
    const query: TelegramBot.CallbackQuery = {
      id: "callback-1",
      from: {
        id: 456,
        is_bot: false,
        first_name: "John",
        username: "john_doe",
      },
      message: {
        message_id: 1,
        date: Date.now(),
        chat: { id: 123, type: "private" },
      },
      data: "copy:tg-user-1",
    };

    const mockSessionData = {
      type: "create_command",
      responseText: "This is a generated post about productivity tips.",
      prompt: "Write a post about productivity tips",
      timestamp: Date.now(),
      telegramUserId: "tg-user-1",
    };

    mockSessionService.getSessionData.mockResolvedValue(mockSessionData);

    await handler.handle(query, mockContext);

    expect(mockLogger.logCallback).toHaveBeenCalledWith(
      "copy",
      "456",
      "123",
      true
    );

    expect(mockBot.answerCallbackQuery).toHaveBeenCalledWith(
      "callback-1",
      { text: "Preparing content..." }
    );

    expect(mockSessionService.getSessionData).toHaveBeenCalledWith("tg-user-1");

    expect(mockBot.sendMessage).toHaveBeenCalledWith(
      123,
      expect.stringContaining("📋 *Copy & Share Your Post:*"),
      expect.objectContaining({
        parse_mode: "Markdown",
        disable_web_page_preview: true,
        reply_markup: expect.objectContaining({
          inline_keyboard: expect.arrayContaining([
            expect.arrayContaining([
              expect.objectContaining({
                text: "🔄 Generate New",
                callback_data: expect.stringContaining("regenerate:"),
              }),
            ]),
          ]),
        }),
      })
    );

    // Check that Twitter intent URL is included
    const sentMessage = mockBot.sendMessage.mock.calls[0][1];
    expect(sentMessage).toContain("https://twitter.com/intent/tweet?text=");
    expect(sentMessage).toContain(encodeURIComponent(mockSessionData.responseText));
  });

  it("should handle copy for Twitter URL", async () => {
    const query: TelegramBot.CallbackQuery = {
      id: "callback-1",
      from: {
        id: 456,
        is_bot: false,
        first_name: "John",
        username: "john_doe",
      },
      message: {
        message_id: 1,
        date: Date.now(),
        chat: { id: 123, type: "private" },
      },
      data: "copy:tg-user-1",
    };

    const mockSessionData = {
      type: "twitter_url",
      responseText: "Great insights! Thanks for sharing this perspective.",
      twitterUrl: "https://twitter.com/user/status/1234567890",
      timestamp: Date.now(),
      telegramUserId: "tg-user-1",
    };

    mockSessionService.getSessionData.mockResolvedValue(mockSessionData);

    await handler.handle(query, mockContext);

    expect(mockBot.sendMessage).toHaveBeenCalledWith(
      123,
      expect.stringContaining("🚀 *Send your reply on Twitter:*"),
      expect.objectContaining({
        parse_mode: "Markdown",
        disable_web_page_preview: true,
        reply_markup: expect.objectContaining({
          inline_keyboard: expect.arrayContaining([
            expect.arrayContaining([
              expect.objectContaining({
                text: "🐦 View Original",
                url: mockSessionData.twitterUrl,
              }),
            ]),
          ]),
        }),
      })
    );

    // Check that Twitter reply URL is included
    const sentMessage = mockBot.sendMessage.mock.calls[0][1];
    expect(sentMessage).toContain("https://twitter.com/intent/tweet?in_reply_to=1234567890");
    expect(sentMessage).toContain(encodeURIComponent(mockSessionData.responseText));
  });

  it("should handle fallback for unknown session type", async () => {
    const query: TelegramBot.CallbackQuery = {
      id: "callback-1",
      from: {
        id: 456,
        is_bot: false,
        first_name: "John",
        username: "john_doe",
      },
      message: {
        message_id: 1,
        date: Date.now(),
        chat: { id: 123, type: "private" },
      },
      data: "copy:tg-user-1",
    };

    const mockSessionData = {
      // No type specified
      responseText: "Some generated content",
      timestamp: Date.now(),
      telegramUserId: "tg-user-1",
    };

    mockSessionService.getSessionData.mockResolvedValue(mockSessionData);

    await handler.handle(query, mockContext);

    // Should fallback to create command copy since it has responseText but no twitterUrl
    expect(mockBot.sendMessage).toHaveBeenCalledWith(
      123,
      expect.stringContaining("📋 *Copy & Share Your Post:*"),
      expect.any(Object)
    );
  });

  it("should handle expired session", async () => {
    const query: TelegramBot.CallbackQuery = {
      id: "callback-1",
      from: {
        id: 456,
        is_bot: false,
        first_name: "John",
        username: "john_doe",
      },
      message: {
        message_id: 1,
        date: Date.now(),
        chat: { id: 123, type: "private" },
      },
      data: "copy:tg-user-1",
    };

    mockSessionService.getSessionData.mockResolvedValue(null);

    await handler.handle(query, mockContext);

    expect(mockBot.sendMessage).toHaveBeenCalledWith(
      123,
      "❌ Session expired. Please regenerate the content or try again."
    );
  });

  it("should handle invalid Twitter URL in session", async () => {
    const query: TelegramBot.CallbackQuery = {
      id: "callback-1",
      from: {
        id: 456,
        is_bot: false,
        first_name: "John",
        username: "john_doe",
      },
      message: {
        message_id: 1,
        date: Date.now(),
        chat: { id: 123, type: "private" },
      },
      data: "copy:tg-user-1",
    };

    const mockSessionData = {
      type: "twitter_url",
      responseText: "Great insights!",
      twitterUrl: "https://twitter.com/invalid-url", // Invalid URL without status
      timestamp: Date.now(),
      telegramUserId: "tg-user-1",
    };

    mockSessionService.getSessionData.mockResolvedValue(mockSessionData);

    await handler.handle(query, mockContext);

    expect(mockBot.sendMessage).toHaveBeenCalledWith(
      123,
      "❌ Could not extract tweet ID from URL. Please try with a direct tweet link."
    );
  });

  it("should handle missing query data", async () => {
    const query: TelegramBot.CallbackQuery = {
      id: "callback-1",
      from: {
        id: 456,
        is_bot: false,
        first_name: "John",
        username: "john_doe",
      },
      message: {
        message_id: 1,
        date: Date.now(),
        chat: { id: 123, type: "private" },
      },
      // No data property
    };

    await handler.handle(query, mockContext);

    // Should return early due to validation failure
    expect(mockSessionService.getSessionData).not.toHaveBeenCalled();
    expect(mockBot.sendMessage).not.toHaveBeenCalled();
  });

  it("should handle service errors", async () => {
    const query: TelegramBot.CallbackQuery = {
      id: "callback-1",
      from: {
        id: 456,
        is_bot: false,
        first_name: "John",
        username: "john_doe",
      },
      message: {
        message_id: 1,
        date: Date.now(),
        chat: { id: 123, type: "private" },
      },
      data: "copy:tg-user-1",
    };

    const error = new Error("Database connection failed");
    mockSessionService.getSessionData.mockRejectedValue(error);

    await handler.handle(query, mockContext);

    expect(mockLogger.error).toHaveBeenCalledWith(
      "Error in copy callback handler",
      expect.objectContaining({
        chatId: "123",
        telegramUserId: "tg-user-1",
        metadata: expect.objectContaining({
          error: "Database connection failed",
        }),
      })
    );

    expect(mockBot.sendMessage).toHaveBeenCalledWith(
      123,
      "❌ Sorry, something went wrong. Please try again."
    );
  });
});

/**
 * Message Router
 * Routes Telegram messages to appropriate processors
 */

import type TelegramBot from "node-telegram-bot-api";
import type { TelegramMessageProcessor } from "./base/message-processor.interface";
import type { TelegramHandlerContext } from "../core/telegram-handler-context";
import { TwitterUrlProcessor } from "./twitter-url.processor";
import { GeneralMessageProcessor } from "./general-message.processor";

/**
 * Router for message processing
 * Determines which processor should handle each message type
 */
export class TelegramMessageRouter {
  private processors: TelegramMessageProcessor[] = [];

  constructor(context: TelegramHandlerContext) {
    // Initialize processors in priority order (higher priority first)
    this.processors = [
      new TwitterUrlProcessor(),
      new GeneralMessageProcessor(),
    ];

    // Sort by priority (highest first)
    this.processors.sort((a, b) => b.getPriority() - a.getPriority());

    context.logger.info("Message router initialized", {
      metadata: {
        processorCount: this.processors.length,
        processors: this.processors.map(p => ({
          description: p.getDescription(),
          priority: p.getPriority(),
        })),
      },
    });
  }

  /**
   * Route a message to the appropriate processor
   * @param message - The Telegram message to route
   * @param context - Handler context
   * @returns True if message was handled, false otherwise
   */
  async routeMessage(
    message: TelegramBot.Message,
    context: TelegramHandlerContext
  ): Promise<boolean> {
    const chatId = message.chat.id;
    const telegramUser = message.from;

    if (!telegramUser) {
      context.logger.warn("Message router: No user information in message", {
        chatId: chatId.toString(),
        metadata: { messageId: message.message_id },
      });
      return false;
    }

    // Validate message
    if (!this.validateMessage(message, context)) {
      return false;
    }

    // Security check
    const securityCheck = await context.securityService.checkMessageSecurity(
      message,
      telegramUser.id.toString()
    );

    if (!securityCheck.allowed) {
      context.logger.warn("Message router: Security check failed", {
        telegramUserId: telegramUser.id.toString(),
        chatId: chatId.toString(),
        metadata: {
          reason: securityCheck.reason,
          securityLevel: securityCheck.securityLevel,
          issues: securityCheck.issues,
        },
      });

      await context.bot.sendMessage(
        chatId,
        `🚫 Message blocked: ${securityCheck.reason}`
      );
      return true; // Message was "handled" (blocked)
    }

    // Find the first processor that can handle this message
    for (const processor of this.processors) {
      if (processor.canHandle(message)) {
        context.logger.debug("Message router: Routing to processor", {
          telegramUserId: telegramUser.id.toString(),
          chatId: chatId.toString(),
          metadata: {
            processorDescription: processor.getDescription(),
            processorPriority: processor.getPriority(),
            messageType: this.getMessageType(message),
          },
        });

        try {
          await processor.process(message, context);
          
          context.logger.debug("Message router: Processing completed", {
            telegramUserId: telegramUser.id.toString(),
            chatId: chatId.toString(),
            metadata: {
              processorDescription: processor.getDescription(),
              success: true,
            },
          });

          return true;
        } catch (error) {
          context.logger.error("Message router: Processor error", {
            telegramUserId: telegramUser.id.toString(),
            chatId: chatId.toString(),
            metadata: {
              processorDescription: processor.getDescription(),
              error: error instanceof Error ? error.message : String(error),
            },
          });

          // Send generic error message to user
          await context.bot.sendMessage(
            chatId,
            "❌ Sorry, I encountered an error processing your message. Please try again."
          );

          return true; // Message was handled (with error)
        }
      }
    }

    // No processor could handle this message
    context.logger.warn("Message router: No processor found for message", {
      telegramUserId: telegramUser.id.toString(),
      chatId: chatId.toString(),
      metadata: {
        messageType: this.getMessageType(message),
        hasText: !!message.text,
        textLength: message.text?.length || 0,
        textPreview: message.text?.substring(0, 50) || "N/A",
      },
    });

    // Send helpful message to user
    await context.bot.sendMessage(
      chatId,
      "🤔 I'm not sure how to handle that message. Try:\n\n• Sending a Twitter URL for AI replies\n• Asking me a question\n• Using /help for available commands"
    );

    return false;
  }

  /**
   * Validate message structure and content
   * @param message - The message to validate
   * @param context - Handler context
   * @returns True if message is valid
   */
  private validateMessage(
    message: TelegramBot.Message,
    context: TelegramHandlerContext
  ): boolean {
    const chatId = message.chat.id;

    // Check basic message structure
    if (!message.chat?.id) {
      context.logger.warn("Message router: Invalid message - no chat ID", {
        metadata: { messageId: message.message_id },
      });
      return false;
    }

    if (!message.from) {
      context.logger.warn("Message router: Invalid message - no sender", {
        chatId: chatId.toString(),
        metadata: { messageId: message.message_id },
      });
      return false;
    }

    // Check if message has any processable content
    if (!message.text && !message.photo && !message.document && !message.voice) {
      context.logger.debug("Message router: No processable content", {
        chatId: chatId.toString(),
        metadata: { messageId: message.message_id },
      });
      return false;
    }

    return true;
  }

  /**
   * Get message type for logging
   * @param message - The message to analyze
   * @returns Message type string
   */
  private getMessageType(message: TelegramBot.Message): string {
    if (message.text?.startsWith("/")) {
      return "command";
    }

    if (message.text && /https?:\/\/(twitter\.com|x\.com)\/\w+\/status\/\d+/.test(message.text)) {
      return "twitter_url";
    }

    if (message.text) {
      return "text";
    }

    if (message.photo) {
      return "photo";
    }

    if (message.document) {
      return "document";
    }

    if (message.voice) {
      return "voice";
    }

    if (message.video) {
      return "video";
    }

    return "unknown";
  }

  /**
   * Get statistics about message routing
   * @returns Routing statistics
   */
  getStats(): {
    processorCount: number;
    processors: Array<{
      description: string;
      priority: number;
    }>;
  } {
    return {
      processorCount: this.processors.length,
      processors: this.processors.map(processor => ({
        description: processor.getDescription(),
        priority: processor.getPriority(),
      })),
    };
  }

  /**
   * Add a custom processor
   * @param processor - The processor to add
   */
  addProcessor(processor: TelegramMessageProcessor): void {
    this.processors.push(processor);
    
    // Re-sort by priority
    this.processors.sort((a, b) => b.getPriority() - a.getPriority());
  }

  /**
   * Remove a processor by description
   * @param description - The description of the processor to remove
   * @returns True if processor was removed
   */
  removeProcessor(description: string): boolean {
    const initialLength = this.processors.length;
    this.processors = this.processors.filter(
      processor => processor.getDescription() !== description
    );
    return this.processors.length < initialLength;
  }
}

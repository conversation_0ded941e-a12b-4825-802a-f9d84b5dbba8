/**
 * Twitter URL Processor
 * Handles Twitter/X URL messages for Telegram bot
 */

import type TelegramBot from "node-telegram-bot-api";
import { BaseMessageProcessor } from "./base/message-processor.interface";
import type { TelegramHandlerContext } from "../core/telegram-handler-context";
import { validateTwitterUrl } from "../utils/telegram-validator";
import { getTelegramBenjiForUser } from "../../telegram-benji-agent";
import { checkTelegramUserRateLimit } from "../../telegram-rate-limiting";

/**
 * Processor for Twitter/X URL messages
 * Extracts tweet content and generates AI responses
 */
export class TwitterUrlProcessor extends BaseMessageProcessor {
  constructor() {
    super(90, "Process Twitter/X URLs and generate AI responses"); // High priority
  }

  canHandle(message: TelegramBot.Message): boolean {
    if (!message.text) return false;
    
    // Check if message contains Twitter/X URL
    const twitterUrlRegex = /https?:\/\/(twitter\.com|x\.com)\/\w+\/status\/\d+/;
    return twitterUrlRegex.test(message.text);
  }

  async process(message: TelegramBot.Message, context: TelegramHandlerContext): Promise<void> {
    const chatId = message.chat.id;
    const telegramUser = message.from;
    const twitterUrl = message.text;

    if (!telegramUser || !twitterUrl) {
      context.logger.warn("Twitter URL processor: Missing user or URL", {
        chatId: chatId.toString(),
      });
      return;
    }

    context.logger.logMessageProcessing(
      "twitter_url",
      telegramUser.id.toString(),
      chatId.toString(),
      true
    );

    try {
      // Validate Twitter URL
      const urlValidation = validateTwitterUrl(twitterUrl);
      if (!urlValidation.isValid) {
        context.logger.warn("Invalid Twitter URL provided", {
          telegramUserId: telegramUser.id.toString(),
          chatId: chatId.toString(),
          metadata: { error: urlValidation.error, url: twitterUrl },
        });

        await context.bot.sendMessage(
          chatId,
          `❌ Invalid Twitter URL: ${urlValidation.error}`
        );
        return;
      }

      // Get or create user
      const dbTelegramUser = await context.userService.getOrCreateTelegramUser(telegramUser);

      if (!dbTelegramUser.userId) {
        await this.sendAccountLinkingMessage(chatId, context);
        return;
      }

      // Check rate limits
      const telegramRateLimit = await checkTelegramUserRateLimit(
        telegramUser.id.toString(),
        "twitter_url"
      );

      if (!telegramRateLimit.allowed) {
        await context.bot.sendMessage(
          chatId,
          `⚠️ Rate limit exceeded. Please wait ${Math.ceil(
            (telegramRateLimit.resetTime ?? 0) / 1000 / 60
          )} minutes before sending another Twitter URL.`
        );
        return;
      }

      // Send typing indicator
      await this.sendTypingIndicator(chatId, context);

      // Get Telegram Benji agent for the user
      const benji = await getTelegramBenjiForUser(
        dbTelegramUser.userId,
        dbTelegramUser.id,
        chatId.toString()
      );

      // Extract tweet content
      const tweetContent = await this.extractTweetContent(twitterUrl, context);

      if (!tweetContent) {
        await context.bot.sendMessage(
          chatId,
          "❌ Could not extract tweet content. Please check the URL and try again."
        );
        return;
      }

      // Generate AI response optimized for Telegram
      const result = await benji.generateTelegramQuickReply(
        tweetContent,
        twitterUrl
      );

      // Convert streaming result to text
      let responseText = "";
      for await (const chunk of result.textStream) {
        responseText += chunk;
      }

      context.logger.debug("Generated Twitter response", {
        telegramUserId: dbTelegramUser.id,
        metadata: {
          responseLength: responseText.length,
          responsePreview: responseText.substring(0, 100) + "...",
          hasContent: !!responseText.trim(),
          tweetUrl: twitterUrl,
        },
      });

      // Record usage (handled by the AI service internally)

      if (responseText.trim()) {
        // Escape special Markdown characters in tweet content to prevent parsing errors
        const escapedTweetContent = tweetContent
          .substring(0, 200)
          .replace(/[_*[\]()~`>#+=|{}.!-]/g, '\\$&');
        const truncatedContent = escapedTweetContent + (tweetContent.length > 200 ? "..." : "");
        
        // Escape special Markdown characters in response text
        const escapedResponseText = responseText.replace(/[_*[\]()~`>#+=|{}.!-]/g, '\\$&');
        
        // Format response for Telegram
        const formattedResponse = `🐦 *AI Reply Generated*\n\nOriginal Tweet:\n"${truncatedContent}"\n\nSuggested Reply:\n${escapedResponseText}`;

        // Store data in session for callback buttons
        const sessionData = {
          twitterUrl,
          responseText,
          timestamp: Date.now(),
          type: "twitter_url" as const,
        };

        context.logger.debug("Storing Twitter session data", {
          telegramUserId: dbTelegramUser.id,
          metadata: {
            responseTextLength: responseText.length,
            responseTextPreview: responseText.substring(0, 50) + "...",
            twitterUrl,
          },
        });

        // Store in user session for callback handling
        await context.sessionService.storeSessionData(dbTelegramUser.id, sessionData);

        await context.bot.sendMessage(chatId, formattedResponse, {
          parse_mode: "Markdown",
          reply_markup: {
            inline_keyboard: [
              [
                {
                  text: "🔄 Regenerate",
                  callback_data: `regenerate:${dbTelegramUser.id}`,
                },
                {
                  text: "✨ Enhance",
                  callback_data: `enhance:${dbTelegramUser.id}`,
                },
              ],
              [
                {
                  text: "🚀 Send Reply",
                  callback_data: `copy:${dbTelegramUser.id}`,
                },
              ],
            ],
          },
        });

        context.logger.info("Twitter URL processing completed successfully", {
          chatId: chatId.toString(),
          userId: telegramUser.id.toString(),
          metadata: { 
            twitterUrl,
            responseLength: responseText.length,
            tweetContentLength: tweetContent.length,
          },
        });
      } else {
        await context.bot.sendMessage(
          chatId,
          "❌ Sorry, I couldn't generate a response for this tweet. Please try again."
        );

        context.logger.warn("Empty response generated for Twitter URL", {
          telegramUserId: dbTelegramUser.id,
          metadata: { twitterUrl, tweetContent: tweetContent.substring(0, 100) },
        });
      }
    } catch (error) {
      context.logger.error("Error processing Twitter URL", {
        chatId: chatId.toString(),
        telegramUserId: telegramUser.id.toString(),
        metadata: { 
          error: error instanceof Error ? error.message : String(error),
          twitterUrl,
        },
      });

      await this.sendError(chatId, error as Error, context);
    }
  }

  /**
   * Extract tweet content from URL using existing Twitter API client
   */
  private async extractTweetContent(url: string, context: TelegramHandlerContext): Promise<string | null> {
    try {
      context.logger.debug("Extracting tweet content", {
        metadata: { url },
      });

      // Import and use the existing Twitter API client
      const { twitterClient } = await import("../../twitter-client");

      // Validate the URL first
      if (!twitterClient.validateTwitterUrl(url)) {
        context.logger.error("Invalid Twitter URL format", {
          metadata: { url },
        });
        return null;
      }

      // Extract tweet content using the existing API client
      const tweet = await twitterClient.getTweetFromUrl(url);

      if (!tweet) {
        context.logger.error("Could not fetch tweet content", {
          metadata: { url },
        });
        return null;
      }

      context.logger.debug("Successfully extracted tweet content", {
        metadata: { 
          url,
          tweetId: tweet.id,
          contentLength: tweet.text?.length || 0,
        },
      });

      return tweet.text || null;
    } catch (error) {
      context.logger.error("Error extracting tweet content", {
        metadata: { 
          url,
          error: error instanceof Error ? error.message : String(error),
        },
      });
      return null;
    }
  }

  /**
   * Send account linking message for Twitter processing
   */
  private async sendAccountLinkingMessage(chatId: number, context: TelegramHandlerContext): Promise<void> {
    const linkingMessage = `🔗 *Account Required for Twitter Processing*

To process Twitter URLs and generate AI replies, you need to link your BuddyChip account.

*Why link your account?*
• Access to premium AI models
• Higher rate limits for Twitter processing
• Better response quality
• Conversation memory
• Priority support

Use /start to get started!`;

    const keyboard = [
      [{ text: "🔗 Link Account", callback_data: "link_account" }],
      [{ text: "🌐 Create Account", url: "https://buddychip.app" }],
      [{ text: "🎯 Try Demo", callback_data: "demo_mode" }],
    ];

    await context.bot.sendMessage(chatId, linkingMessage, {
      parse_mode: "Markdown",
      reply_markup: { inline_keyboard: keyboard },
    });
  }
}

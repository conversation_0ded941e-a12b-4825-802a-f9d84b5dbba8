/**
 * Telegram Security Service
 * Handles spam detection, rate limiting, and security validation for Telegram bot
 */

import type TelegramBot from "node-telegram-bot-api";
import type { TelegramLogger } from "../utils/telegram-logger";

export interface SecurityCheckResult {
  allowed: boolean;
  reason?: string;
  securityLevel: "HIGH" | "MEDIUM" | "LOW";
  issues: string[];
  metadata?: Record<string, any>;
}

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime?: number;
  reason?: string;
}

export interface SpamCheckResult {
  isSpam: boolean;
  reason?: string;
  confidence: number; // 0-1 scale
}

export interface SecurityConfig {
  enableRateLimit: boolean;
  enableSpamDetection: boolean;
  enableContentValidation: boolean;
  maxMessageLength?: number;
  maxUrlsPerMessage?: number;
  maxMentionsPerMessage?: number;
  rateLimits: {
    messagesPerMinute: number;
    commandsPerMinute: number;
    twitterUrlsPerMinute: number;
    callbacksPerMinute: number;
  };
}

/**
 * Service for handling Telegram bot security, spam detection, and rate limiting
 */
export class TelegramSecurityService {
  private readonly rateLimitStore = new Map<string, RateLimitEntry>();
  private readonly spamStore = new Map<string, SpamEntry>();
  private readonly blockedUsers = new Set<string>();
  private readonly suspiciousActivity = new Map<string, SuspiciousActivityEntry>();
  private readonly config: SecurityConfig;

  private readonly defaultConfig: SecurityConfig = {
    enableRateLimit: true,
    enableSpamDetection: true,
    enableContentValidation: true,
    maxMessageLength: 4000,
    maxUrlsPerMessage: 3,
    maxMentionsPerMessage: 5,
    rateLimits: {
      messagesPerMinute: 20,
      commandsPerMinute: 10,
      twitterUrlsPerMinute: 5,
      callbacksPerMinute: 30,
    },
  };

  constructor(
    private readonly logger: TelegramLogger,
    config: Partial<SecurityConfig> = {}
  ) {
    // Merge config with defaults
    this.config = { ...this.defaultConfig, ...config };

    // Start cleanup interval
    setInterval(() => this.cleanup(), 5 * 60 * 1000); // Every 5 minutes
  }

  /**
   * Perform comprehensive security check on a message
   * @param message - The Telegram message to check
   * @param userId - The user ID
   * @returns Security check result
   */
  async checkMessageSecurity(
    message: TelegramBot.Message,
    userId: string
  ): Promise<SecurityCheckResult> {
    const issues: string[] = [];
    let securityLevel: "HIGH" | "MEDIUM" | "LOW" = "HIGH";

    try {
      // Check if user is blocked
      if (this.blockedUsers.has(userId)) {
        return {
          allowed: false,
          reason: "User is blocked",
          securityLevel: "LOW",
          issues: ["User blocked"],
        };
      }

      // Rate limiting check
      if (this.config.enableRateLimit) {
        const rateLimitCheck = this.checkRateLimit(userId, "message");
        if (!rateLimitCheck.allowed) {
          issues.push("Rate limit exceeded");
          securityLevel = "MEDIUM";
        }
      }

      // Spam detection
      if (this.config.enableSpamDetection && message.text) {
        const spamCheck = this.checkSpam(userId, message.text);
        if (spamCheck.isSpam) {
          issues.push(`Spam detected: ${spamCheck.reason}`);
          securityLevel = "LOW";
        }
      }

      // Content validation
      if (this.config.enableContentValidation && message.text) {
        const contentCheck = this.validateContent(message.text);
        if (!contentCheck.valid) {
          issues.push(`Content validation failed: ${contentCheck.reason}`);
          securityLevel = "LOW";
        }
      }

      // Security risk detection
      const securityRisk = this.detectSecurityRisks(message);
      if (securityRisk.hasRisk) {
        issues.push(...securityRisk.risks);
        securityLevel = "LOW";
      }

      const allowed = issues.length === 0 || securityLevel !== "LOW";

      // Log security events
      if (!allowed || issues.length > 0) {
        this.logger.logSecurity("MESSAGE_SECURITY_CHECK", userId, message.chat.id.toString(), {
          allowed,
          issues,
          securityLevel,
          messageLength: message.text?.length || 0,
        });
      }

      return {
        allowed,
        reason: issues.length > 0 ? issues.join(", ") : undefined,
        securityLevel,
        issues,
        metadata: {
          messageLength: message.text?.length || 0,
          hasUrls: this.containsUrls(message.text || ""),
          hasMentions: this.containsMentions(message.text || ""),
        },
      };
    } catch (error) {
      this.logger.error("Error in security check", {
        userId,
        metadata: { error: error instanceof Error ? error.message : String(error) },
      });

      return {
        allowed: false,
        reason: "Security check failed",
        securityLevel: "LOW",
        issues: ["Internal security error"],
      };
    }
  }

  /**
   * Check rate limits for a user action
   * @param userId - The user ID
   * @param action - The action type
   * @returns Rate limit result
   */
  checkRateLimit(userId: string, action: string): RateLimitResult {
    const key = `${userId}:${action}`;
    const now = Date.now();
    const windowMs = 60 * 1000; // 1 minute window

    let entry = this.rateLimitStore.get(key);
    if (!entry) {
      entry = {
        count: 0,
        firstRequest: now,
        lastRequest: now,
        windowMs,
      };
      this.rateLimitStore.set(key, entry);
    }

    // Reset window if expired
    if (now - entry.firstRequest > windowMs) {
      entry.count = 0;
      entry.firstRequest = now;
    }

    entry.count++;
    entry.lastRequest = now;

    // Get limit for action
    const limit = this.getLimitForAction(action);
    const allowed = entry.count <= limit;
    const remaining = Math.max(0, limit - entry.count);
    const resetTime = entry.firstRequest + windowMs;

    if (!allowed) {
      this.logger.warn("Rate limit exceeded", {
        userId,
        metadata: { action, count: entry.count, limit },
      });
    }

    return {
      allowed,
      remaining,
      resetTime,
      reason: allowed ? undefined : `Rate limit exceeded for ${action}`,
    };
  }

  /**
   * Check for spam patterns in message
   * @param userId - The user ID
   * @param messageText - The message text
   * @returns Spam check result
   */
  checkSpam(userId: string, messageText: string): SpamCheckResult {
    const now = Date.now();
    let entry = this.spamStore.get(userId);

    if (!entry) {
      entry = {
        recentMessages: [],
        lastMessageTime: now,
        identicalCount: 0,
        warningCount: 0,
      };
      this.spamStore.set(userId, entry);
    }

    // Add current message to history
    entry.recentMessages.push({
      text: messageText,
      timestamp: now,
    });

    // Keep only last 10 messages
    entry.recentMessages = entry.recentMessages.slice(-10);
    entry.lastMessageTime = now;

    // Check for identical messages
    const identicalMessages = entry.recentMessages.filter(
      (msg) => msg.text === messageText && now - msg.timestamp < 60000 // Last minute
    );

    if (identicalMessages.length >= 3) {
      entry.identicalCount++;
      return {
        isSpam: true,
        reason: "Identical messages repeated",
        confidence: 0.9,
      };
    }

    // Check for rapid fire messages
    const recentMessages = entry.recentMessages.filter(
      (msg) => now - msg.timestamp < 10000 // Last 10 seconds
    );

    if (recentMessages.length > 5) {
      return {
        isSpam: true,
        reason: "Too many messages in short time",
        confidence: 0.8,
      };
    }

    // Check for excessive URLs or mentions
    const urlCount = (messageText.match(/https?:\/\/\S+/g) || []).length;
    const mentionCount = (messageText.match(/@\w+/g) || []).length;

    if (urlCount > (this.config.maxUrlsPerMessage ?? 0)) {
      return {
        isSpam: true,
        reason: "Too many URLs",
        confidence: 0.7,
      };
    }

    if (mentionCount > (this.config.maxMentionsPerMessage ?? 0)) {
      return {
        isSpam: true,
        reason: "Too many mentions",
        confidence: 0.6,
      };
    }

    return {
      isSpam: false,
      confidence: 0,
    };
  }

  /**
   * Validate message content for security issues
   * @param content - The content to validate
   * @returns Validation result
   */
  validateContent(content: string): { valid: boolean; reason?: string } {
    // Check length
    if (content.length > (this.config.maxMessageLength ?? 0)) {
      return { valid: false, reason: "Message too long" };
    }

    // Check for malicious patterns
    const maliciousPatterns = [
      /javascript:/i,
      /data:/i,
      /<script/i,
      /eval\(/i,
      /function\s*\(/i,
      /onclick=/i,
      /onerror=/i,
    ];

    for (const pattern of maliciousPatterns) {
      if (pattern.test(content)) {
        return { valid: false, reason: "Potentially malicious content detected" };
      }
    }

    return { valid: true };
  }

  /**
   * Detect security risks in message
   * @param message - The Telegram message
   * @returns Security risk result
   */
  detectSecurityRisks(message: TelegramBot.Message): { hasRisk: boolean; risks: string[] } {
    const risks: string[] = [];

    if (!message.text) {
      return { hasRisk: false, risks: [] };
    }

    // Check for suspicious patterns
    if (message.text.includes("javascript:")) {
      risks.push("JavaScript injection attempt");
    }

    if (message.text.match(/<script|<iframe|<object/i)) {
      risks.push("HTML injection attempt");
    }

    if (message.text.match(/\b(password|token|secret|key)\s*[:=]\s*\S+/i)) {
      risks.push("Potential credential exposure");
    }

    return {
      hasRisk: risks.length > 0,
      risks,
    };
  }

  /**
   * Block a user
   * @param userId - The user ID to block
   */
  blockUser(userId: string): void {
    this.blockedUsers.add(userId);
    this.logger.logSecurity("USER_BLOCKED", userId, "", { reason: "Manual block" });
  }

  /**
   * Unblock a user
   * @param userId - The user ID to unblock
   */
  unblockUser(userId: string): void {
    this.blockedUsers.delete(userId);
    this.logger.logSecurity("USER_UNBLOCKED", userId, "", { reason: "Manual unblock" });
  }

  /**
   * Check if user is blocked
   * @param userId - The user ID
   * @returns True if user is blocked
   */
  isUserBlocked(userId: string): boolean {
    return this.blockedUsers.has(userId);
  }

  /**
   * Get security statistics
   * @returns Security statistics
   */
  getSecurityStats(): {
    blockedUsers: number;
    activeRateLimits: number;
    spamDetections: number;
    suspiciousActivity: number;
  } {
    return {
      blockedUsers: this.blockedUsers.size,
      activeRateLimits: this.rateLimitStore.size,
      spamDetections: Array.from(this.spamStore.values()).reduce(
        (sum, entry) => sum + entry.warningCount,
        0
      ),
      suspiciousActivity: this.suspiciousActivity.size,
    };
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    const expiredTime = 60 * 60 * 1000; // 1 hour

    // Clean rate limit store
    for (const [key, entry] of this.rateLimitStore.entries()) {
      if (now - entry.lastRequest > expiredTime) {
        this.rateLimitStore.delete(key);
      }
    }

    // Clean spam store
    for (const [key, entry] of this.spamStore.entries()) {
      if (now - entry.lastMessageTime > expiredTime) {
        this.spamStore.delete(key);
      }
    }

    // Clean suspicious activity
    for (const [key, entry] of this.suspiciousActivity.entries()) {
      if (now - entry.lastActivity > expiredTime) {
        this.suspiciousActivity.delete(key);
      }
    }

    this.logger.debug("Security service cleanup completed", {
      metadata: {
        rateLimitEntries: this.rateLimitStore.size,
        spamEntries: this.spamStore.size,
        suspiciousEntries: this.suspiciousActivity.size,
      },
    });
  }

  /**
   * Get rate limit for action type
   * @param action - The action type
   * @returns Rate limit number
   */
  private getLimitForAction(action: string): number {
    switch (action) {
      case "message":
        return this.config.rateLimits.messagesPerMinute ?? 0;
      case "command":
        return this.config.rateLimits.commandsPerMinute ?? 0;
      case "twitter_url":
        return this.config.rateLimits.twitterUrlsPerMinute;
      case "callback":
        return this.config.rateLimits.callbacksPerMinute;
      default:
        return 10; // Default limit
    }
  }

  /**
   * Check if text contains URLs
   * @param text - The text to check
   * @returns True if contains URLs
   */
  private containsUrls(text: string): boolean {
    return /https?:\/\/\S+/.test(text);
  }

  /**
   * Check if text contains mentions
   * @param text - The text to check
   * @returns True if contains mentions
   */
  private containsMentions(text: string): boolean {
    return /@\w+/.test(text);
  }
}

// Supporting interfaces
interface RateLimitEntry {
  count: number;
  firstRequest: number;
  lastRequest: number;
  windowMs: number;
}

interface SpamEntry {
  recentMessages: Array<{ text: string; timestamp: number }>;
  lastMessageTime: number;
  identicalCount: number;
  warningCount: number;
}

interface SuspiciousActivityEntry {
  activityType: string;
  count: number;
  lastActivity: number;
}

/**
 * Telegram User Service
 * Handles user management and account linking for Telegram bot
 */

import type TelegramBot from "node-telegram-bot-api";
import { prisma } from "../../db-utils";
import type { TelegramLogger } from "../utils/telegram-logger";

export interface TelegramUserData {
  id: string;
  telegramId: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  languageCode?: string;
  userId?: string; // Linked BuddyChip user ID
  isActive: boolean;
  isBlocked: boolean;
  lastActiveAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface LinkAccountResult {
  success: boolean;
  message: string;
  telegramUser?: TelegramUserData;
}

export interface UserStats {
  totalUsers: number;
  linkedUsers: number;
  activeUsers: number;
  blockedUsers: number;
  newUsersToday: number;
}

/**
 * Service for managing Telegram users and account linking
 */
export class TelegramUserService {
  constructor(private readonly logger: TelegramLogger) {}

  /**
   * Get or create a Telegram user record
   * @param telegramUser - The Telegram user from the bot API
   * @returns The database user record
   */
  async getOrCreateTelegramUser(telegramUser: TelegramBot.User): Promise<TelegramUserData> {
    const telegramId = telegramUser.id.toString();

    try {
      this.logger.debug("Getting or creating Telegram user", {
        telegramUserId: telegramId,
        metadata: { username: telegramUser.username },
      });

      // Try to find existing user
      let dbTelegramUser = await prisma.telegramUser.findUnique({
        where: { telegramId },
      });

      if (!dbTelegramUser) {
        // Create new user
        dbTelegramUser = await prisma.telegramUser.create({
          data: {
            telegramId,
            username: telegramUser.username,
            firstName: telegramUser.first_name,
            lastName: telegramUser.last_name,
            languageCode: telegramUser.language_code,
            lastActiveAt: new Date(),
          },
        });

        this.logger.info("Created new Telegram user", {
          telegramUserId: telegramId,
          metadata: { 
            username: telegramUser.username,
            firstName: telegramUser.first_name,
          },
        });
      } else {
        // Update existing user info and last active time
        dbTelegramUser = await prisma.telegramUser.update({
          where: { id: dbTelegramUser.id },
          data: {
            username: telegramUser.username,
            firstName: telegramUser.first_name,
            lastName: telegramUser.last_name,
            languageCode: telegramUser.language_code,
            lastActiveAt: new Date(),
            isActive: true, // Reactivate if they were inactive
          },
        });

        this.logger.debug("Updated existing Telegram user", {
          telegramUserId: telegramId,
          metadata: { userId: dbTelegramUser.userId },
        });
      }

      return this.mapToTelegramUserData(dbTelegramUser);
    } catch (error) {
      this.logger.error("Error getting or creating Telegram user", {
        telegramUserId: telegramId,
        metadata: { error: error instanceof Error ? error.message : String(error) },
      });
      throw error;
    }
  }

  /**
   * Get a Telegram user by their Telegram ID
   * @param telegramId - The Telegram user ID
   * @returns The user data or null if not found
   */
  async getTelegramUser(telegramId: string): Promise<TelegramUserData | null> {
    try {
      const user = await prisma.telegramUser.findUnique({
        where: { telegramId },
      });

      return user ? this.mapToTelegramUserData(user) : null;
    } catch (error) {
      this.logger.error("Error getting Telegram user", {
        telegramUserId: telegramId,
        metadata: { error: error instanceof Error ? error.message : String(error) },
      });
      return null;
    }
  }

  /**
   * Get a Telegram user by their linked BuddyChip user ID
   * @param userId - The BuddyChip user ID
   * @returns The Telegram user data or null if not found
   */
  async getTelegramUserByUserId(userId: string): Promise<TelegramUserData | null> {
    try {
      const user = await prisma.telegramUser.findFirst({
        where: { userId },
      });

      return user ? this.mapToTelegramUserData(user) : null;
    } catch (error) {
      this.logger.error("Error getting Telegram user by user ID", {
        userId,
        metadata: { error: error instanceof Error ? error.message : String(error) },
      });
      return null;
    }
  }

  /**
   * Link a Telegram user to a BuddyChip account
   * @param telegramId - The Telegram user ID
   * @param buddyChipUserId - The BuddyChip user ID
   * @returns Link result
   */
  async linkAccount(telegramId: string, buddyChipUserId: string): Promise<LinkAccountResult> {
    try {
      this.logger.info("Attempting to link Telegram account", {
        telegramUserId: telegramId,
        userId: buddyChipUserId,
      });

      // Check if BuddyChip user exists
      const buddyChipUser = await prisma.user.findUnique({
        where: { id: buddyChipUserId },
      });

      if (!buddyChipUser) {
        return {
          success: false,
          message: "BuddyChip user not found",
        };
      }

      // Find the Telegram user
      const telegramUser = await prisma.telegramUser.findUnique({
        where: { telegramId },
      });

      if (!telegramUser) {
        return {
          success: false,
          message: "Telegram user not found. Please start a conversation with the bot first.",
        };
      }

      // Check if this Telegram user is already linked to another account
      if (telegramUser.userId && telegramUser.userId !== buddyChipUserId) {
        return {
          success: false,
          message: "This Telegram account is already linked to another BuddyChip account.",
        };
      }

      // Check if this BuddyChip user already has a linked Telegram account
      const existingLink = await prisma.telegramUser.findFirst({
        where: {
          userId: buddyChipUserId,
          id: { not: telegramUser.id },
        },
      });

      if (existingLink) {
        return {
          success: false,
          message: "Your BuddyChip account is already linked to another Telegram account.",
        };
      }

      // Link the accounts
      const updatedTelegramUser = await prisma.telegramUser.update({
        where: { id: telegramUser.id },
        data: {
          userId: buddyChipUserId,
          isActive: true,
        },
      });

      this.logger.info("Successfully linked Telegram account", {
        telegramUserId: telegramId,
        userId: buddyChipUserId,
      });

      return {
        success: true,
        message: "Accounts successfully linked",
        telegramUser: this.mapToTelegramUserData(updatedTelegramUser),
      };
    } catch (error) {
      this.logger.error("Error linking Telegram account", {
        telegramUserId: telegramId,
        userId: buddyChipUserId,
        metadata: { error: error instanceof Error ? error.message : String(error) },
      });

      return {
        success: false,
        message: "Failed to link accounts due to an internal error",
      };
    }
  }

  /**
   * Unlink a Telegram account from a BuddyChip user
   * @param buddyChipUserId - The BuddyChip user ID
   * @returns Unlink result
   */
  async unlinkAccount(buddyChipUserId: string): Promise<LinkAccountResult> {
    try {
      this.logger.info("Attempting to unlink Telegram account", {
        userId: buddyChipUserId,
      });

      const telegramUser = await prisma.telegramUser.findFirst({
        where: { userId: buddyChipUserId },
      });

      if (!telegramUser) {
        return {
          success: false,
          message: "No linked Telegram account found",
        };
      }

      // Unlink the account
      const updatedUser = await prisma.telegramUser.update({
        where: { id: telegramUser.id },
        data: { userId: null },
      });

      this.logger.info("Successfully unlinked Telegram account", {
        telegramUserId: telegramUser.telegramId,
        userId: buddyChipUserId,
      });

      return {
        success: true,
        message: "Telegram account successfully unlinked",
        telegramUser: this.mapToTelegramUserData(updatedUser),
      };
    } catch (error) {
      this.logger.error("Error unlinking Telegram account", {
        userId: buddyChipUserId,
        metadata: { error: error instanceof Error ? error.message : String(error) },
      });

      return {
        success: false,
        message: "Failed to unlink account due to an internal error",
      };
    }
  }

  /**
   * Block a Telegram user
   * @param telegramId - The Telegram user ID
   * @returns Success status
   */
  async blockUser(telegramId: string): Promise<boolean> {
    try {
      await prisma.telegramUser.update({
        where: { telegramId },
        data: { isBlocked: true, isActive: false },
      });

      this.logger.info("Blocked Telegram user", { telegramUserId: telegramId });
      return true;
    } catch (error) {
      this.logger.error("Error blocking Telegram user", {
        telegramUserId: telegramId,
        metadata: { error: error instanceof Error ? error.message : String(error) },
      });
      return false;
    }
  }

  /**
   * Unblock a Telegram user
   * @param telegramId - The Telegram user ID
   * @returns Success status
   */
  async unblockUser(telegramId: string): Promise<boolean> {
    try {
      await prisma.telegramUser.update({
        where: { telegramId },
        data: { isBlocked: false, isActive: true },
      });

      this.logger.info("Unblocked Telegram user", { telegramUserId: telegramId });
      return true;
    } catch (error) {
      this.logger.error("Error unblocking Telegram user", {
        telegramUserId: telegramId,
        metadata: { error: error instanceof Error ? error.message : String(error) },
      });
      return false;
    }
  }

  /**
   * Get user statistics
   * @returns User statistics
   */
  async getUserStats(): Promise<UserStats> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const [totalUsers, linkedUsers, activeUsers, blockedUsers, newUsersToday] = await Promise.all([
        prisma.telegramUser.count(),
        prisma.telegramUser.count({ where: { userId: { not: null } } }),
        prisma.telegramUser.count({ where: { isActive: true } }),
        prisma.telegramUser.count({ where: { isBlocked: true } }),
        prisma.telegramUser.count({ where: { createdAt: { gte: today } } }),
      ]);

      return {
        totalUsers,
        linkedUsers,
        activeUsers,
        blockedUsers,
        newUsersToday,
      };
    } catch (error) {
      this.logger.error("Error getting user stats", {
        metadata: { error: error instanceof Error ? error.message : String(error) },
      });
      return { totalUsers: 0, linkedUsers: 0, activeUsers: 0, blockedUsers: 0, newUsersToday: 0 };
    }
  }

  /**
   * Map database user to TelegramUserData
   * @param user - Database user record
   * @returns Mapped user data
   */
  private mapToTelegramUserData(user: any): TelegramUserData {
    return {
      id: user.id,
      telegramId: user.telegramId,
      username: user.username,
      firstName: user.firstName,
      lastName: user.lastName,
      languageCode: user.languageCode,
      userId: user.userId,
      isActive: user.isActive,
      isBlocked: user.isBlocked,
      lastActiveAt: user.lastActiveAt,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };
  }
}

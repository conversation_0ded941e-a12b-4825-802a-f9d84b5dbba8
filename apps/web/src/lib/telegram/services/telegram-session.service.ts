/**
 * Telegram Session Service
 * Handles session management for Telegram bot interactions
 */

import { prisma } from "../../db-utils";
import type { TelegramLogger } from "../utils/telegram-logger";

export interface SessionData {
  responseText?: string;
  prompt?: string;
  twitterUrl?: string;
  timestamp: number;
  type?: "create_command" | "twitter_url" | "general";
  messageHistory?: Array<{
    role: "user" | "assistant";
    content: string;
    timestamp: Date;
  }>;
  lastInteraction?: Date;
  telegramChatId?: string;
  [key: string]: any; // Allow additional custom data
}

export interface SessionOptions {
  expirationHours?: number;
  maxHistoryLength?: number;
}

/**
 * Service for managing Telegram user sessions and callback data
 */
export class TelegramSessionService {
  private readonly defaultExpirationHours = 24;
  private readonly defaultMaxHistoryLength = 20;

  constructor(private readonly logger: TelegramLogger) {}

  /**
   * Store session data for a Telegram user
   * @param telegramUserId - The Telegram user ID
   * @param data - The session data to store
   * @param options - Optional session configuration
   */
  async storeSessionData(
    telegramUserId: string,
    data: SessionData,
    options: SessionOptions = {}
  ): Promise<boolean> {
    try {
      const expirationHours = options.expirationHours || this.defaultExpirationHours;
      const expiresAt = new Date(Date.now() + expirationHours * 60 * 60 * 1000);

      this.logger.debug("Storing session data", {
        telegramUserId,
        metadata: {
          dataType: data.type,
          hasResponseText: !!data.responseText,
          hasPrompt: !!data.prompt,
          hasTwitterUrl: !!data.twitterUrl,
          expiresAt: expiresAt.toISOString(),
        },
      });

      // Find existing active session
      const existingSession = await prisma.telegramSession.findFirst({
        where: {
          telegramUserId: telegramUserId,
          isActive: true,
        },
      });

      if (existingSession) {
        // Update existing session
        await prisma.telegramSession.update({
          where: { id: existingSession.id },
          data: {
            context: data,
            updatedAt: new Date(),
            expiresAt,
          },
        });

        this.logger.debug("Updated existing session", {
          telegramUserId,
          metadata: { sessionId: existingSession.id },
        });
      } else {
        // Create new session
        const newSession = await prisma.telegramSession.create({
          data: {
            telegramUserId: telegramUserId,
            context: data,
            isActive: true,
            expiresAt,
          },
        });

        this.logger.debug("Created new session", {
          telegramUserId,
          metadata: { sessionId: newSession.id },
        });
      }

      return true;
    } catch (error) {
      this.logger.error("Error storing session data", {
        telegramUserId,
        metadata: { error: error instanceof Error ? error.message : String(error) },
      });
      return false;
    }
  }

  /**
   * Get session data for a Telegram user
   * @param telegramUserId - The Telegram user ID
   * @returns The session data or null if not found/expired
   */
  async getSessionData(telegramUserId: string): Promise<SessionData | null> {
    try {
      const session = await prisma.telegramSession.findFirst({
        where: {
          telegramUserId: telegramUserId,
          isActive: true,
          expiresAt: { gt: new Date() },
        },
        orderBy: { updatedAt: "desc" },
      });

      if (!session?.context) {
        this.logger.debug("No active session found", { telegramUserId });
        return null;
      }

      this.logger.debug("Retrieved session data", {
        telegramUserId,
        metadata: {
          sessionId: session.id,
          hasContext: !!session.context,
          expiresAt: session.expiresAt.toISOString(),
        },
      });

      return session.context as SessionData;
    } catch (error) {
      this.logger.error("Error getting session data", {
        telegramUserId,
        metadata: { error: error instanceof Error ? error.message : String(error) },
      });
      return null;
    }
  }

  /**
   * Update session data for a Telegram user
   * @param telegramUserId - The Telegram user ID
   * @param updates - Partial session data to update
   * @returns Success status
   */
  async updateSessionData(
    telegramUserId: string,
    updates: Partial<SessionData>
  ): Promise<boolean> {
    try {
      const existingData = await this.getSessionData(telegramUserId);
      if (!existingData) {
        this.logger.warn("Attempted to update non-existent session", { telegramUserId });
        return false;
      }

      const updatedData = { ...existingData, ...updates };
      return await this.storeSessionData(telegramUserId, updatedData);
    } catch (error) {
      this.logger.error("Error updating session data", {
        telegramUserId,
        metadata: { error: error instanceof Error ? error.message : String(error) },
      });
      return false;
    }
  }

  /**
   * Clear session data for a Telegram user
   * @param telegramUserId - The Telegram user ID
   * @returns Success status
   */
  async clearSessionData(telegramUserId: string): Promise<boolean> {
    try {
      await prisma.telegramSession.updateMany({
        where: {
          telegramUserId: telegramUserId,
          isActive: true,
        },
        data: {
          isActive: false,
          updatedAt: new Date(),
        },
      });

      this.logger.debug("Cleared session data", { telegramUserId });
      return true;
    } catch (error) {
      this.logger.error("Error clearing session data", {
        telegramUserId,
        metadata: { error: error instanceof Error ? error.message : String(error) },
      });
      return false;
    }
  }

  /**
   * Add message to conversation history
   * @param telegramUserId - The Telegram user ID
   * @param role - The message role (user or assistant)
   * @param content - The message content
   * @param options - Optional configuration
   * @returns Success status
   */
  async addMessageToHistory(
    telegramUserId: string,
    role: "user" | "assistant",
    content: string,
    options: SessionOptions = {}
  ): Promise<boolean> {
    try {
      const sessionData = await this.getSessionData(telegramUserId) || {
        timestamp: Date.now(),
        messageHistory: [],
      };

      const messageHistory = sessionData.messageHistory || [];
      messageHistory.push({
        role,
        content,
        timestamp: new Date(),
      });

      // Trim history to prevent it from growing too large
      const maxLength = options.maxHistoryLength || this.defaultMaxHistoryLength;
      const trimmedHistory = messageHistory.slice(-maxLength);

      const updatedData: SessionData = {
        ...sessionData,
        messageHistory: trimmedHistory,
        lastInteraction: new Date(),
      };

      return await this.storeSessionData(telegramUserId, updatedData, options);
    } catch (error) {
      this.logger.error("Error adding message to history", {
        telegramUserId,
        metadata: { 
          role, 
          contentLength: content.length,
          error: error instanceof Error ? error.message : String(error) 
        },
      });
      return false;
    }
  }

  /**
   * Clean up expired sessions
   * @returns Number of sessions cleaned up
   */
  async cleanupExpiredSessions(): Promise<number> {
    try {
      const result = await prisma.telegramSession.updateMany({
        where: {
          isActive: true,
          expiresAt: { lt: new Date() },
        },
        data: {
          isActive: false,
          updatedAt: new Date(),
        },
      });

      this.logger.info(`Cleaned up ${result.count} expired sessions`);
      return result.count;
    } catch (error) {
      this.logger.error("Error cleaning up expired sessions", {
        metadata: { error: error instanceof Error ? error.message : String(error) },
      });
      return 0;
    }
  }

  /**
   * Get session statistics
   * @returns Session statistics
   */
  async getSessionStats(): Promise<{
    totalActive: number;
    totalExpired: number;
    averageSessionAge: number;
  }> {
    try {
      const [activeCount, expiredCount, sessions] = await Promise.all([
        prisma.telegramSession.count({
          where: {
            isActive: true,
            expiresAt: { gt: new Date() },
          },
        }),
        prisma.telegramSession.count({
          where: {
            isActive: true,
            expiresAt: { lt: new Date() },
          },
        }),
        prisma.telegramSession.findMany({
          where: { isActive: true },
          select: { createdAt: true },
        }),
      ]);

      const now = Date.now();
      const averageAge = sessions.length > 0
        ? sessions.reduce((sum, session) => sum + (now - session.createdAt.getTime()), 0) / sessions.length
        : 0;

      return {
        totalActive: activeCount,
        totalExpired: expiredCount,
        averageSessionAge: Math.round(averageAge / (1000 * 60 * 60)), // Convert to hours
      };
    } catch (error) {
      this.logger.error("Error getting session stats", {
        metadata: { error: error instanceof Error ? error.message : String(error) },
      });
      return { totalActive: 0, totalExpired: 0, averageSessionAge: 0 };
    }
  }
}

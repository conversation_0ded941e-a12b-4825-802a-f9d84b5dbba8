# Telegram Bot Migration Guide

This guide helps you migrate from the old monolithic `telegram-bot.ts` to the new modular architecture.

## 🎯 Migration Overview

The refactoring maintains **100% backward compatibility** while introducing a new modular architecture. Your existing code will continue to work without changes, but you can gradually adopt the new patterns.

## 📋 Pre-Migration Checklist

- [ ] Backup your current `telegram-bot.ts` file
- [ ] Ensure all tests are passing
- [ ] Document any custom modifications
- [ ] Review current bot configuration
- [ ] Plan testing strategy for new architecture

## 🔄 Migration Steps

### Step 1: Update Imports (Optional)

The main class remains the same, but you can now access new features:

```typescript
// Before (still works)
import { TelegramBotService } from './telegram-bot';

// After (same import, new features available)
import { TelegramBotService } from './telegram-bot';
```

### Step 2: Initialize with New Architecture

Your existing initialization code works unchanged:

```typescript
// Before & After - No changes needed
const bot = new TelegramBotService({
  token: process.env.TELEGRAM_BOT_TOKEN!,
  enablePolling: true,
  webhookUrl: process.env.WEBHOOK_URL,
});

await bot.initialize();
```

### Step 3: Process Updates (No Changes)

Update processing remains identical:

```typescript
// Before & After - No changes needed
app.post('/webhook', async (req, res) => {
  await bot.processUpdate(req.body);
  res.sendStatus(200);
});
```

### Step 4: Access New Features (Optional)

You can now access additional capabilities:

```typescript
// New: Get detailed statistics
const stats = await bot.getStats();
console.log('Bot architecture:', stats.architecture); // "modular"
console.log('Command handlers:', stats.router.commandHandlers);

// New: Check if bot is ready
if (bot.isReady()) {
  console.log('Bot is ready to process updates');
}

// New: Graceful shutdown
await bot.shutdown();
```

## 🆕 New Capabilities

### 1. Custom Command Handlers

You can now easily add custom commands:

```typescript
import { BaseCommandHandler } from './telegram/commands/base/command-handler.interface';
import type { TelegramHandlerContext } from './telegram/core/telegram-handler-context';

class WeatherCommandHandler extends BaseCommandHandler {
  constructor() {
    super("weather", "Get weather information");
  }

  async handle(message: TelegramBot.Message, context: TelegramHandlerContext): Promise<void> {
    const location = message.text?.replace('/weather', '').trim();
    
    if (!location) {
      await context.bot.sendMessage(
        message.chat.id,
        "Please provide a location: /weather London"
      );
      return;
    }

    // Your weather logic here
    const weather = await getWeather(location);
    await context.bot.sendMessage(
      message.chat.id,
      `Weather in ${location}: ${weather.description}, ${weather.temperature}°C`
    );
  }
}

// Add to bot (after initialization)
const router = bot.getRouter();
router.addCommandHandler(new WeatherCommandHandler());
```

### 2. Custom Message Processors

Add custom message processing logic:

```typescript
import { BaseMessageProcessor } from './telegram/processors/base/message-processor.interface';

class ImageProcessor extends BaseMessageProcessor {
  constructor() {
    super(80, "Process image messages"); // High priority
  }

  canHandle(message: TelegramBot.Message): boolean {
    return !!message.photo;
  }

  async process(message: TelegramBot.Message, context: TelegramHandlerContext): Promise<void> {
    // Your image processing logic
    await context.bot.sendMessage(
      message.chat.id,
      "I received an image! Processing..."
    );
  }
}

// Add to message router
const messageRouter = bot.getMessageRouter();
messageRouter.addProcessor(new ImageProcessor());
```

### 3. Custom Callback Handlers

Handle custom inline keyboard callbacks:

```typescript
import { BaseCallbackHandler } from './telegram/callbacks/base/callback-handler.interface';

class VoteCallbackHandler extends BaseCallbackHandler {
  constructor() {
    super("vote", "Handle voting callbacks");
  }

  async handle(query: TelegramBot.CallbackQuery, context: TelegramHandlerContext): Promise<void> {
    const voteType = this.extractPayload(query.data!); // "up" or "down"
    
    // Your voting logic
    await this.recordVote(query.from.id, voteType);
    
    await context.bot.answerCallbackQuery(query.id, {
      text: `Vote ${voteType} recorded!`,
    });
  }

  private async recordVote(userId: number, voteType: string): Promise<void> {
    // Implementation
  }
}

// Add to callback router
const callbackRouter = bot.getCallbackRouter();
callbackRouter.addHandler(new VoteCallbackHandler());
```

## 🔧 Configuration Changes

### Environment Variables

No changes to environment variables are required. The new architecture uses the same configuration:

```bash
# Same as before
TELEGRAM_BOT_TOKEN=your_bot_token
WEBHOOK_URL=https://your-domain.com/webhook
WEBHOOK_SECRET=your_webhook_secret
```

### Bot Configuration

The configuration object remains the same:

```typescript
interface TelegramBotConfig {
  token: string;
  webhookUrl?: string;
  enablePolling?: boolean;
}
```

## 🧪 Testing Migration

### 1. Verify Existing Functionality

Run your existing tests to ensure nothing is broken:

```bash
# Run your existing test suite
bun test

# Test specific bot functionality
bun test telegram
```

### 2. Test New Architecture

Use the new test suite to verify the modular architecture:

```bash
# Run new architecture tests
bun test src/lib/telegram/__tests__/

# Run performance tests
bun test src/lib/telegram/__tests__/performance/

# Run integration tests
bun test src/lib/telegram/__tests__/integration/
```

### 3. Benchmark Performance

Compare old vs new performance:

```bash
bun run src/lib/telegram/__tests__/performance/benchmark.ts
```

## 📊 Monitoring Migration

### 1. Check Bot Statistics

Monitor the new statistics endpoint:

```typescript
const stats = await bot.getStats();
console.log('Architecture:', stats.architecture); // Should be "modular"
console.log('Bot info:', stats.botInfo);
console.log('Router stats:', stats.router);
```

### 2. Monitor Logs

The new architecture provides enhanced logging:

```typescript
// Logs are automatically enhanced with:
// - Command execution tracking
// - Message processing metrics
// - Callback handling statistics
// - Error context and debugging info
```

### 3. Performance Monitoring

Track key metrics:

```typescript
// Monitor processing times
const startTime = performance.now();
await bot.processUpdate(update);
const processingTime = performance.now() - startTime;

// Should be similar or better than old architecture
console.log(`Processing time: ${processingTime}ms`);
```

## 🚨 Troubleshooting

### Common Migration Issues

#### 1. Bot Not Responding

**Symptoms:** Bot doesn't respond to messages after migration

**Solution:**
```typescript
// Check if bot is properly initialized
if (!bot.isReady()) {
  console.error('Bot not ready, reinitializing...');
  await bot.initialize();
}

// Verify configuration
const stats = await bot.getStats();
console.log('Bot status:', stats);
```

#### 2. Performance Regression

**Symptoms:** Slower response times after migration

**Solution:**
```bash
# Run performance benchmark
bun run src/lib/telegram/__tests__/performance/benchmark.ts

# Check for memory leaks
node --expose-gc your-app.js
```

#### 3. Custom Logic Not Working

**Symptoms:** Custom modifications from old bot not working

**Solution:**
```typescript
// Migrate custom logic to new handlers
// Example: Custom command logic
class CustomHandler extends BaseCommandHandler {
  // Move your custom logic here
}

// Add to router
router.addCommandHandler(new CustomHandler());
```

### Debug Mode

Enable detailed debugging:

```typescript
// Set debug environment
process.env.TELEGRAM_DEBUG = "true";

// Or enable programmatically
const bot = new TelegramBotService({
  token: process.env.TELEGRAM_BOT_TOKEN!,
  enablePolling: true,
  debug: true, // Enable debug mode
});
```

### Rollback Plan

If you need to rollback:

1. **Keep Old File:** The old `telegram-bot.ts` is preserved as `telegram-bot.legacy.ts`
2. **Switch Import:** Change import to use legacy version
3. **Restore Configuration:** Use old configuration format

```typescript
// Rollback import
import { TelegramBotService } from './telegram-bot.legacy';
```

## 📈 Post-Migration Benefits

After migration, you'll have access to:

### 1. Better Maintainability
- Modular code structure
- Single responsibility principle
- Easy to add new features

### 2. Enhanced Testing
- Unit tests for each component
- Integration tests
- Performance benchmarks

### 3. Improved Monitoring
- Detailed logging
- Performance metrics
- Error tracking

### 4. Extensibility
- Easy to add custom handlers
- Plugin-like architecture
- Type-safe interfaces

## 🎉 Migration Complete

Once migration is complete:

1. **Update Documentation:** Document any custom handlers
2. **Train Team:** Share new architecture patterns
3. **Monitor Performance:** Keep an eye on metrics
4. **Plan Enhancements:** Use new capabilities for future features

## 📞 Support

If you encounter issues during migration:

1. **Check Logs:** Review detailed error messages
2. **Run Tests:** Use the comprehensive test suite
3. **Performance Check:** Run benchmarks
4. **Documentation:** Refer to README.md for detailed usage

The migration maintains full backward compatibility, so you can migrate gradually and take advantage of new features as needed.

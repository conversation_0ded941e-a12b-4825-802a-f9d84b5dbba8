# Telegram Bot Architecture

This directory contains the refactored Telegram bot implementation using a modular, maintainable architecture. The bot has been broken down from a monolithic 1665+ line file into focused, testable modules with proper separation of concerns.

## 🏗️ Architecture Overview

The new architecture follows these principles:
- **Separation of Concerns**: Each module has a single responsibility
- **Dependency Injection**: Services are injected through context
- **Testability**: All components are easily unit testable
- **Extensibility**: New handlers and processors can be easily added
- **Type Safety**: Full TypeScript support with proper interfaces

## 📁 Directory Structure

```
telegram/
├── core/                           # Core bot management
│   ├── telegram-bot-core.ts        # Bot instance and webhook management
│   ├── telegram-bot-router.ts      # Main routing logic
│   └── telegram-handler-context.ts # Shared context for all handlers
├── commands/                       # Command handlers
│   ├── base/
│   │   └── command-handler.interface.ts
│   ├── start-command.handler.ts
│   ├── help-command.handler.ts
│   ├── settings-command.handler.ts
│   ├── status-command.handler.ts
│   └── create-command.handler.ts
├── processors/                     # Message processors
│   ├── base/
│   │   └── message-processor.interface.ts
│   ├── twitter-url.processor.ts
│   ├── general-message.processor.ts
│   └── message-router.ts
├── callbacks/                      # Callback query handlers
│   ├── base/
│   │   └── callback-handler.interface.ts
│   ├── copy-callback.handler.ts
│   ├── regenerate-callback.handler.ts
│   ├── enhance-callback.handler.ts
│   └── callback-router.ts
├── services/                       # Business logic services
│   ├── telegram-session.service.ts
│   ├── telegram-user.service.ts
│   └── telegram-security.service.ts
├── utils/                          # Utility functions
│   ├── telegram-logger.ts
│   ├── telegram-validator.ts
│   └── telegram-formatter.ts
└── __tests__/                      # Comprehensive test suite
    ├── commands/
    ├── processors/
    ├── callbacks/
    ├── core/
    ├── integration/
    └── performance/
```

## 🚀 Quick Start

### Basic Usage

```typescript
import { TelegramBotService } from './telegram-bot';

// Initialize bot
const bot = new TelegramBotService({
  token: process.env.TELEGRAM_BOT_TOKEN!,
  enablePolling: true, // For development
  // webhookUrl: 'https://your-domain.com/webhook', // For production
});

// Initialize and start
await bot.initialize();

// Process webhook updates (production)
app.post('/webhook', (req, res) => {
  bot.processUpdate(req.body);
  res.sendStatus(200);
});
```

### Adding Custom Command Handler

```typescript
import { BaseCommandHandler } from './commands/base/command-handler.interface';

class CustomCommandHandler extends BaseCommandHandler {
  constructor() {
    super("custom", "Custom command description");
  }

  async handle(message: TelegramBot.Message, context: TelegramHandlerContext): Promise<void> {
    await context.bot.sendMessage(
      message.chat.id,
      "Custom command response!"
    );
  }
}

// Add to router
const router = bot.getRouter();
router.addCommandHandler(new CustomCommandHandler());
```

## 🔧 Core Components

### TelegramBotCore

Manages the bot instance, webhook setup, and basic update handling.

**Key Features:**
- Polling and webhook support
- Graceful initialization and shutdown
- Update distribution to handlers
- Error handling and logging

### TelegramBotRouter

Main routing logic that coordinates all handlers and processors.

**Responsibilities:**
- Route commands to appropriate handlers
- Route messages to processors
- Route callback queries to handlers
- Handle unknown update types

### TelegramHandlerContext

Shared context object passed to all handlers containing:
- Bot instance
- Services (user, session, security)
- Logger
- Configuration

## 📝 Handler Types

### Command Handlers

Handle `/command` messages with specific business logic.

**Interface:**
```typescript
interface TelegramCommandHandler {
  getCommand(): string;
  getDescription(): string;
  handle(message: TelegramBot.Message, context: TelegramHandlerContext): Promise<void>;
}
```

**Available Commands:**
- `/start` - Welcome and account linking
- `/help` - Show available commands
- `/settings` - User preferences
- `/status` - Account status and usage
- `/create` - Generate AI content

### Message Processors

Process non-command messages based on content type.

**Interface:**
```typescript
interface TelegramMessageProcessor {
  getPriority(): number;
  getDescription(): string;
  canHandle(message: TelegramBot.Message): boolean;
  process(message: TelegramBot.Message, context: TelegramHandlerContext): Promise<void>;
}
```

**Available Processors:**
- **TwitterUrlProcessor** (Priority: 90) - Handles Twitter/X URLs
- **GeneralMessageProcessor** (Priority: 10) - Handles general conversation

### Callback Handlers

Handle inline keyboard button clicks.

**Interface:**
```typescript
interface TelegramCallbackHandler {
  getCallbackPrefix(): string;
  getDescription(): string;
  canHandle(data: string): boolean;
  handle(query: TelegramBot.CallbackQuery, context: TelegramHandlerContext): Promise<void>;
}
```

**Available Handlers:**
- **CopyCallbackHandler** - Handle copy button clicks
- **RegenerateCallbackHandler** - Handle regenerate button clicks
- **EnhanceCallbackHandler** - Handle enhance button clicks (o3 model)

## 🛠️ Services

### TelegramSessionService

Manages user sessions and conversation history.

**Methods:**
- `storeSessionData(userId, data)` - Store session data
- `getSessionData(userId)` - Retrieve session data
- `addMessageToHistory(userId, role, content)` - Add to conversation history

### TelegramUserService

Manages user accounts and linking.

**Methods:**
- `getOrCreateTelegramUser(telegramUser)` - Get or create user record
- `getTelegramUser(telegramUserId)` - Get existing user
- `getUserStats()` - Get user statistics

### TelegramSecurityService

Handles security, rate limiting, and spam detection.

**Methods:**
- `checkMessageSecurity(message, userId)` - Security validation
- `checkRateLimit(userId, action)` - Rate limit checking

## 🧪 Testing

The architecture includes comprehensive tests:

### Unit Tests
- Command handlers
- Message processors
- Callback handlers
- Services
- Utilities

### Integration Tests
- Full bot initialization
- Update processing flow
- Service interactions

### Performance Tests
- Processing speed benchmarks
- Memory usage monitoring
- High-volume testing

**Run Tests:**
```bash
# All tests
bun test

# Specific test suite
bun test commands/
bun test processors/
bun test callbacks/

# Performance tests
bun test performance/

# Run benchmark
bun run telegram/__tests__/performance/benchmark.ts
```

## 📊 Performance

The new architecture maintains excellent performance:

- **Command Processing**: <50ms average
- **Message Processing**: <40ms average
- **Callback Processing**: <30ms average
- **High Volume**: <10ms per update (100+ concurrent)
- **Memory Efficient**: <10MB increase for 200 updates

## 🔄 Migration Guide

### From Old Architecture

The main `TelegramBotService` class maintains backward compatibility:

```typescript
// Old usage still works
const bot = new TelegramBotService(config);
await bot.initialize();
await bot.processUpdate(update);

// New features available
const stats = await bot.getStats();
const router = bot.getRouter();
```

### Breaking Changes

None! The refactor maintains full backward compatibility while adding new capabilities.

## 🚀 Deployment

### Development (Polling)
```typescript
const bot = new TelegramBotService({
  token: process.env.TELEGRAM_BOT_TOKEN!,
  enablePolling: true,
});
```

### Production (Webhooks)
```typescript
const bot = new TelegramBotService({
  token: process.env.TELEGRAM_BOT_TOKEN!,
  webhookUrl: process.env.WEBHOOK_URL!,
  webhookSecret: process.env.WEBHOOK_SECRET,
});
```

## 🔍 Monitoring

The architecture includes comprehensive logging:

```typescript
// Access logger through context
context.logger.info("Operation completed", {
  metadata: { userId, action, duration }
});

// Built-in logging for:
// - Command execution
// - Message processing
// - Callback handling
// - Errors and warnings
```

## 🤝 Contributing

### Adding New Command Handler

1. Create handler class extending `BaseCommandHandler`
2. Implement required methods
3. Add to router in `TelegramBotRouter`
4. Write unit tests
5. Update documentation

### Adding New Message Processor

1. Create processor class implementing `TelegramMessageProcessor`
2. Set appropriate priority
3. Implement `canHandle` and `process` methods
4. Add to `TelegramMessageRouter`
5. Write unit tests

### Adding New Callback Handler

1. Create handler class extending `BaseCallbackHandler`
2. Define callback prefix
3. Implement handling logic
4. Add to `TelegramCallbackRouter`
5. Write unit tests

## 📚 Additional Resources

- [Telegram Bot API Documentation](https://core.telegram.org/bots/api)
- [Node Telegram Bot API](https://github.com/yagop/node-telegram-bot-api)
- [TypeScript Best Practices](https://typescript-eslint.io/rules/)
- [Testing with Vitest](https://vitest.dev/)

## 🐛 Troubleshooting

### Common Issues

1. **Bot not responding**: Check token and webhook configuration
2. **Rate limits**: Verify rate limiting service configuration
3. **Memory leaks**: Monitor session cleanup
4. **Performance issues**: Run performance tests and benchmarks

### Debug Mode

Enable debug logging:
```typescript
process.env.TELEGRAM_DEBUG = "true";
```

### Health Checks

```typescript
// Check bot status
const isReady = bot.isReady();
const stats = await bot.getStats();
```

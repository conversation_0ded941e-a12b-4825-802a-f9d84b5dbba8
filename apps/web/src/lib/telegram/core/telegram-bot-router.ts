/**
 * Telegram Bot Router
 * Main router that coordinates all handlers and processors
 */

import type TelegramBot from "node-telegram-bot-api";
import type { TelegramHandlerContext } from "./telegram-handler-context";
import type { TelegramCommandHandler } from "../commands/base/command-handler.interface";
import { TelegramMessageRouter } from "../processors/message-router";
import { TelegramCallbackRouter } from "../callbacks/callback-router";

// Import command handlers
import { StartCommandHandler } from "../commands/start-command.handler";
import { HelpCommandHandler } from "../commands/help-command.handler";
import { SettingsCommandHandler } from "../commands/settings-command.handler";
import { StatusCommandHandler } from "../commands/status-command.handler";
import { CreateCommandHandler } from "../commands/create-command.handler";

/**
 * Main router that coordinates all Telegram bot functionality
 * Routes updates to appropriate handlers and processors
 */
export class TelegramBotRouter {
  private commandHandlers: Map<string, TelegramCommandHandler> = new Map();
  private messageRouter: TelegramMessageRouter;
  private callbackRouter: TelegramCallbackRouter;

  constructor(private context: TelegramHandlerContext) {
    // Initialize command handlers
    this.initializeCommandHandlers();

    // Initialize routers
    this.messageRouter = new TelegramMessageRouter(context);
    this.callbackRouter = new TelegramCallbackRouter(context);

    this.context.logger.info("TelegramBotRouter initialized", {
      metadata: {
        commandHandlerCount: this.commandHandlers.size,
        commands: Array.from(this.commandHandlers.keys()),
        messageProcessors: this.messageRouter.getStats().processorCount,
        callbackHandlers: this.callbackRouter.getStats().handlerCount,
      },
    });
  }

  /**
   * Initialize all command handlers
   */
  private initializeCommandHandlers(): void {
    const handlers = [
      new StartCommandHandler(),
      new HelpCommandHandler(),
      new SettingsCommandHandler(),
      new StatusCommandHandler(),
      new CreateCommandHandler(),
    ];

    for (const handler of handlers) {
      this.commandHandlers.set(handler.getCommand(), handler);
    }

    this.context.logger.debug("Command handlers initialized", {
      metadata: {
        commands: Array.from(this.commandHandlers.keys()),
      },
    });
  }

  /**
   * Route an update to the appropriate handler
   * @param update - The Telegram update to route
   */
  async routeUpdate(update: any): Promise<void> {
    try {
      this.context.logger.debug("Routing update", {
        metadata: {
          updateId: update.update_id,
          hasMessage: !!update.message,
          hasCallbackQuery: !!update.callback_query,
          hasInlineQuery: !!update.inline_query,
          hasEditedMessage: !!update.edited_message,
        },
      });

      // Handle messages
      if (update.message) {
        await this.handleMessage(update.message);
        return;
      }

      // Handle callback queries
      if (update.callback_query) {
        await this.handleCallbackQuery(update.callback_query);
        return;
      }

      // Handle inline queries (future feature)
      if (update.inline_query) {
        await this.handleInlineQuery(update.inline_query);
        return;
      }

      // Handle edited messages (optional)
      if (update.edited_message) {
        this.context.logger.debug("Edited message received (ignored)", {
          metadata: { messageId: update.edited_message.message_id },
        });
        return;
      }

      // Unknown update type
      this.context.logger.warn("Unknown update type received", {
        metadata: {
          updateId: update.update_id,
          updateKeys: Object.keys(update),
        },
      });
    } catch (error) {
      this.context.logger.error("Error routing update", {
        metadata: {
          updateId: update.update_id,
          error: error instanceof Error ? error.message : String(error),
        },
      });
    }
  }

  /**
   * Handle incoming messages
   * @param message - The Telegram message
   */
  private async handleMessage(message: TelegramBot.Message): Promise<void> {
    const chatId = message.chat.id;
    const telegramUser = message.from;

    if (!telegramUser) {
      this.context.logger.warn("Message received without sender information", {
        chatId: chatId.toString(),
        metadata: { messageId: message.message_id },
      });
      return;
    }

    this.context.logger.debug("Processing message", {
      telegramUserId: telegramUser.id.toString(),
      chatId: chatId.toString(),
      metadata: {
        messageId: message.message_id,
        hasText: !!message.text,
        textLength: message.text?.length || 0,
        isCommand: message.text?.startsWith("/") || false,
      },
    });

    // Handle commands
    if (message.text?.startsWith("/")) {
      await this.handleCommand(message);
      return;
    }

    // Route to message processors
    const handled = await this.messageRouter.routeMessage(message, this.context);
    
    if (!handled) {
      this.context.logger.warn("Message not handled by any processor", {
        telegramUserId: telegramUser.id.toString(),
        chatId: chatId.toString(),
        metadata: {
          messageId: message.message_id,
          hasText: !!message.text,
          textPreview: message.text?.substring(0, 50) || "N/A",
        },
      });
    }
  }

  /**
   * Handle command messages
   * @param message - The command message
   */
  private async handleCommand(message: TelegramBot.Message): Promise<void> {
    const chatId = message.chat.id;
    const telegramUser = message.from!;
    const commandText = message.text!;

    // Extract command name
    const commandMatch = commandText.match(/^\/([a-zA-Z0-9_]+)/);
    if (!commandMatch) {
      this.context.logger.warn("Invalid command format", {
        telegramUserId: telegramUser.id.toString(),
        chatId: chatId.toString(),
        metadata: { commandText },
      });
      return;
    }

    const commandName = commandMatch[1];
    const handler = this.commandHandlers.get(commandName);

    if (!handler) {
      this.context.logger.warn("Unknown command", {
        telegramUserId: telegramUser.id.toString(),
        chatId: chatId.toString(),
        metadata: { 
          commandName,
          availableCommands: Array.from(this.commandHandlers.keys()),
        },
      });

      await this.context.bot.sendMessage(
        chatId,
        `❓ Unknown command: /${commandName}\n\nUse /help to see available commands.`
      );
      return;
    }

    this.context.logger.debug("Routing to command handler", {
      telegramUserId: telegramUser.id.toString(),
      chatId: chatId.toString(),
      metadata: {
        commandName,
        handlerDescription: handler.getDescription(),
      },
    });

    try {
      await handler.handle(message, this.context);
    } catch (error) {
      this.context.logger.error("Command handler error", {
        telegramUserId: telegramUser.id.toString(),
        chatId: chatId.toString(),
        metadata: {
          commandName,
          error: error instanceof Error ? error.message : String(error),
        },
      });

      await this.context.bot.sendMessage(
        chatId,
        "❌ Sorry, something went wrong processing your command. Please try again."
      );
    }
  }

  /**
   * Handle callback queries
   * @param query - The callback query
   */
  private async handleCallbackQuery(query: TelegramBot.CallbackQuery): Promise<void> {
    const chatId = query.message?.chat.id;
    const callbackData = query.data;

    this.context.logger.debug("Processing callback query", {
      telegramUserId: query.from.id.toString(),
      chatId: chatId?.toString() || "unknown",
      metadata: {
        queryId: query.id,
        callbackData,
      },
    });

    try {
      const handled = await this.callbackRouter.routeCallback(query, this.context);
      
      if (!handled) {
        this.context.logger.warn("Callback query not handled", {
          telegramUserId: query.from.id.toString(),
          chatId: chatId?.toString() || "unknown",
          metadata: {
            queryId: query.id,
            callbackData,
          },
        });
      }
    } catch (error) {
      this.context.logger.error("Callback query routing error", {
        telegramUserId: query.from.id.toString(),
        chatId: chatId?.toString() || "unknown",
        metadata: {
          queryId: query.id,
          callbackData,
          error: error instanceof Error ? error.message : String(error),
        },
      });

      // Answer callback query to remove loading state
      await this.context.bot.answerCallbackQuery(query.id, {
        text: "❌ Something went wrong. Please try again.",
        show_alert: true,
      });
    }
  }

  /**
   * Handle inline queries (future feature)
   * @param query - The inline query
   */
  private async handleInlineQuery(query: TelegramBot.InlineQuery): Promise<void> {
    this.context.logger.debug("Inline query received (not implemented)", {
      telegramUserId: query.from.id.toString(),
      metadata: {
        queryId: query.id,
        queryText: query.query,
      },
    });

    // Answer with empty results for now
    await this.context.bot.answerInlineQuery(query.id, [], {
      cache_time: 300,
      is_personal: true,
    });
  }

  /**
   * Get router statistics
   */
  getStats(): {
    commandHandlers: number;
    messageProcessors: number;
    callbackHandlers: number;
    commands: string[];
  } {
    return {
      commandHandlers: this.commandHandlers.size,
      messageProcessors: this.messageRouter.getStats().processorCount,
      callbackHandlers: this.callbackRouter.getStats().handlerCount,
      commands: Array.from(this.commandHandlers.keys()),
    };
  }

  /**
   * Add a custom command handler
   * @param handler - The command handler to add
   */
  addCommandHandler(handler: TelegramCommandHandler): void {
    this.commandHandlers.set(handler.getCommand(), handler);
    this.context.logger.info("Custom command handler added", {
      metadata: {
        command: handler.getCommand(),
        description: handler.getDescription(),
      },
    });
  }

  /**
   * Remove a command handler
   * @param command - The command to remove
   * @returns True if handler was removed
   */
  removeCommandHandler(command: string): boolean {
    const removed = this.commandHandlers.delete(command);
    if (removed) {
      this.context.logger.info("Command handler removed", {
        metadata: { command },
      });
    }
    return removed;
  }
}

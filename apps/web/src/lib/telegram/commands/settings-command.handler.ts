/**
 * Settings Command Handler
 * Handles the /settings command for Telegram bot
 */

import type TelegramBot from "node-telegram-bot-api";
import { BaseCommandHandler } from "./base/command-handler.interface";
import type { TelegramHandlerContext } from "../core/telegram-handler-context";

/**
 * <PERSON><PERSON> for the /settings command
 * Provides account settings and linking management
 */
export class SettingsCommandHandler extends BaseCommandHandler {
  constructor() {
    super("settings", "Manage account settings and linking");
  }

  async handle(message: TelegramBot.Message, context: TelegramHandlerContext): Promise<void> {
    const chatId = message.chat.id;
    const telegramUser = message.from;

    if (!telegramUser) {
      await context.bot.sendMessage(
        chatId,
        "❌ Unable to identify user. Please try again."
      );
      return;
    }

    context.logger.logCommand("settings", telegramUser.id.toString(), chatId.toString(), true);

    try {
      const dbTelegramUser = await context.userService.getOrCreateTelegramUser(telegramUser);

      const settingsMessage = `
⚙️ *Account Settings*

*Account Status:*
${
  dbTelegramUser.userId
    ? "✅ Linked to BuddyChip account"
    : "❌ Not linked - limited functionality"
}

*User Information:*
• Telegram ID: \`${dbTelegramUser.telegramId}\`
• Username: ${telegramUser.username ? `@${telegramUser.username}` : "Not set"}
• Name: ${telegramUser.first_name || "Not set"}${telegramUser.last_name ? ` ${telegramUser.last_name}` : ""}
• Language: ${telegramUser.language_code || "Not set"}

*Account Features:*
${
  dbTelegramUser.userId
    ? `• ✅ Twitter URL processing
• ✅ Content creation (/create)
• ✅ Image generation (/image)
• ✅ Full conversation history
• ✅ Premium AI models
• ✅ Enhanced responses`
    : `• ⚠️ Limited Twitter processing
• ⚠️ Basic content creation
• ❌ Image generation (requires linking)
• ❌ Conversation history
• ❌ Premium AI models
• ❌ Enhanced responses`
}

*Privacy & Security:*
• Your data is encrypted and secure
• We only store necessary information
• You can unlink your account anytime
• Chat history is automatically cleaned up

${
  dbTelegramUser.userId
    ? "*Linked Account Benefits:*\n• Access to premium features\n• Higher usage limits\n• Priority support\n• Advanced AI models"
    : "*Link Your Account:*\n• Get access to all premium features\n• Higher usage limits\n• Better AI responses\n• Conversation memory"
}
      `;

      const keyboard = dbTelegramUser.userId
        ? [
            [
              {
                text: "🌐 Open Dashboard",
                url: "https://buddychip.app/dashboard",
              },
            ],
            [
              { text: "🔄 Refresh Status", callback_data: "refresh_status" },
              { text: "📊 Usage Stats", callback_data: "show_usage" },
            ],
            [
              { text: "🔓 Unlink Account", callback_data: "unlink_account" },
              { text: "🗑️ Clear History", callback_data: "clear_history" },
            ],
          ]
        : [
            [{ text: "🔗 Link Account", callback_data: "link_account" }],
            [{ text: "🌐 Create Account", url: "https://buddychip.app" }],
            [{ text: "🎯 Try Demo", callback_data: "demo_mode" }],
          ];

      await context.bot.sendMessage(chatId, settingsMessage, {
        parse_mode: "Markdown",
        reply_markup: { inline_keyboard: keyboard },
      });

      context.logger.info("Settings message sent successfully", {
        chatId: chatId.toString(),
        userId: telegramUser.id.toString(),
        metadata: { 
          command: "/settings", 
          isLinked: !!dbTelegramUser.userId,
          hasUsername: !!telegramUser.username,
        },
      });
    } catch (error) {
      context.logger.error("Error in /settings command", {
        chatId: chatId.toString(),
        telegramUserId: telegramUser.id.toString(),
        metadata: { error: error instanceof Error ? error.message : String(error) },
      });

      await this.sendError(chatId, error instanceof Error ? error : new Error(String(error)), context);
    }
  }
}

import type Teleg<PERSON><PERSON>ot from "node-telegram-bot-api";
import type { TelegramHandlerContext } from "../../core/telegram-handler-context";

/**
 * Interface for all Telegram command handlers
 * Each command (like /start, /help, /create) should implement this interface
 */
export interface TelegramCommandHandler {
  /**
   * Handle the command execution
   * @param message - The Telegram message containing the command
   * @param context - Shared context with services and dependencies
   */
  handle(message: TelegramBot.Message, context: TelegramHandlerContext): Promise<void>;
  
  /**
   * Get the command this handler processes
   * @returns The command string (e.g., "start", "help", "create")
   */
  getCommand(): string;
  
  /**
   * Get a description of what this command does
   * @returns Human-readable description for help text
   */
  getDescription(): string;
  
  /**
   * Check if this handler can process the given message
   * @param message - The Telegram message to check
   * @returns True if this handler can process the message
   */
  canHandle(message: TelegramBot.Message): boolean;
}

/**
 * Abstract base class for command handlers
 * Provides common functionality and validation
 */
export abstract class BaseCommandHandler implements TelegramCommandHandler {
  constructor(
    protected readonly command: string,
    protected readonly description: string
  ) {}

  abstract handle(message: TelegramBot.Message, context: TelegramHandlerContext): Promise<void>;

  getCommand(): string {
    return this.command;
  }

  getDescription(): string {
    return this.description;
  }

  canHandle(message: TelegramBot.Message): boolean {
    if (!message.text) return false;
    
    // Check if message starts with the command
    const text = message.text.toLowerCase().trim();
    return text === `/${this.command}` || text.startsWith(`/${this.command} `);
  }

  /**
   * Extract command arguments from the message
   * @param message - The Telegram message
   * @returns Array of arguments or empty array if none
   */
  protected extractArgs(message: TelegramBot.Message): string[] {
    if (!message.text) return [];
    
    const parts = message.text.trim().split(/\s+/);
    return parts.slice(1); // Remove the command itself
  }

  /**
   * Validate that the user exists and is authorized
   * @param message - The Telegram message
   * @param context - Handler context
   * @returns True if user is valid
   */
  protected async validateUser(
    message: TelegramBot.Message, 
    context: TelegramHandlerContext
  ): Promise<boolean> {
    if (!message.from) {
      await context.bot.sendMessage(
        message.chat.id,
        "❌ Unable to identify user. Please try again."
      );
      return false;
    }
    return true;
  }

  /**
   * Send an error message to the user
   * @param chatId - The chat ID to send the error to
   * @param error - The error message or Error object
   * @param context - Handler context
   */
  protected async sendError(
    chatId: number,
    error: string | Error,
    context: TelegramHandlerContext
  ): Promise<void> {
    const errorMessage = error instanceof Error ? error.message : error;
    context.logger.error(`Command ${this.command} error: ${errorMessage}`);
    
    await context.bot.sendMessage(
      chatId,
      "❌ Sorry, something went wrong. Please try again later."
    );
  }
}

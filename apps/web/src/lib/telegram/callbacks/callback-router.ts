/**
 * Callback Router
 * Routes Telegram callback queries to appropriate handlers
 */

import type TelegramBot from "node-telegram-bot-api";
import type { Telegram<PERSON>allbackHandler } from "./base/callback-handler.interface";
import type { TelegramHandlerContext } from "../core/telegram-handler-context";
import { CopyCallbackHandler } from "./copy-callback.handler";
import { RegenerateCallbackHandler } from "./regenerate-callback.handler";
import { EnhanceCallbackHandler } from "./enhance-callback.handler";

/**
 * Router for callback query processing
 * Determines which handler should process each callback query
 */
export class TelegramCallbackRouter {
  private handlers: TelegramCallbackHandler[] = [];

  constructor(context: TelegramHandlerContext) {
    // Initialize handlers
    this.handlers = [
      new CopyCallbackHandler(),
      new RegenerateCallbackHandler(),
      new EnhanceCallbackHandler(),
    ];

    context.logger.info("Callback router initialized", {
      metadata: {
        handlerCount: this.handlers.length,
        handlers: this.handlers.map(h => ({
          prefix: h.getCallbackPrefix(),
          description: h.getDescription(),
        })),
      },
    });
  }

  /**
   * Route a callback query to the appropriate handler
   * @param query - The Telegram callback query to route
   * @param context - Handler context
   * @returns True if callback was handled, false otherwise
   */
  async routeCallback(
    query: TelegramBot.CallbackQuery,
    context: TelegramHandlerContext
  ): Promise<boolean> {
    const chatId = query.message?.chat.id;
    const callbackData = query.data;

    if (!chatId || !callbackData) {
      context.logger.warn("Callback router: Invalid callback query", {
        metadata: {
          hasChatId: !!chatId,
          hasCallbackData: !!callbackData,
          queryId: query.id,
        },
      });
      return false;
    }

    // Validate callback query
    if (!this.validateCallbackQuery(query, context)) {
      return false;
    }

    // Security check
    const securityCheck = await context.securityService.checkRateLimit(
      query.from.id.toString(),
      "callback"
    );

    if (!securityCheck.allowed) {
      context.logger.warn("Callback router: Rate limit exceeded", {
        telegramUserId: query.from.id.toString(),
        chatId: chatId.toString(),
        metadata: {
          callbackData,
          reason: securityCheck.reason,
          remaining: securityCheck.remaining,
        },
      });

      await context.bot.answerCallbackQuery(query.id, {
        text: "⚠️ Too many requests. Please wait a moment.",
        show_alert: true,
      });
      return true; // Callback was "handled" (rate limited)
    }

    // Find the handler for this callback
    for (const handler of this.handlers) {
      if (handler.canHandle(callbackData)) {
        context.logger.debug("Callback router: Routing to handler", {
          telegramUserId: query.from.id.toString(),
          chatId: chatId.toString(),
          metadata: {
            handlerPrefix: handler.getCallbackPrefix(),
            handlerDescription: handler.getDescription(),
            callbackData,
          },
        });

        try {
          await handler.handle(query, context);
          
          context.logger.debug("Callback router: Processing completed", {
            telegramUserId: query.from.id.toString(),
            chatId: chatId.toString(),
            metadata: {
              handlerPrefix: handler.getCallbackPrefix(),
              success: true,
            },
          });

          return true;
        } catch (error) {
          context.logger.error("Callback router: Handler error", {
            telegramUserId: query.from.id.toString(),
            chatId: chatId.toString(),
            metadata: {
              handlerPrefix: handler.getCallbackPrefix(),
              error: error instanceof Error ? error.message : String(error),
              callbackData,
            },
          });

          // Answer callback query with error
          await context.bot.answerCallbackQuery(query.id, {
            text: "❌ Something went wrong. Please try again.",
            show_alert: true,
          });

          return true; // Callback was handled (with error)
        }
      }
    }

    // Handle built-in callbacks that don't need dedicated handlers
    if (await this.handleBuiltInCallbacks(query, context)) {
      return true;
    }

    // No handler found for this callback
    context.logger.warn("Callback router: No handler found for callback", {
      telegramUserId: query.from.id.toString(),
      chatId: chatId.toString(),
      metadata: {
        callbackData,
        availableHandlers: this.handlers.map(h => h.getCallbackPrefix()),
      },
    });

    // Answer callback query to remove loading state
    await context.bot.answerCallbackQuery(query.id, {
      text: "🤔 Unknown action. Please try again.",
    });

    return false;
  }

  /**
   * Handle built-in callbacks that don't need dedicated handlers
   * @param query - The callback query
   * @param context - Handler context
   * @returns True if callback was handled
   */
  private async handleBuiltInCallbacks(
    query: TelegramBot.CallbackQuery,
    context: TelegramHandlerContext
  ): Promise<boolean> {
    const chatId = query.message?.chat?.id;
    const callbackData = query.data;

    if (!chatId || !callbackData) {
      context.logger.warn("Built-in callback: Missing required data", {
        telegramUserId: query.from?.id?.toString(),
        metadata: {
          hasChatId: !!chatId,
          hasCallbackData: !!callbackData,
          queryId: query.id,
        },
      });
      return false;
    }

    try {
      // Answer the callback query first
      await context.bot.answerCallbackQuery(query.id);

      switch (callbackData) {
        case "link_account":
          await this.handleLinkAccount(chatId, context);
          return true;

        case "show_help":
          await this.handleShowHelp(chatId, query.from, context);
          return true;

        case "show_settings":
          await this.handleShowSettings(chatId, query.from, context);
          return true;

        case "show_status":
          await this.handleShowStatus(chatId, query.from, context);
          return true;

        case "refresh_status":
          await this.handleShowStatus(chatId, query.from, context);
          return true;

        case "quick_create":
          await this.handleQuickCreate(chatId, context);
          return true;

        case "demo_mode":
          await this.handleDemoMode(chatId, context);
          return true;

        default:
          return false;
      }
    } catch (error) {
      context.logger.error("Error handling built-in callback", {
        telegramUserId: query.from.id.toString(),
        chatId: chatId.toString(),
        metadata: {
          callbackData,
          error: error instanceof Error ? error.message : String(error),
        },
      });
      return false;
    }
  }

  /**
   * Validate callback query structure and content
   * @param query - The callback query to validate
   * @param context - Handler context
   * @returns True if query is valid
   */
  private validateCallbackQuery(
    query: TelegramBot.CallbackQuery,
    context: TelegramHandlerContext
  ): boolean {
    if (!query.from) {
      context.logger.warn("Callback router: Invalid query - no sender", {
        metadata: { queryId: query.id },
      });
      return false;
    }

    if (!query.message?.chat?.id) {
      context.logger.warn("Callback router: Invalid query - no chat ID", {
        telegramUserId: query.from.id.toString(),
        metadata: { queryId: query.id },
      });
      return false;
    }

    if (!query.data) {
      context.logger.warn("Callback router: Invalid query - no callback data", {
        telegramUserId: query.from.id.toString(),
        metadata: { queryId: query.id },
      });
      return false;
    }

    return true;
  }

  /**
   * Handle link account callback
   */
  private async handleLinkAccount(chatId: number, context: TelegramHandlerContext): Promise<void> {
    const linkMessage = `🔗 *Link Your BuddyChip Account*

To link your account, please visit your BuddyChip dashboard and follow the Telegram integration instructions.

*Steps:*
1. Go to your BuddyChip dashboard
2. Navigate to Settings → Integrations
3. Click "Link Telegram Account"
4. Follow the instructions provided

*Benefits of linking:*
• Access to premium AI models
• Higher usage limits
• Conversation memory
• Enhanced responses
• Priority support`;

    const keyboard = [
      [{ text: "🌐 Open Dashboard", url: "https://buddychip.app/dashboard" }],
      [{ text: "📚 Help", callback_data: "show_help" }],
    ];

    await context.bot.sendMessage(chatId, linkMessage, {
      parse_mode: "Markdown",
      reply_markup: { inline_keyboard: keyboard },
    });
  }

  /**
   * Handle show help callback
   */
  private async handleShowHelp(chatId: number, user: TelegramBot.User, context: TelegramHandlerContext): Promise<void> {
    // Import and use HelpCommandHandler
    const { HelpCommandHandler } = await import("../commands/help-command.handler");
    const helpHandler = new HelpCommandHandler();

    const mockMessage: TelegramBot.Message = {
      message_id: Date.now(),
      date: Math.floor(Date.now() / 1000),
      chat: { id: chatId, type: "private" },
      from: user,
      text: "/help",
    };

    await helpHandler.handle(mockMessage, context);
  }

  /**
   * Handle show settings callback
   */
  private async handleShowSettings(chatId: number, user: TelegramBot.User, context: TelegramHandlerContext): Promise<void> {
    // Import and use SettingsCommandHandler
    const { SettingsCommandHandler } = await import("../commands/settings-command.handler");
    const settingsHandler = new SettingsCommandHandler();

    const mockMessage: TelegramBot.Message = {
      message_id: Date.now(),
      date: Math.floor(Date.now() / 1000),
      chat: { id: chatId, type: "private" },
      from: user,
      text: "/settings",
    };

    await settingsHandler.handle(mockMessage, context);
  }

  /**
   * Handle show status callback
   */
  private async handleShowStatus(chatId: number, user: TelegramBot.User, context: TelegramHandlerContext): Promise<void> {
    // Import and use StatusCommandHandler
    const { StatusCommandHandler } = await import("../commands/status-command.handler");
    const statusHandler = new StatusCommandHandler();

    const mockMessage: TelegramBot.Message = {
      message_id: Date.now(),
      date: Math.floor(Date.now() / 1000),
      chat: { id: chatId, type: "private" },
      from: user,
      text: "/status",
    };

    await statusHandler.handle(mockMessage, context);
  }

  /**
   * Handle quick create callback
   */
  private async handleQuickCreate(chatId: number, context: TelegramHandlerContext): Promise<void> {
    const createMessage = `🎨 *Quick Create*

Use the /create command to generate AI posts:

\`/create [your prompt]\`

*Examples:*
• \`/create A motivational Monday post\`
• \`/create Tips for remote work productivity\`
• \`/create Funny observation about coffee\`

What would you like to create?`;

    await context.bot.sendMessage(chatId, createMessage, {
      parse_mode: "Markdown",
    });
  }

  /**
   * Handle demo mode callback
   */
  private async handleDemoMode(chatId: number, context: TelegramHandlerContext): Promise<void> {
    const demoMessage = `🎯 *Demo Mode*

You can try these features without linking your account:

*Available Commands:*
• \`/help\` - Show help information
• \`/start\` - Welcome message
• Basic conversation (limited)

*To unlock full features:*
• Link your BuddyChip account
• Get access to premium AI models
• Higher usage limits
• Enhanced responses

Ready to get started?`;

    const keyboard = [
      [{ text: "🔗 Link Account", callback_data: "link_account" }],
      [{ text: "📚 Help", callback_data: "show_help" }],
    ];

    await context.bot.sendMessage(chatId, demoMessage, {
      parse_mode: "Markdown",
      reply_markup: { inline_keyboard: keyboard },
    });
  }

  /**
   * Get statistics about callback routing
   * @returns Routing statistics
   */
  getStats(): {
    handlerCount: number;
    handlers: Array<{
      prefix: string;
      description: string;
    }>;
  } {
    return {
      handlerCount: this.handlers.length,
      handlers: this.handlers.map(handler => ({
        prefix: handler.getCallbackPrefix(),
        description: handler.getDescription(),
      })),
    };
  }

  /**
   * Add a custom callback handler
   * @param handler - The handler to add
   */
  addHandler(handler: TelegramCallbackHandler): void {
    this.handlers.push(handler);
  }

  /**
   * Remove a handler by prefix
   * @param prefix - The prefix of the handler to remove
   * @returns True if handler was removed
   */
  removeHandler(prefix: string): boolean {
    const initialLength = this.handlers.length;
    this.handlers = this.handlers.filter(
      handler => handler.getCallbackPrefix() !== prefix
    );
    return this.handlers.length < initialLength;
  }
}

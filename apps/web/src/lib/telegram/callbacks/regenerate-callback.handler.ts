/**
 * Regenerate Callback Handler
 * Handles regenerate button clicks for both /create and Twitter URL responses
 */

import type TelegramBot from "node-telegram-bot-api";
import { BaseCallbackHandler } from "./base/callback-handler.interface";
import type { TelegramHandlerContext } from "../core/telegram-handler-context";
import { <PERSON><PERSON><PERSON>ommandHandler } from "../commands/create-command.handler";
import { TwitterUrlProcessor } from "../processors/twitter-url.processor";

/**
 * Handler for regenerate button callbacks
 * Supports both /create command posts and Twitter URL replies
 */
export class RegenerateCallbackHandler extends BaseCallbackHandler {
  private createCommandHandler: CreateCommandHandler;
  private twitterUrlProcessor: TwitterUrlProcessor;

  constructor() {
    super("regenerate", "Handle regenerate button clicks for generated content");
    this.createCommandHandler = new CreateCommandHandler();
    this.twitterUrlProcessor = new TwitterUrlProcessor();
  }

  async handle(query: TelegramBot.CallbackQuery, context: TelegramHandlerContext): Promise<void> {
    if (!await this.validateQuery(query, context)) {
      return;
    }

    const chatId = query.message?.chat?.id;
    const callbackData = query.data;

    if (!chatId || !callbackData) {
      context.logger.warn("Regenerate callback: Missing required data", {
        telegramUserId: query.from?.id?.toString(),
        metadata: {
          hasChatId: !!chatId,
          hasCallbackData: !!callbackData,
          queryId: query.id,
        },
      });
      await this.answerCallback(query, context, "❌ Invalid request");
      return;
    }

    const telegramUserId = this.extractPayload(callbackData);

    // Authorization check: ensure the callback requester is the same user who initiated the request
    if (!telegramUserId || telegramUserId !== query.from.id.toString()) {
      context.logger.warn("Regenerate callback: Authorization failed", {
        telegramUserId: query.from.id.toString(),
        chatId: chatId.toString(),
        metadata: {
          unauthorized: true,
          callbackData,
          requestingUserId: query.from.id.toString(),
          sessionUserId: telegramUserId,
        },
      });

      await this.answerCallback(query, context, "❌ Unauthorized: You can only regenerate your own content");
      return;
    }

    context.logger.logCallback(
      "regenerate",
      query.from.id.toString(),
      chatId.toString(),
      true
    );

    try {
      // Answer the callback query to remove loading state
      await this.answerCallback(query, context, "Regenerating...");

      // Get session data
      const sessionData = await context.sessionService.getSessionData(telegramUserId);

      context.logger.debug("Regenerate handler - Retrieved session data", {
        telegramUserId,
        metadata: {
          hasSessionData: !!sessionData,
          sessionType: sessionData?.type || "unknown",
          hasPrompt: !!sessionData?.prompt,
          hasTwitterUrl: !!sessionData?.twitterUrl,
          prompt: sessionData?.prompt?.substring(0, 50) + "..." || "N/A",
          twitterUrl: sessionData?.twitterUrl || "N/A",
        },
      });

      if (!sessionData) {
        await context.bot.sendMessage(
          chatId,
          "❌ Session expired. Please generate new content or try again."
        );
        return;
      }

      // Handle different session types
      if (sessionData.type === "create_command") {
        await this.handleCreateCommandRegenerate(chatId, sessionData, query.from, context);
      } else if (sessionData.type === "twitter_url") {
        await this.handleTwitterUrlRegenerate(chatId, sessionData, query.from, context);
      } else {
        // Fallback: try to determine type from available data
        if (sessionData.twitterUrl) {
          await this.handleTwitterUrlRegenerate(chatId, sessionData, query.from, context);
        } else if (sessionData.prompt) {
          await this.handleCreateCommandRegenerate(chatId, sessionData, query.from, context);
        } else {
          await context.bot.sendMessage(
            chatId,
            "❌ Cannot regenerate: No original prompt or Twitter URL found. Please create new content."
          );
        }
      }

      context.logger.info("Regenerate callback handled successfully", {
        chatId: chatId.toString(),
        userId: query.from.id.toString(),
        metadata: {
          sessionType: sessionData.type,
          hasPrompt: !!sessionData.prompt,
          hasTwitterUrl: !!sessionData.twitterUrl,
        },
      });
    } catch (error) {
      context.logger.error("Error in regenerate callback handler", {
        chatId: chatId.toString(),
        telegramUserId,
        metadata: { error: error instanceof Error ? error.message : String(error) },
      });

      await this.sendError(chatId, error instanceof Error ? error : new Error(String(error)), context);
    }
  }

  /**
   * Handle regenerate for /create command generated posts
   */
  private async handleCreateCommandRegenerate(
    chatId: number,
    sessionData: any,
    user: TelegramBot.User,
    context: TelegramHandlerContext
  ): Promise<void> {
    if (!sessionData.prompt) {
      await context.bot.sendMessage(
        chatId,
        "❌ No original prompt found. Please use /create to generate a new post."
      );
      return;
    }

    await context.bot.sendMessage(chatId, "🔄 Regenerating post...");

    context.logger.debug("Regenerating create command", {
      metadata: {
        prompt: sessionData.prompt.substring(0, 100) + "...",
        promptLength: sessionData.prompt.length,
      },
    });

    // Create a mock message to simulate the /create command
    const mockMessage: TelegramBot.Message = {
      message_id: Date.now(),
      date: Math.floor(Date.now() / 1000),
      chat: { id: chatId, type: "private" },
      from: user,
      text: `/create ${sessionData.prompt}`,
    };

    try {
      // Use the CreateCommandHandler to regenerate
      await this.createCommandHandler.handle(mockMessage, context);
    } catch (error) {
      context.logger.error("Error regenerating create command", {
        metadata: { 
          error: error instanceof Error ? error.message : String(error),
          prompt: sessionData.prompt.substring(0, 50),
        },
      });

      await context.bot.sendMessage(
        chatId,
        "❌ Error regenerating post. Please try using /create again."
      );
    }
  }

  /**
   * Handle regenerate for Twitter URL replies
   */
  private async handleTwitterUrlRegenerate(
    chatId: number,
    sessionData: any,
    user: TelegramBot.User,
    context: TelegramHandlerContext
  ): Promise<void> {
    if (!sessionData.twitterUrl) {
      await context.bot.sendMessage(
        chatId,
        "❌ No original Twitter URL found. Please send the Twitter URL again."
      );
      return;
    }

    await context.bot.sendMessage(chatId, "🔄 Regenerating reply...");

    context.logger.debug("Regenerating Twitter URL response", {
      metadata: {
        twitterUrl: sessionData.twitterUrl,
      },
    });

    // Create a mock message to simulate the Twitter URL message
    const mockMessage: TelegramBot.Message = {
      message_id: Date.now(),
      date: Math.floor(Date.now() / 1000),
      chat: { id: chatId, type: "private" },
      from: user,
      text: sessionData.twitterUrl,
    };

    try {
      // Use the TwitterUrlProcessor to regenerate
      await this.twitterUrlProcessor.process(mockMessage, context);
    } catch (error) {
      context.logger.error("Error regenerating Twitter URL response", {
        metadata: { 
          error: error instanceof Error ? error.message : String(error),
          twitterUrl: sessionData.twitterUrl,
        },
      });

      await context.bot.sendMessage(
        chatId,
        "❌ Error regenerating reply. Please send the Twitter URL again."
      );
    }
  }
}

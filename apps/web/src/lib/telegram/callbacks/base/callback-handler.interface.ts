import type Tel<PERSON><PERSON><PERSON>ot from "node-telegram-bot-api";
import type { TelegramHandlerContext } from "../../core/telegram-handler-context";

/**
 * Interface for callback query handlers
 * <PERSON>les button clicks and other callback queries
 */
export interface TelegramCallbackHandler {
  /**
   * Check if this handler can process the given callback data
   * @param data - The callback data from the button click
   * @returns True if this handler can process the callback
   */
  canHandle(data: string): boolean;
  
  /**
   * Handle the callback query
   * @param query - The Telegram callback query
   * @param context - Shared context with services and dependencies
   */
  handle(query: TelegramBot.CallbackQuery, context: TelegramHandlerContext): Promise<void>;
  
  /**
   * Get the callback prefix this handler processes
   * @returns The callback prefix (e.g., "copy", "regenerate", "enhance")
   */
  getCallbackPrefix(): string;
  
  /**
   * Get a description of what this handler does
   * @returns Human-readable description
   */
  getDescription(): string;
}

/**
 * Abstract base class for callback handlers
 * Provides common functionality and validation
 */
export abstract class BaseCallbackHandler implements TelegramCallbackHandler {
  constructor(
    protected readonly callbackPrefix: string,
    protected readonly description: string
  ) {}

  abstract handle(query: TelegramBot.CallbackQuery, context: TelegramHandlerContext): Promise<void>;

  canHandle(data: string): boolean {
    return data.startsWith(`${this.callbackPrefix}:`);
  }

  getCallbackPrefix(): string {
    return this.callbackPrefix;
  }

  getDescription(): string {
    return this.description;
  }

  /**
   * Extract the payload from callback data
   * @param data - The callback data (e.g., "copy:user123")
   * @returns The payload part after the prefix
   */
  protected extractPayload(data: string): string {
    const parts = data.split(":");
    return parts.slice(1).join(":"); // Handle payloads that might contain colons
  }

  /**
   * Validate that the callback query has required properties
   * @param query - The callback query
   * @param context - Handler context
   * @returns True if query is valid
   */
  protected async validateQuery(
    query: TelegramBot.CallbackQuery,
    context: TelegramHandlerContext
  ): Promise<boolean> {
    if (!query.message?.chat.id) {
      context.logger.warn("Callback query received without chat ID");
      return false;
    }

    if (!query.from) {
      context.logger.warn("Callback query received without sender information");
      return false;
    }

    if (!query.data) {
      context.logger.warn("Callback query received without data");
      return false;
    }

    return true;
  }

  /**
   * Answer the callback query to remove loading state
   * @param query - The callback query
   * @param context - Handler context
   * @param text - Optional text to show to the user
   */
  protected async answerCallback(
    query: TelegramBot.CallbackQuery,
    context: TelegramHandlerContext,
    text?: string
  ): Promise<void> {
    try {
      await context.bot.answerCallbackQuery(query.id, { text });
    } catch (error) {
      context.logger.warn("Failed to answer callback query", {
        metadata: { error: error instanceof Error ? error.message : String(error) }
      });
    }
  }

  /**
   * Send an error message to the user
   * @param chatId - The chat ID to send the error to
   * @param error - The error message or Error object
   * @param context - Handler context
   */
  protected async sendError(
    chatId: number,
    error: string | Error,
    context: TelegramHandlerContext
  ): Promise<void> {
    const errorMessage = error instanceof Error ? error.message : error;
    context.logger.error(`Callback handler ${this.callbackPrefix} error: ${errorMessage}`);
    
    await context.bot.sendMessage(
      chatId,
      "❌ Sorry, something went wrong. Please try again."
    );
  }
}

/**
 * Copy Callback Handler
 * Handles copy button clicks for both /create and Twitter URL responses
 */

import type TelegramBot from "node-telegram-bot-api";
import { BaseCallbackHandler } from "./base/callback-handler.interface";
import type { TelegramHandlerContext } from "../core/telegram-handler-context";

/**
 * Handler for copy button callbacks
 * Supports both /create command posts and Twitter URL replies
 */
export class CopyCallbackHandler extends BaseCallbackHandler {
  constructor() {
    super("copy", "Handle copy button clicks for generated content");
  }

  async handle(query: TelegramBot.CallbackQuery, context: TelegramHandlerContext): Promise<void> {
    if (!await this.validateQuery(query, context)) {
      return;
    }

    const chatId = query.message!.chat.id;
    const telegramUserId = this.extractPayload(query.data!);

    context.logger.logCallback(
      "copy",
      query.from.id.toString(),
      chatId.toString(),
      true
    );

    try {
      // Answer the callback query to remove loading state
      await this.answerCallback(query, context, "Preparing content...");

      // Get session data
      const sessionData = await context.sessionService.getSessionData(telegramUserId);

      context.logger.debug("Copy handler - Retrieved session data", {
        telegramUserId,
        metadata: {
          hasSessionData: !!sessionData,
          sessionType: sessionData?.type || "unknown",
          hasResponseText: !!sessionData?.responseText,
          responseTextLength: sessionData?.responseText?.length || 0,
          responseTextPreview: sessionData?.responseText?.substring(0, 50) + "..." || "N/A",
          hasTwitterUrl: !!sessionData?.twitterUrl,
        },
      });

      if (!sessionData) {
        await context.bot.sendMessage(
          chatId,
          "❌ Session expired. Please regenerate the content or try again."
        );
        return;
      }

      // Handle different session types
      if (sessionData.type === "create_command") {
        await this.handleCreateCommandCopy(chatId, sessionData, context);
      } else if (sessionData.type === "twitter_url") {
        await this.handleTwitterUrlCopy(chatId, sessionData, context);
      } else {
        // Fallback: try to determine type from available data
        if (sessionData.twitterUrl && sessionData.responseText) {
          await this.handleTwitterUrlCopy(chatId, sessionData, context);
        } else if (sessionData.responseText) {
          await this.handleCreateCommandCopy(chatId, sessionData, context);
        } else {
          await context.bot.sendMessage(
            chatId,
            "❌ No content available to copy. Please generate new content first."
          );
        }
      }

      context.logger.info("Copy callback handled successfully", {
        chatId: chatId.toString(),
        userId: query.from.id.toString(),
        metadata: {
          sessionType: sessionData.type,
          hasContent: !!sessionData.responseText,
        },
      });
    } catch (error) {
      context.logger.error("Error in copy callback handler", {
        chatId: chatId.toString(),
        telegramUserId,
        metadata: { error: error instanceof Error ? error.message : String(error) },
      });

      await this.sendError(chatId, error instanceof Error ? error : new Error(String(error)), context);
    }
  }

  /**
   * Handle copy for /create command generated posts
   */
  private async handleCreateCommandCopy(
    chatId: number,
    sessionData: any,
    context: TelegramHandlerContext
  ): Promise<void> {
    if (!sessionData.responseText) {
      await context.bot.sendMessage(
        chatId,
        "❌ No generated post found. Please use /create to generate a new post."
      );
      return;
    }

    const encodedContent = encodeURIComponent(sessionData.responseText);
    const tweetUrl = `https://twitter.com/intent/tweet?text=${encodedContent}`;

    const copyMessage = `📋 *Copy & Share Your Post:*

\`\`\`
${sessionData.responseText}
\`\`\`

🚀 [Share on Twitter](${tweetUrl})

_Click the link above to open Twitter with your AI-generated post ready to share!_

*Other sharing options:*
• Copy the text above and paste it anywhere
• Share on LinkedIn, Facebook, or other platforms
• Use as inspiration for your own content`;

    const keyboard = [
      [
        { text: "🔄 Generate New", callback_data: `regenerate:${sessionData.telegramUserId || "unknown"}` },
        { text: "✨ Enhance", callback_data: `enhance:${sessionData.telegramUserId || "unknown"}` },
      ],
      [
        { text: "🎨 Create Another", callback_data: "quick_create" },
        { text: "📚 Help", callback_data: "show_help" },
      ],
    ];

    await context.bot.sendMessage(chatId, copyMessage, {
      parse_mode: "Markdown",
      disable_web_page_preview: true,
      reply_markup: { inline_keyboard: keyboard },
    });

    context.logger.debug("Create command copy message sent", {
      metadata: {
        contentLength: sessionData.responseText.length,
        twitterUrl: tweetUrl,
      },
    });
  }

  /**
   * Handle copy for Twitter URL replies
   */
  private async handleTwitterUrlCopy(
    chatId: number,
    sessionData: any,
    context: TelegramHandlerContext
  ): Promise<void> {
    if (!sessionData.responseText || !sessionData.twitterUrl) {
      await context.bot.sendMessage(
        chatId,
        "❌ Session expired. Please send the Twitter URL again."
      );
      return;
    }

    // Extract tweet ID from the Twitter URL
    const tweetIdMatch = sessionData.twitterUrl.match(/\/status\/(\d+)/);
    if (!tweetIdMatch?.[1]) {
      await context.bot.sendMessage(
        chatId,
        "❌ Could not extract tweet ID from URL. Please try with a direct tweet link."
      );
      return;
    }

    const tweetId = tweetIdMatch[1];

    // Create Twitter intent URL with pre-filled response
    const encodedResponse = encodeURIComponent(sessionData.responseText);
    const replyUrl = `https://twitter.com/intent/tweet?in_reply_to=${tweetId}&text=${encodedResponse}`;

    const copyMessage = `🚀 *Send your reply on Twitter:*

**Your AI-generated reply:**
\`\`\`
${sessionData.responseText}
\`\`\`

[Open Twitter with pre-filled response](${replyUrl})

_Click the link above to open Twitter with your AI-generated reply ready to send!_

*Alternative options:*
• Copy the text above and paste it manually
• Edit the response before posting
• Use it as inspiration for your own reply`;

    const keyboard = [
      [
        { text: "🔄 Regenerate", callback_data: `regenerate:${sessionData.telegramUserId || "unknown"}` },
        { text: "✨ Enhance", callback_data: `enhance:${sessionData.telegramUserId || "unknown"}` },
      ],
      [
        { text: "🐦 View Original", url: sessionData.twitterUrl },
        { text: "📚 Help", callback_data: "show_help" },
      ],
    ];

    await context.bot.sendMessage(chatId, copyMessage, {
      parse_mode: "Markdown",
      disable_web_page_preview: true,
      reply_markup: { inline_keyboard: keyboard },
    });

    context.logger.debug("Twitter URL copy message sent", {
      metadata: {
        twitterUrl: sessionData.twitterUrl,
        tweetId,
        responseLength: sessionData.responseText.length,
        replyUrl,
      },
    });
  }
}

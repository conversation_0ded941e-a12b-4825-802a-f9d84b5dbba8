/**
 * Telegram validation utilities
 * Handles input validation, URL validation, and security checks
 */

import type TelegramBot from "node-telegram-bot-api";

/**
 * Validate Twitter URL format and extract components
 * @param url - The URL to validate
 * @returns Validation result with extracted components
 */
export function validateTwitterUrl(url: string): {
  isValid: boolean;
  error?: string;
  tweetId?: string;
  username?: string;
} {
  try {
    const twitterUrlRegex = /https?:\/\/(twitter\.com|x\.com)\/(\w+)\/status\/(\d+)/;
    const match = url.match(twitterUrlRegex);
    
    if (!match) {
      return {
        isValid: false,
        error: "Invalid Twitter URL format. Please provide a direct tweet link.",
      };
    }

    const [, domain, username, tweetId] = match;
    
    return {
      isValid: true,
      tweetId,
      username,
    };
  } catch (error) {
    return {
      isValid: false,
      error: "Failed to parse Twitter URL",
    };
  }
}

/**
 * Extract Twitter URL from text
 * @param text - The text to search
 * @returns The first Twitter URL found, or null
 */
export function extractTwitterUrl(text: string): string | null {
  const twitterUrlRegex = /https?:\/\/(twitter\.com|x\.com)\/\w+\/status\/\d+/;
  const match = text.match(twitterUrlRegex);
  return match ? match[0] : null;
}

/**
 * Check if text contains a Twitter URL
 * @param text - The text to check
 * @returns True if text contains a Twitter URL
 */
export function containsTwitterUrl(text: string): boolean {
  return extractTwitterUrl(text) !== null;
}

/**
 * Parse Telegram command from message text
 * @param text - The message text
 * @returns Parsed command and arguments, or null if not a command
 */
export function parseCommand(text: string): {
  command: string;
  args: string[];
} | null {
  if (!text.startsWith("/")) {
    return null;
  }

  const parts = text.split(" ");
  const command = parts[0].substring(1); // Remove the '/'
  const args = parts.slice(1);

  return { command, args };
}

/**
 * Sanitize text input for security
 * @param text - The text to sanitize
 * @returns Sanitized text or null if invalid
 */
export function sanitizeText(text: string): string | null {
  if (!text || typeof text !== "string") {
    return null;
  }

  // Remove excessive whitespace
  const cleaned = text.trim().replace(/\s+/g, " ");
  
  // Check for minimum length
  if (cleaned.length < 1) {
    return null;
  }

  // Check for maximum length (Telegram's limit is 4096 characters)
  if (cleaned.length > 4000) {
    return cleaned.substring(0, 4000);
  }

  return cleaned;
}

/**
 * Sanitize callback data for security
 * @param data - The callback data to sanitize
 * @returns Sanitized callback data
 */
export function sanitizeCallbackData(data: string): string {
  if (!data || typeof data !== "string") {
    return "";
  }

  // Remove potentially dangerous characters
  return data.replace(/[<>'"&]/g, "").trim();
}

/**
 * Validate Telegram message structure
 * @param message - The Telegram message to validate
 * @returns Validation result
 */
export function validateMessage(message: TelegramBot.Message): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!message) {
    errors.push("Message is null or undefined");
    return { isValid: false, errors };
  }

  if (!message.chat?.id) {
    errors.push("Message missing chat ID");
  }

  if (!message.from) {
    errors.push("Message missing sender information");
  }

  if (!message.text && !message.photo && !message.document) {
    errors.push("Message has no content");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Check if content should be rejected based on security rules
 * @param content - The content to check
 * @returns True if content should be rejected
 */
export function shouldRejectBasedOnContent(content: string): boolean {
  if (!content) return true;

  // Check for suspicious patterns
  const suspiciousPatterns = [
    /javascript:/i,
    /data:/i,
    /<script/i,
    /eval\(/i,
    /function\s*\(/i,
  ];

  return suspiciousPatterns.some(pattern => pattern.test(content));
}

/**
 * Check if message has security risks
 * @param message - The Telegram message to check
 * @returns True if message has security risks
 */
export function hasSecurityRisk(message: TelegramBot.Message): boolean {
  if (!message.text) return false;

  return shouldRejectBasedOnContent(message.text);
}

/**
 * Validate environment variables for Telegram integration
 * @returns Validation result with missing and optional variables
 */
export function validateTelegramEnvironment(): {
  isValid: boolean;
  missing: string[];
  optional: string[];
} {
  const required = ["TELEGRAM_BOT_TOKEN"];
  const optional = ["TELEGRAM_WEBHOOK_SECRET", "NEXT_PUBLIC_APP_URL"];

  const missing = required.filter((key) => !process.env[key]);
  const missingOptional = optional.filter((key) => !process.env[key]);

  return {
    isValid: missing.length === 0,
    missing,
    optional: missingOptional,
  };
}

/**
 * Validate bot configuration
 * @param config - The bot configuration to validate
 * @returns Validation result
 */
export function validateBotConfig(config: {
  token?: string;
  webhookUrl?: string;
  enablePolling?: boolean;
}): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!config.token) {
    errors.push("Bot token is required");
  }

  if (config.token && config.token.length < 10) {
    errors.push("Bot token appears to be invalid");
  }

  if (config.webhookUrl && !config.webhookUrl.startsWith("https://")) {
    errors.push("Webhook URL must use HTTPS");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

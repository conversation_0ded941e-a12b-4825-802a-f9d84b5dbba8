/**
 * Telegram structured logging utility
 * Provides consistent logging for all Telegram bot operations
 */

export interface TelegramLogContext {
  userId?: string;
  chatId?: string;
  command?: string;
  messageType?: string;
  telegramUserId?: string;
  sessionId?: string;
  metadata?: Record<string, any>;
}

export interface TelegramLogEntry {
  level: "info" | "warn" | "error" | "debug";
  message: string;
  context?: TelegramLogContext;
  timestamp: string;
  error?: Error;
}

/**
 * Telegram logger class for structured logging
 */
export class TelegramLogger {
  private prefix: string;

  constructor(prefix: string = "TELEGRAM") {
    this.prefix = prefix;
  }

  /**
   * Log an info message
   * @param message - The log message
   * @param context - Optional context information
   */
  info(message: string, context?: TelegramLogContext): void {
    this.log("info", message, context);
  }

  /**
   * Log a warning message
   * @param message - The log message
   * @param context - Optional context information
   */
  warn(message: string, context?: TelegramLogContext): void {
    this.log("warn", message, context);
  }

  /**
   * Log an error message
   * @param message - The log message
   * @param context - Optional context information
   * @param error - Optional error object
   */
  error(message: string, context?: TelegramLogContext, error?: Error): void {
    this.log("error", message, context, error);
  }

  /**
   * Log a debug message
   * @param message - The log message
   * @param context - Optional context information
   */
  debug(message: string, context?: TelegramLogContext): void {
    this.log("debug", message, context);
  }

  /**
   * Log a command execution
   * @param command - The command name
   * @param userId - The user ID
   * @param chatId - The chat ID
   * @param success - Whether the command was successful
   * @param metadata - Additional metadata
   */
  logCommand(
    command: string,
    userId: string,
    chatId: string,
    success: boolean,
    metadata?: Record<string, any>
  ): void {
    this.info(`Command ${command} ${success ? "completed" : "failed"}`, {
      command,
      userId,
      chatId,
      metadata: { success, ...metadata },
    });
  }

  /**
   * Log a message processing event
   * @param messageType - The type of message processed
   * @param userId - The user ID
   * @param chatId - The chat ID
   * @param success - Whether processing was successful
   * @param metadata - Additional metadata
   */
  logMessageProcessing(
    messageType: string,
    userId: string,
    chatId: string,
    success: boolean,
    metadata?: Record<string, any>
  ): void {
    this.info(`Message processing ${messageType} ${success ? "completed" : "failed"}`, {
      messageType,
      userId,
      chatId,
      metadata: { success, ...metadata },
    });
  }

  /**
   * Log a callback handling event
   * @param callbackType - The type of callback handled
   * @param userId - The user ID
   * @param chatId - The chat ID
   * @param success - Whether handling was successful
   * @param metadata - Additional metadata
   */
  logCallback(
    callbackType: string,
    userId: string,
    chatId: string,
    success: boolean,
    metadata?: Record<string, any>
  ): void {
    this.info(`Callback ${callbackType} ${success ? "completed" : "failed"}`, {
      userId,
      chatId,
      metadata: { callbackType, success, ...metadata },
    });
  }

  /**
   * Log a security event
   * @param event - The security event type
   * @param userId - The user ID
   * @param chatId - The chat ID
   * @param details - Event details
   */
  logSecurity(
    event: string,
    userId: string,
    chatId: string,
    details: Record<string, any>
  ): void {
    this.warn(`Security event: ${event}`, {
      userId,
      chatId,
      metadata: { securityEvent: event, ...details },
    });
  }

  /**
   * Core logging method
   * @param level - Log level
   * @param message - Log message
   * @param context - Optional context
   * @param error - Optional error object
   */
  private log(
    level: "info" | "warn" | "error" | "debug",
    message: string,
    context?: TelegramLogContext,
    error?: Error
  ): void {
    const logEntry: TelegramLogEntry = {
      level,
      message: `${this.prefix}: ${message}`,
      context,
      timestamp: new Date().toISOString(),
      error,
    };

    // Format the log output
    const logOutput = this.formatLogEntry(logEntry);

    // Output to appropriate console method
    switch (level) {
      case "error":
        console.error(logOutput);
        if (error) {
          console.error(error);
        }
        break;
      case "warn":
        console.warn(logOutput);
        break;
      case "debug":
        if (process.env.NODE_ENV === "development") {
          console.debug(logOutput);
        }
        break;
      default:
        console.log(logOutput);
    }
  }

  /**
   * Format log entry for output
   * @param entry - The log entry to format
   * @returns Formatted log string
   */
  private formatLogEntry(entry: TelegramLogEntry): string {
    const emoji = this.getLevelEmoji(entry.level);
    let output = `${emoji} ${entry.message}`;

    if (entry.context) {
      const contextParts: string[] = [];
      
      if (entry.context.userId) {
        contextParts.push(`userId=${entry.context.userId}`);
      }
      
      if (entry.context.chatId) {
        contextParts.push(`chatId=${entry.context.chatId}`);
      }
      
      if (entry.context.command) {
        contextParts.push(`command=${entry.context.command}`);
      }
      
      if (entry.context.messageType) {
        contextParts.push(`messageType=${entry.context.messageType}`);
      }

      if (contextParts.length > 0) {
        output += ` | ${contextParts.join(", ")}`;
      }

      if (entry.context.metadata) {
        output += ` | metadata=${JSON.stringify(entry.context.metadata)}`;
      }
    }

    return output;
  }

  /**
   * Get emoji for log level
   * @param level - The log level
   * @returns Appropriate emoji
   */
  private getLevelEmoji(level: string): string {
    switch (level) {
      case "error":
        return "❌";
      case "warn":
        return "⚠️";
      case "debug":
        return "🔍";
      default:
        return "📱";
    }
  }
}

// Export a default logger instance
export const telegramLogger = new TelegramLogger();

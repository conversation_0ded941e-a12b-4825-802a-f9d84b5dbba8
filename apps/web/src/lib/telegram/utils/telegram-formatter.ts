/**
 * Telegram message formatting utilities
 * Handles text formatting, message splitting, and Markdown processing
 */

/**
 * Split long messages to fit Telegram's character limit
 * @param text - The text to split
 * @param maxLength - Maximum length per message (default: 4000)
 * @returns Array of message chunks
 */
export function splitLongMessage(text: string, maxLength: number = 4000): string[] {
  if (text.length <= maxLength) {
    return [text];
  }

  const messages: string[] = [];
  let currentMessage = "";

  // Split by paragraphs first to maintain readability
  const paragraphs = text.split("\n\n");

  for (const paragraph of paragraphs) {
    // If adding this paragraph would exceed the limit
    if ((currentMessage + paragraph + "\n\n").length > maxLength) {
      // Save current message if it has content
      if (currentMessage.trim()) {
        messages.push(currentMessage.trim());
        currentMessage = "";
      }

      // If the paragraph itself is too long, split by sentences
      if (paragraph.length > maxLength) {
        const sentences = paragraph.split(". ");
        for (const sentence of sentences) {
          const sentenceWithPeriod = sentence.endsWith(".") ? sentence : sentence + ".";
          
          if ((currentMessage + sentenceWithPeriod + " ").length > maxLength) {
            if (currentMessage.trim()) {
              messages.push(currentMessage.trim());
              currentMessage = "";
            }
            
            // If even a single sentence is too long, split by words
            if (sentenceWithPeriod.length > maxLength) {
              const words = sentenceWithPeriod.split(" ");
              for (const word of words) {
                if ((currentMessage + word + " ").length > maxLength) {
                  if (currentMessage.trim()) {
                    messages.push(currentMessage.trim());
                    currentMessage = "";
                  }
                  currentMessage = word + " ";
                } else {
                  currentMessage += word + " ";
                }
              }
            } else {
              currentMessage = sentenceWithPeriod + " ";
            }
          } else {
            currentMessage += sentenceWithPeriod + " ";
          }
        }
      } else {
        currentMessage = paragraph + "\n\n";
      }
    } else {
      currentMessage += paragraph + "\n\n";
    }
  }

  // Add any remaining content
  if (currentMessage.trim()) {
    messages.push(currentMessage.trim());
  }

  return messages;
}

/**
 * Format text for Telegram Markdown
 * Escapes special characters while preserving intentional formatting
 * @param text - The text to format
 * @returns Formatted text safe for Telegram Markdown
 */
export function formatTelegramMarkdown(text: string): string {
  // Escape special characters for Telegram MarkdownV2
  return text
    .replace(/[_*[\]()~`>#+=|{}.!-]/g, "\\$&")
    .replace(/\\\*/g, "*") // Allow intentional bold
    .replace(/\\_/g, "_") // Allow intentional italic
    .replace(/\\`/g, "`"); // Allow intentional code
}

/**
 * Create inline keyboard markup for Telegram
 * @param buttons - Array of button rows
 * @returns Telegram inline keyboard markup
 */
export function createInlineKeyboard(
  buttons: Array<
    Array<{
      text: string;
      callback_data?: string;
      url?: string;
    }>
  >
) {
  return {
    inline_keyboard: buttons,
  };
}

/**
 * Format response for Telegram with proper message splitting
 * @param text - The response text to format
 * @param maxLength - Maximum length per message
 * @returns Array of formatted message chunks
 */
export function formatForTelegram(text: string, maxLength: number = 4000): string[] {
  if (text.length <= maxLength) {
    return [text];
  }

  return splitLongMessage(text, maxLength);
}

/**
 * Create a formatted status message
 * @param title - The status title
 * @param items - Array of status items
 * @returns Formatted status message
 */
export function formatStatusMessage(
  title: string,
  items: Array<{ label: string; value: string; emoji?: string }>
): string {
  let message = `*${title}*\n\n`;
  
  for (const item of items) {
    const emoji = item.emoji ? `${item.emoji} ` : "";
    message += `${emoji}*${item.label}:* ${item.value}\n`;
  }
  
  return message;
}

/**
 * Create a formatted error message
 * @param error - The error message or Error object
 * @param context - Optional context information
 * @returns Formatted error message
 */
export function formatErrorMessage(error: string | Error, context?: string): string {
  const errorText = error instanceof Error ? error.message : error;
  const contextText = context ? ` (${context})` : "";
  return `❌ Error${contextText}: ${errorText}`;
}

/**
 * Create a formatted success message
 * @param message - The success message
 * @param details - Optional additional details
 * @returns Formatted success message
 */
export function formatSuccessMessage(message: string, details?: string): string {
  const detailsText = details ? `\n\n${details}` : "";
  return `✅ ${message}${detailsText}`;
}

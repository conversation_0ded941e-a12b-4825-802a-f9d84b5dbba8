/**
 * Query Optimization Service
 *
 * Provides optimized query patterns and utilities to prevent N+1 queries,
 * improve pagination performance, and implement efficient data loading strategies.
 */

import { type FeatureType, type Prisma, Prisma as PrismaUtils } from "../../prisma/generated";
import { databaseOptimizer } from "./database-optimizer";
import { performanceMonitor } from "./performance-monitor";

/**
 * Cursor-based pagination utilities for efficient large dataset navigation
 */
export class CursorPagination {
  /**
   * Create optimized cursor for timestamp + ID pagination
   */
  static createCursor(timestamp: Date, id: string): string {
    return `${timestamp.getTime()}_${id}`;
  }

  /**
   * Parse cursor back to timestamp and ID
   */
  static parseCursor(cursor: string): { timestamp: Date; id: string } | null {
    try {
      const [timestampStr, id] = cursor.split("_");
      const timestamp = new Date(parseInt(timestampStr));

      if (isNaN(timestamp.getTime()) || !id) {
        return null;
      }

      return { timestamp, id };
    } catch {
      return null;
    }
  }

  /**
   * Build optimized where clause for cursor pagination
   */
  static buildWhereClause(
    cursor: string | undefined,
    timestampField: string = "createdAt",
    idField: string = "id"
  ): Prisma.MentionWhereInput | {} {
    if (!cursor) return {};

    const parsed = CursorPagination.parseCursor(cursor);
    if (!parsed) return {};

    return {
      OR: [
        { [timestampField]: { lt: parsed.timestamp } },
        {
          [timestampField]: parsed.timestamp,
          [idField]: { lt: parsed.id },
        },
      ],
    };
  }
}

/**
 * Optimized query builders for common patterns
 */
export class QueryBuilders {
  /**
   * Build optimized mention query with selective includes
   */
  static buildMentionQuery(options: {
    userId?: string;
    accountId?: string;
    limit?: number;
    cursor?: string;
    includeResponses?: boolean;
    includeAccount?: boolean;
    includeImages?: boolean;
    archived?: boolean;
    processed?: boolean;
    minBullishScore?: number;
  }) {
    const {
      userId,
      accountId,
      limit = 20,
      cursor,
      includeResponses = false,
      includeAccount = false,
      includeImages = false,
      archived = false,
      processed,
      minBullishScore,
    } = options;

    // Build where clause
    const where: Prisma.MentionWhereInput = {
      archived,
      ...(userId && { userId }),
      ...(accountId && { accountId }),
      ...(processed !== undefined && { processed }),
      ...(minBullishScore && {
        bullishScore: { gte: minBullishScore },
      }),
      ...CursorPagination.buildWhereClause(cursor, "mentionedAt"),
    };

    // Build select clause for optimal performance
    const select: Prisma.MentionSelect = {
      id: true,
      content: true,
      authorName: true,
      authorHandle: true,
      authorAvatarUrl: true,
      link: true,
      mentionedAt: true,
      createdAt: true,
      bullishScore: true,
      importanceScore: true,
      keywords: true,
      processed: true,
      ...(includeAccount && {
        account: {
          select: {
            id: true,
            twitterHandle: true,
            displayName: true,
            avatarUrl: true,
            isActive: true,
          },
        },
      }),
      ...(includeResponses && {
        responses: {
          select: {
            id: true,
            content: true,
            createdAt: true,
            model: true,
          },
          orderBy: { createdAt: "desc" },
          take: 3, // Limit to recent responses
        },
      }),
      ...(includeImages && {
        images: {
          select: {
            id: true,
            url: true,
            altText: true,
          },
        },
      }),
    };

    return {
      where,
      select,
      take: limit,
      orderBy: [{ mentionedAt: "desc" as const }, { id: "desc" as const }],
    };
  }

  /**
   * Build optimized user query with plan and usage data
   */
  static buildUserWithPlanQuery(userId: string) {
    return {
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true,
        avatarUrl: true,
        planId: true,
        personalityId: true,
        customSystemPrompt: true,
        useFirstPerson: true,
        createdAt: true,
        lastActiveAt: true,
        plan: {
          select: {
            id: true,
            name: true,
            price: true,
            features: {
              select: {
                feature: true,
                limit: true,
              },
            },
          },
        },
        personality: {
          select: {
            id: true,
            name: true,
            systemPrompt: true,
          },
        },
        // Aggregate current period usage
        _count: {
          select: {
            usageLogs: {
              where: {
                billingPeriod: {
                  gte: new Date(
                    new Date().getFullYear(),
                    new Date().getMonth(),
                    1
                  ),
                },
              },
            },
          },
        },
      },
    };
  }

  /**
   * Build optimized account query with mention counts
   */
  static buildAccountWithStatsQuery(userId: string) {
    return {
      where: { userId },
      select: {
        id: true,
        twitterHandle: true,
        displayName: true,
        avatarUrl: true,
        isActive: true,
        lastSyncAt: true,
        totalMentions: true,
        syncSettings: true,
        createdAt: true,
        // Get recent mention count
        _count: {
          select: {
            mentions: {
              where: {
                mentionedAt: {
                  gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
                },
                archived: false,
              },
            },
          },
        },
      },
      orderBy: [{ isActive: "desc" as const }, { lastSyncAt: "desc" as const }],
    };
  }
}

/**
 * Data loader patterns to prevent N+1 queries
 */
export class DataLoaders {
  /**
   * Batch load AI responses for multiple mentions
   */
  static async batchLoadResponses(mentionIds: string[]) {
    if (mentionIds.length === 0) return new Map();

    const responses = await performanceMonitor.trackQuery(
      "responses.batchLoad",
      () =>
        databaseOptimizer.client.aIResponse.findMany({
          where: {
            mentionId: { in: mentionIds },
          },
          select: {
            id: true,
            mentionId: true,
            content: true,
            createdAt: true,
            model: true,
          },
          orderBy: { createdAt: "desc" },
        }),
      { mentionIds: mentionIds.length }
    );

    // Group by mention ID
    const responseMap = new Map<string, typeof responses>();
    responses.forEach((response) => {
      const existing = responseMap.get(response.mentionId) || [];
      existing.push(response);
      responseMap.set(response.mentionId, existing);
    });

    return responseMap;
  }

  /**
   * Batch load account information for multiple mentions
   */
  static async batchLoadAccounts(accountIds: string[]) {
    if (accountIds.length === 0) return new Map();

    const accounts = await performanceMonitor.trackQuery(
      "accounts.batchLoad",
      () =>
        databaseOptimizer.client.monitoredAccount.findMany({
          where: {
            id: { in: accountIds },
          },
          select: {
            id: true,
            twitterHandle: true,
            displayName: true,
            avatarUrl: true,
            isActive: true,
          },
        }),
      { accountIds: accountIds.length }
    );

    const accountMap = new Map();
    accounts.forEach((account) => {
      accountMap.set(account.id, account);
    });

    return accountMap;
  }

  /**
   * Batch load usage statistics for multiple users
   */
  static async batchLoadUsageStats(userIds: string[], feature?: string) {
    if (userIds.length === 0) return new Map();

    const currentPeriodStart = new Date(
      new Date().getFullYear(),
      new Date().getMonth(),
      1
    );

    // Using raw query for groupBy to avoid type issues
    interface RawUsageResult {
      userId: string;
      feature: FeatureType;
      amount: number | null;
    }

    const usageStats = await performanceMonitor.trackQuery(
      "usage.batchLoad",
      async () => {
        if (feature) {
          return await databaseOptimizer.client.$queryRaw<RawUsageResult[]>`
            SELECT 
              "userId",
              feature,
              SUM(amount)::int as amount
            FROM "UsageLog"
            WHERE "userId" IN (${PrismaUtils.join(userIds)})
              AND "billingPeriod" >= ${currentPeriodStart}
              AND feature = ${feature}::"FeatureType"
            GROUP BY "userId", feature
          `;
        } else {
          return await databaseOptimizer.client.$queryRaw<RawUsageResult[]>`
            SELECT 
              "userId",
              feature,
              SUM(amount)::int as amount
            FROM "UsageLog"
            WHERE "userId" IN (${PrismaUtils.join(userIds)})
              AND "billingPeriod" >= ${currentPeriodStart}
            GROUP BY "userId", feature
          `;
        }
      },
      { userIds: userIds.length, feature }
    );

    const usageMap = new Map<string, Record<string, number>>();
    usageStats.forEach((stat) => {
      const userUsage = usageMap.get(stat.userId) || {};
      userUsage[stat.feature] = stat.amount || 0;
      usageMap.set(stat.userId, userUsage);
    });

    return usageMap;
  }
}

/**
 * Query optimization utilities
 */
export class QueryOptimizer {
  /**
   * Optimize a query by analyzing its execution plan
   */
  static async analyzeQuery(sql: string, params?: any[]) {
    try {
      const plan = await databaseOptimizer.client.$queryRawUnsafe(
        `EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) ${sql}`,
        ...(params || [])
      );

      console.log("📊 Query Execution Plan:", JSON.stringify(plan, null, 2));
      return plan;
    } catch (error) {
      console.error("❌ Failed to analyze query:", error);
      return null;
    }
  }

  /**
   * Suggest optimizations for slow queries
   */
  static suggestOptimizations(executionTimeMs: number, resultCount: number) {
    const suggestions: string[] = [];

    if (executionTimeMs > 1000) {
      suggestions.push("🐌 Query is very slow (>1s) - consider adding indexes");
    }

    if (executionTimeMs > 500) {
      suggestions.push(
        "⚠️ Query is moderately slow (>500ms) - review query structure"
      );
    }

    if (resultCount > 1000) {
      suggestions.push("📄 Large result set - consider pagination");
    }

    if (resultCount === 0) {
      suggestions.push("🔍 No results - check if indexes are being used");
    }

    return suggestions;
  }

  /**
   * Monitor query performance and suggest improvements
   */
  static async monitorQuery<T>(
    queryName: string,
    queryFn: () => Promise<T>,
    options: {
      warnThreshold?: number;
      errorThreshold?: number;
      logResults?: boolean;
    } = {}
  ): Promise<T> {
    const {
      warnThreshold = 500,
      errorThreshold = 1000,
      logResults = false,
    } = options;

    const startTime = performance.now();

    try {
      const result = await queryFn();
      const duration = performance.now() - startTime;

      let resultCount = 0;
      if (Array.isArray(result)) {
        resultCount = result.length;
      } else if (result && typeof result === "object" && "length" in result) {
        resultCount = (result as any).length;
      }

      // Log performance
      if (duration > errorThreshold) {
        console.error(
          `🚨 SLOW QUERY: ${queryName} took ${duration.toFixed(2)}ms`
        );
      } else if (duration > warnThreshold) {
        console.warn(
          `⚠️ MODERATE QUERY: ${queryName} took ${duration.toFixed(2)}ms`
        );
      } else {
        console.log(
          `⚡ FAST QUERY: ${queryName} took ${duration.toFixed(2)}ms`
        );
      }

      // Suggest optimizations
      const suggestions = QueryOptimizer.suggestOptimizations(
        duration,
        resultCount
      );
      if (suggestions.length > 0) {
        console.log(`💡 Optimization suggestions for ${queryName}:`);
        suggestions.forEach((suggestion) => console.log(`   ${suggestion}`));
      }

      if (logResults) {
        console.log(`📊 Query ${queryName} returned ${resultCount} results`);
      }

      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      console.error(
        `❌ FAILED QUERY: ${queryName} failed after ${duration.toFixed(2)}ms`,
        error
      );
      throw error;
    }
  }
}

/**
 * Export all optimization utilities
 */
export const queryOptimizer = {
  cursor: CursorPagination,
  builders: QueryBuilders,
  loaders: DataLoaders,
  optimizer: QueryOptimizer,
};

/**
 * Common tRPC Schemas and Validation Patterns
 *
 * Consolidates Zod schemas used across multiple routers to ensure consistency
 * and reduce duplication. Includes input validation, response schemas, and
 * common data structures.
 */

import { z } from "zod";
import { FeatureType } from "../../prisma/generated/index.js";

/**
 * Common input schemas
 */
export const commonInputSchemas = {
  // Pagination
  pagination: z.object({
    limit: z.number().min(1).max(100).default(10),
    cursor: z.string().optional(),
    offset: z.number().min(0).optional(),
  }),

  // ID validation
  id: z.string().min(1, "ID is required"),
  cuid: z.string().cuid("Invalid ID format"),

  // Date ranges
  dateRange: z
    .object({
      startDate: z.date().optional(),
      endDate: z.date().optional(),
    })
    .refine((data) => {
      if (data.startDate && data.endDate) {
        return data.startDate <= data.endDate;
      }
      return true;
    }, "Start date must be before end date"),

  // Twitter-specific
  twitterHandle: z
    .string()
    .min(1, "Twitter handle is required")
    .max(15, "Twitter handle too long")
    .regex(/^@?[a-zA-Z0-9_]+$/, "Invalid Twitter handle format")
    .transform((val) => val.replace("@", "")),

  twitterUrl: z
    .string()
    .url("Invalid URL format")
    .refine(
      (url) => url.includes("twitter.com") || url.includes("x.com"),
      "Must be a Twitter/X URL"
    ),

  // Content validation
  mentionContent: z
    .string()
    .min(1, "Content is required")
    .max(10000, "Content too long"),

  // Feature types
  featureType: z.nativeEnum(FeatureType),

  // Search and filtering
  searchQuery: z
    .string()
    .min(1, "Search query is required")
    .max(500, "Search query too long"),

  // Sync settings
  syncSettings: z.object({
    syncMentions: z.boolean().default(true),
    syncUserTweets: z.boolean().default(false),
    syncReplies: z.boolean().default(false),
    syncRetweets: z.boolean().default(true),
  }),

  // Tweet type filters
  tweetTypes: z.object({
    mentions: z.boolean().default(true),
    userTweets: z.boolean().default(true),
    replies: z.boolean().default(true),
    retweets: z.boolean().default(true),
  }),
};

/**
 * Mention-specific schemas
 */
export const mentionSchemas = {
  // Mention creation from URL
  createFromUrl: z.object({
    url: commonInputSchemas.twitterUrl,
    accountId: commonInputSchemas.cuid.optional(),
  }),

  // Mention filtering
  mentionFilters: z.object({
    accountId: commonInputSchemas.cuid.optional(),
    archived: z.boolean().default(false),
    hasResponse: z.boolean().optional(),
    sentiment: z.enum(["positive", "negative", "neutral"]).optional(),
    tweetTypes: commonInputSchemas.tweetTypes.optional(),
    dateRange: commonInputSchemas.dateRange.optional(),
  }),

  // Mention update
  updateMention: z.object({
    mentionId: commonInputSchemas.cuid,
    bullishScore: z.number().min(0).max(100).optional(),
    archived: z.boolean().optional(),
    hasResponse: z.boolean().optional(),
    notes: z.string().max(1000).optional(),
  }),
};

/**
 * Account-specific schemas
 */
export const accountSchemas = {
  // Add monitored account
  addAccount: z.object({
    handle: commonInputSchemas.twitterHandle,
    syncSettings: commonInputSchemas.syncSettings.optional(),
  }),

  // Update account settings
  updateAccount: z.object({
    accountId: commonInputSchemas.cuid,
    syncSettings: commonInputSchemas.syncSettings.optional(),
    isActive: z.boolean().optional(),
  }),

  // Account sync
  syncAccount: z.object({
    accountId: commonInputSchemas.cuid,
    force: z.boolean().default(false),
    syncType: z.enum(["mentions", "tweets", "all"]).default("all"),
  }),
};

/**
 * AI/Benji-specific schemas
 */
export const aiSchemas = {
  // Generate response
  generateResponse: z.object({
    mentionId: commonInputSchemas.cuid,
    mentionContent: commonInputSchemas.mentionContent,
    authorInfo: z
      .object({
        name: z.string(),
        handle: commonInputSchemas.twitterHandle,
        avatarUrl: z.string().url().optional(),
      })
      .optional(),
    enhance: z.boolean().default(false),
    customPrompt: z.string().max(2000).optional(),
  }),

  // Memory operations
  memoryQuery: z.object({
    query: commonInputSchemas.searchQuery,
    limit: z.number().min(1).max(50).default(10),
    memoryType: z
      .enum(["conversation", "preference", "fact", "context"])
      .optional(),
  }),

  // Image generation
  generateImage: z.object({
    prompt: z
      .string()
      .min(1, "Prompt is required")
      .max(1000, "Prompt too long"),
    style: z
      .enum(["realistic", "artistic", "cartoon", "abstract"])
      .default("realistic"),
    size: z.enum(["square", "portrait", "landscape"]).default("square"),
  }),
};

/**
 * Crypto-specific schemas
 */
export const cryptoSchemas = {
  // Project search
  projectSearch: z.object({
    query: commonInputSchemas.searchQuery.optional(),
    sector: z.string().optional(),
    limit: z.number().min(1).max(50).default(20),
  }),

  // Smart followers
  smartFollowers: z.object({
    projectSlug: z.string().min(1, "Project slug is required"),
    limit: z.number().min(1).max(100).default(50),
  }),

  // Trending projects
  trendingProjects: z.object({
    timeframe: z.enum(["24h", "7d", "30d"]).default("24h"),
    sector: z.string().optional(),
    limit: z.number().min(1).max(50).default(20),
  }),
};

/**
 * Notepad-specific schemas
 */
export const notepadSchemas = {
  // Get or create notepad
  getOrCreate: z.object({
    mentionId: commonInputSchemas.cuid,
  }),

  // Update notes
  updateNotes: z.object({
    notepadId: commonInputSchemas.cuid,
    notes: z.string().max(10000, "Notes too long"),
  }),

  // Add source
  addSource: z.object({
    notepadId: commonInputSchemas.cuid,
    url: z.string().url("Invalid URL"),
    title: z.string().max(200, "Title too long"),
    summary: z.string().max(1000, "Summary too long").optional(),
  }),

  // Create draft
  createDraft: z.object({
    notepadId: commonInputSchemas.cuid,
    content: z
      .string()
      .min(1, "Content is required")
      .max(5000, "Content too long"),
    title: z.string().max(100, "Title too long").optional(),
  }),
};

/**
 * User-specific schemas
 */
export const userSchemas = {
  // Update profile
  updateProfile: z.object({
    name: z.string().max(100, "Name too long").optional(),
    bio: z.string().max(500, "Bio too long").optional(),
    avatar: z.string().url("Invalid avatar URL").optional(),
  }),

  // Update personality
  updatePersonality: z.object({
    personalityId: commonInputSchemas.cuid.nullable(),
    customSystemPrompt: z
      .string()
      .max(2000, "System prompt too long")
      .optional(),
    useFirstPerson: z.boolean().optional(),
  }),

  // Feature usage check
  canUseFeature: z.object({
    feature: commonInputSchemas.featureType,
  }),
};

/**
 * Response schemas for consistent API responses
 */
export const responseSchemas = {
  // Success response
  success: <T extends z.ZodSchema>(dataSchema: T) =>
    z.object({
      success: z.literal(true),
      data: dataSchema,
      message: z.string().optional(),
      metadata: z.record(z.any()).optional(),
    }),

  // Error response
  error: z.object({
    success: z.literal(false),
    error: z.string(),
    code: z.string().optional(),
    details: z.record(z.any()).optional(),
  }),

  // Paginated response
  paginated: <T extends z.ZodSchema>(itemSchema: T) =>
    z.object({
      success: z.literal(true),
      data: z.array(itemSchema),
      pagination: z.object({
        nextCursor: z.string().optional(),
        hasNextPage: z.boolean(),
        totalCount: z.number().optional(),
        limit: z.number(),
      }),
    }),

  // Rate limit info
  rateLimitInfo: z.object({
    allowed: z.boolean(),
    currentUsage: z.number(),
    limit: z.number(),
    remaining: z.number(),
    resetTime: z.date().optional(),
  }),
};

/**
 * Utility functions for schema composition
 */
export const schemaUtils = {
  // Combine pagination with filters
  withPagination: <T extends z.AnyZodObject>(filterSchema: T) =>
    commonInputSchemas.pagination.merge(filterSchema),

  // Add common metadata to any schema
  withMetadata: <T extends z.AnyZodObject>(schema: T) =>
    schema.extend({
      metadata: z.record(z.any()).optional(),
    }),

  // Create optional version of any schema
  makeOptional: <T extends z.AnyZodObject>(schema: T) => schema.partial(),

  // Validate and transform Twitter handle
  normalizeTwitterHandle: (handle: string) =>
    commonInputSchemas.twitterHandle.parse(handle),

  // Validate CUID
  validateCUID: (id: string) => commonInputSchemas.cuid.parse(id),
};

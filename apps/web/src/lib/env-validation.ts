/**
 * Environment Variable Validation
 *
 * Validates and provides type-safe access to environment variables
 */

import { z } from "zod";

// Define environment variable schema
const envSchema = z.object({
  // Core application
  NODE_ENV: z
    .enum(["development", "production", "test"])
    .default("development"),
  PORT: z.string().transform(Number).default("3000"),

  // Database
  DATABASE_URL: z.string().min(1, "DATABASE_URL is required"),
  DIRECT_URL: z.string().min(1, "DIRECT_URL is required"),

  // Authentication
  CLERK_SECRET_KEY: z.string().min(1, "CLERK_SECRET_KEY is required"),
  CLERK_WEBHOOK_SIGNING_SECRET: z.string().optional(),
  NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: z
    .string()
    .min(1, "NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY is required"),

  // AI Providers
  OPENROUTER_API_KEY: z.string().min(1, "OPENROUTER_API_KEY is required"),
  OPENAI_API_KEY: z.string().min(1, "OPENAI_API_KEY is required"),
  XAI_API_KEY: z.string().min(1, "XAI_API_KEY is required"),
  PERPLEXITY_API_KEY: z.string().min(1, "PERPLEXITY_API_KEY is required"),
  EXA_API_KEY: z.string().min(1, "EXA_API_KEY is required"),
  COOKIE_API_KEY: z.string().optional(),

  // External Services
  TWITTER_API_KEY: z.string().min(1, "TWITTER_API_KEY is required"),
  UPLOADTHING_TOKEN: z.string().min(1, "UPLOADTHING_TOKEN is required"),
  MEM0_API_KEY: z.string().min(1, "MEM0_API_KEY is required"),
  
  // Telegram Bot
  TELEGRAM_BOT_TOKEN: z.string().min(1, "TELEGRAM_BOT_TOKEN is required"),
  TELEGRAM_WEBHOOK_SECRET: z.string().min(32, "TELEGRAM_WEBHOOK_SECRET is required and must be at least 32 characters"),

  // Rate Limiting & Cache
  KV_URL: z.string().min(1, "KV_URL is required"),
  KV_TOKEN: z.string().min(1, "KV_TOKEN is required"),

  // Monitoring
  SENTRY_AUTH_TOKEN: z.string().optional(),

  // Configuration
  OR_SITE_URL: z.string().url().optional(),
  OR_APP_NAME: z.string().optional(),
  CORS_ORIGIN: z.string().optional(),

  // Development Flags
  VERBOSE_LOGGING: z
    .string()
    .transform((val) => val === "true")
    .default("false"),
  ENABLE_PRISMA_QUERY_LOGS: z
    .string()
    .transform((val) => val === "true")
    .default("false"),
  ENABLE_CONTEXT_LOGS: z
    .string()
    .transform((val) => val === "true")
    .default("false"),
  ENABLE_TRPC_REQUEST_LOGS: z
    .string()
    .transform((val) => val === "true")
    .default("false"),
});

// Export type for TypeScript support
export type Environment = z.infer<typeof envSchema>;

// Validate and export environment
let validatedEnv: Environment;

try {
  validatedEnv = envSchema.parse(process.env);
} catch (error) {
  if (error instanceof z.ZodError) {
    const missingVars = error.errors
      .map((err) => `${err.path.join(".")}: ${err.message}`)
      .join("\n");

    console.error("❌ Environment validation failed:");
    console.error(missingVars);

    // In production, this should crash the app immediately
    if (process.env.NODE_ENV === "production") {
      console.error(
        "🚨 PRODUCTION DEPLOYMENT FAILED: Missing critical environment variables"
      );
      process.exit(1);
    }

    // In development/test, warn but allow partial functionality
    console.warn(
      "⚠️ Development environment has missing variables - some features may not work"
    );

    // Create a partial environment with defaults for development
    validatedEnv = {
      NODE_ENV: (process.env.NODE_ENV as any) || "development",
      PORT: Number(process.env.PORT) || 3000,
      DATABASE_URL: process.env.DATABASE_URL || "",
      DIRECT_URL: process.env.DIRECT_URL || "",
      CLERK_SECRET_KEY: process.env.CLERK_SECRET_KEY || "",
      CLERK_WEBHOOK_SIGNING_SECRET: process.env.CLERK_WEBHOOK_SIGNING_SECRET,
      NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY:
        process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY || "",
      OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY || "",
      OPENAI_API_KEY: process.env.OPENAI_API_KEY || "",
      XAI_API_KEY: process.env.XAI_API_KEY || "",
      PERPLEXITY_API_KEY: process.env.PERPLEXITY_API_KEY || "",
      EXA_API_KEY: process.env.EXA_API_KEY || "",
      COOKIE_API_KEY: process.env.COOKIE_API_KEY,
      TWITTER_API_KEY: process.env.TWITTER_API_KEY || "",
      UPLOADTHING_TOKEN: process.env.UPLOADTHING_TOKEN || "",
      MEM0_API_KEY: process.env.MEM0_API_KEY || "",
      TELEGRAM_BOT_TOKEN: process.env.TELEGRAM_BOT_TOKEN || "",
      TELEGRAM_WEBHOOK_SECRET: process.env.TELEGRAM_WEBHOOK_SECRET || "",
      KV_URL: process.env.KV_URL || "",
      KV_TOKEN: process.env.KV_TOKEN || "",
      SENTRY_AUTH_TOKEN: process.env.SENTRY_AUTH_TOKEN,
      OR_SITE_URL: process.env.OR_SITE_URL,
      OR_APP_NAME: process.env.OR_APP_NAME,
      CORS_ORIGIN: process.env.CORS_ORIGIN,
      VERBOSE_LOGGING: process.env.VERBOSE_LOGGING === "true",
      ENABLE_PRISMA_QUERY_LOGS: process.env.ENABLE_PRISMA_QUERY_LOGS === "true",
      ENABLE_CONTEXT_LOGS: process.env.ENABLE_CONTEXT_LOGS === "true",
      ENABLE_TRPC_REQUEST_LOGS: process.env.ENABLE_TRPC_REQUEST_LOGS === "true",
    };
  } else {
    throw error;
  }
}

export const env = validatedEnv;

/**
 * Validate environment for specific features
 */
export function validateFeatureEnv(
  feature: "ai" | "twitter" | "auth" | "database" | "monitoring"
): boolean {
  try {
    switch (feature) {
      case "ai":
        z.object({
          OPENROUTER_API_KEY: z.string().min(1),
          OPENAI_API_KEY: z.string().min(1),
          XAI_API_KEY: z.string().min(1),
          PERPLEXITY_API_KEY: z.string().min(1),
          EXA_API_KEY: z.string().min(1),
        }).parse(env);
        return true;

      case "twitter":
        z.object({
          TWITTER_API_KEY: z.string().min(1),
        }).parse(env);
        return true;

      case "auth":
        z.object({
          CLERK_SECRET_KEY: z.string().min(1),
          NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: z.string().min(1),
        }).parse(env);
        return true;

      case "database":
        z.object({
          DATABASE_URL: z.string().min(1),
          DIRECT_URL: z.string().min(1),
        }).parse(env);
        return true;

      case "monitoring":
        // Monitoring is optional
        return true;

      default:
        return false;
    }
  } catch {
    return false;
  }
}

/**
 * Get missing environment variables for a feature
 */
export function getMissingEnvVars(
  feature: "ai" | "twitter" | "auth" | "database" | "monitoring"
): string[] {
  const required = {
    ai: [
      "OPENROUTER_API_KEY",
      "OPENAI_API_KEY",
      "XAI_API_KEY",
      "PERPLEXITY_API_KEY",
      "EXA_API_KEY",
    ],
    twitter: ["TWITTER_API_KEY"],
    auth: ["CLERK_SECRET_KEY", "NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY"],
    database: ["DATABASE_URL", "DIRECT_URL"],
    monitoring: [], // Optional
  };

  return required[feature].filter((key) => !process.env[key]);
}

/**
 * Check if we're in a production environment
 */
export function isProduction(): boolean {
  return env.NODE_ENV === "production";
}

/**
 * Check if we're in a development environment
 */
export function isDevelopment(): boolean {
  return env.NODE_ENV === "development";
}

/**
 * Check if we're in a test environment
 */
export function isTest(): boolean {
  return env.NODE_ENV === "test";
}

/**
 * Get safe environment info for logging (no secrets)
 */
export function getSafeEnvInfo() {
  return {
    NODE_ENV: env.NODE_ENV,
    hasDatabase: !!env.DATABASE_URL,
    hasAuth: !!env.CLERK_SECRET_KEY,
    hasAI: validateFeatureEnv("ai"),
    hasTwitter: validateFeatureEnv("twitter"),
    hasMonitoring: !!env.SENTRY_AUTH_TOKEN,
    verboseLogging: env.VERBOSE_LOGGING,
    features: {
      prismaLogs: env.ENABLE_PRISMA_QUERY_LOGS,
      contextLogs: env.ENABLE_CONTEXT_LOGS,
      trpcLogs: env.ENABLE_TRPC_REQUEST_LOGS,
    },
  };
}

// Log environment info on startup (development only)
if (isDevelopment() && env.VERBOSE_LOGGING) {
  console.log("🔧 Environment Info:", getSafeEnvInfo());
}

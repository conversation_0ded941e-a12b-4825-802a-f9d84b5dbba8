/**
 * Secure Error Handling
 * 
 * Provides secure error handling that prevents information disclosure
 * while maintaining proper logging for debugging
 */

import { NextResponse } from "next/server";
import { ZodError } from "zod";
import { logSecurityEvent } from "./security-utils";

/**
 * Error types for classification
 */
export enum ErrorType {
  VALIDATION_ERROR = "VALIDATION_ERROR",
  AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR",
  AUTHORIZATION_ERROR = "AUTHORIZATION_ERROR",
  NOT_FOUND_ERROR = "NOT_FOUND_ERROR",
  RATE_LIMIT_ERROR = "RATE_LIMIT_ERROR",
  INTERNAL_ERROR = "INTERNAL_ERROR",
  DATABASE_ERROR = "DATABASE_ERROR",
  EXTERNAL_API_ERROR = "EXTERNAL_API_ERROR",
}

/**
 * Error severity levels
 */
export enum ErrorSeverity {
  LOW = "LOW",
  MEDIUM = "MEDIUM",
  HIGH = "HIGH",
  CRITICAL = "CRITICAL",
}

/**
 * Structured error interface
 */
interface AppError {
  type: ErrorType;
  message: string;
  severity: ErrorSeverity;
  statusCode: number;
  details?: any;
  cause?: Error;
  metadata?: Record<string, any>;
}

/**
 * Safe error response interface (what gets sent to client)
 */
interface SafeErrorResponse {
  error: string;
  code: string;
  timestamp: string;
  requestId?: string;
  details?: any; // Only included in development
}

/**
 * Create a structured application error
 */
export function createAppError(
  type: ErrorType,
  message: string,
  severity: ErrorSeverity = ErrorSeverity.MEDIUM,
  details?: any,
  cause?: Error
): AppError {
  const statusCode = getStatusCodeForErrorType(type);
  
  return {
    type,
    message,
    severity,
    statusCode,
    details,
    cause,
    metadata: {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
    },
  };
}

/**
 * Get appropriate HTTP status code for error type
 */
function getStatusCodeForErrorType(type: ErrorType): number {
  switch (type) {
    case ErrorType.VALIDATION_ERROR:
      return 400;
    case ErrorType.AUTHENTICATION_ERROR:
      return 401;
    case ErrorType.AUTHORIZATION_ERROR:
      return 403;
    case ErrorType.NOT_FOUND_ERROR:
      return 404;
    case ErrorType.RATE_LIMIT_ERROR:
      return 429;
    case ErrorType.DATABASE_ERROR:
    case ErrorType.INTERNAL_ERROR:
    case ErrorType.EXTERNAL_API_ERROR:
      return 500;
    default:
      return 500;
  }
}

/**
 * Create safe error response for client
 */
function createSafeErrorResponse(
  error: AppError,
  requestId?: string
): SafeErrorResponse {
  const isProduction = process.env.NODE_ENV === "production";
  
  // Generic messages for production to prevent information disclosure
  const safeMessages: Record<ErrorType, string> = {
    [ErrorType.VALIDATION_ERROR]: "Invalid request data",
    [ErrorType.AUTHENTICATION_ERROR]: "Authentication required",
    [ErrorType.AUTHORIZATION_ERROR]: "Insufficient permissions",
    [ErrorType.NOT_FOUND_ERROR]: "Resource not found",
    [ErrorType.RATE_LIMIT_ERROR]: "Too many requests",
    [ErrorType.INTERNAL_ERROR]: "Internal server error",
    [ErrorType.DATABASE_ERROR]: "Service temporarily unavailable",
    [ErrorType.EXTERNAL_API_ERROR]: "External service error",
  };
  
  const response: SafeErrorResponse = {
    error: isProduction ? safeMessages[error.type] : error.message,
    code: error.type,
    timestamp: new Date().toISOString(),
    requestId,
  };
  
  // Only include detailed error information in development
  if (!isProduction && error.details) {
    response.details = error.details;
  }
  
  return response;
}

/**
 * Log error with appropriate level and context
 */
function logError(
  error: AppError,
  context?: {
    userId?: string;
    ip?: string;
    userAgent?: string;
    endpoint?: string;
    requestId?: string;
  }
) {
  const logEntry = {
    timestamp: new Date().toISOString(),
    type: error.type,
    message: error.message,
    severity: error.severity,
    statusCode: error.statusCode,
    context: context || {},
    details: error.details,
    stack: error.cause?.stack,
    metadata: error.metadata,
  };
  
  // Log based on severity
  switch (error.severity) {
    case ErrorSeverity.CRITICAL:
      console.error("🚨 CRITICAL ERROR:", logEntry);
      break;
    case ErrorSeverity.HIGH:
      console.error("❌ HIGH SEVERITY ERROR:", logEntry);
      break;
    case ErrorSeverity.MEDIUM:
      console.warn("⚠️ MEDIUM SEVERITY ERROR:", logEntry);
      break;
    case ErrorSeverity.LOW:
      console.log("ℹ️ LOW SEVERITY ERROR:", logEntry);
      break;
  }
  
  // Log security events for certain error types
  if ([
    ErrorType.AUTHENTICATION_ERROR,
    ErrorType.AUTHORIZATION_ERROR,
    ErrorType.VALIDATION_ERROR,
  ].includes(error.type)) {
    logSecurityEvent({
      type: "AUTH_FAILURE",
      details: error.message,
      userId: context?.userId,
      ip: context?.ip,
      userAgent: context?.userAgent,
    });
  }
}

/**
 * Handle different types of errors and convert them to AppError
 */
export function normalizeError(error: unknown): AppError {
  // Already an AppError
  if (isAppError(error)) {
    return error;
  }
  
  // Zod validation error
  if (error instanceof ZodError) {
    return createAppError(
      ErrorType.VALIDATION_ERROR,
      "Validation failed",
      ErrorSeverity.LOW,
      error.format(),
      error
    );
  }
  
  // Database/Prisma errors
  if (error instanceof Error) {
    // Prisma errors
    if (error.message.includes("Unique constraint")) {
      return createAppError(
        ErrorType.VALIDATION_ERROR,
        "Duplicate entry",
        ErrorSeverity.LOW,
        undefined,
        error
      );
    }
    
    if (error.message.includes("Foreign key constraint")) {
      return createAppError(
        ErrorType.VALIDATION_ERROR,
        "Referenced resource not found",
        ErrorSeverity.LOW,
        undefined,
        error
      );
    }
    
    if (error.message.includes("Connection") || error.message.includes("timeout")) {
      return createAppError(
        ErrorType.DATABASE_ERROR,
        "Database temporarily unavailable",
        ErrorSeverity.HIGH,
        undefined,
        error
      );
    }
    
    // Network/API errors
    if (error.message.includes("fetch") || error.message.includes("network")) {
      return createAppError(
        ErrorType.EXTERNAL_API_ERROR,
        "External service unavailable",
        ErrorSeverity.MEDIUM,
        undefined,
        error
      );
    }
    
    // Generic JavaScript error - sanitize message for security
    const sanitizedMessage = process.env.NODE_ENV === "production" 
      ? "Internal server error" 
      : error.message;
    
    return createAppError(
      ErrorType.INTERNAL_ERROR,
      sanitizedMessage,
      ErrorSeverity.MEDIUM,
      undefined,
      error
    );
  }
  
  // Unknown error type
  return createAppError(
    ErrorType.INTERNAL_ERROR,
    "An unexpected error occurred",
    ErrorSeverity.HIGH,
    { originalError: error }
  );
}

/**
 * Check if error is already an AppError
 */
function isAppError(error: unknown): error is AppError {
  return (
    typeof error === "object" &&
    error !== null &&
    "type" in error &&
    "message" in error &&
    "severity" in error &&
    "statusCode" in error
  );
}

/**
 * Main error handler that processes errors and returns safe responses
 */
export function handleError(
  error: unknown,
  context?: {
    userId?: string;
    ip?: string;
    userAgent?: string;
    endpoint?: string;
    requestId?: string;
  }
): NextResponse {
  const normalizedError = normalizeError(error);
  
  // Log the error
  logError(normalizedError, context);
  
  // Create safe response
  const safeResponse = createSafeErrorResponse(normalizedError, context?.requestId);
  
  return NextResponse.json(safeResponse, { 
    status: normalizedError.statusCode,
    headers: {
      'X-Request-ID': context?.requestId || '',
      'X-Error-Type': normalizedError.type,
    }
  });
}

/**
 * Async error handler wrapper for API routes
 */
export function withErrorHandler(handler: Function) {
  return async (req: Request, context?: any) => {
    try {
      return await handler(req, context);
    } catch (error) {
      const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      return handleError(error, {
        ip: req.headers.get("x-forwarded-for") || "unknown",
        userAgent: req.headers.get("user-agent") || "unknown",
        endpoint: new URL(req.url).pathname,
        requestId,
      });
    }
  };
}

/**
 * Predefined error creators for common scenarios
 */
export const errors = {
  validation: (message: string, details?: any) =>
    createAppError(ErrorType.VALIDATION_ERROR, message, ErrorSeverity.LOW, details),
    
  authentication: (message: string = "Authentication required") =>
    createAppError(ErrorType.AUTHENTICATION_ERROR, message, ErrorSeverity.MEDIUM),
    
  authorization: (message: string = "Insufficient permissions") =>
    createAppError(ErrorType.AUTHORIZATION_ERROR, message, ErrorSeverity.MEDIUM),
    
  notFound: (resource: string = "Resource") =>
    createAppError(ErrorType.NOT_FOUND_ERROR, `${resource} not found`, ErrorSeverity.LOW),
    
  rateLimit: (message: string = "Rate limit exceeded") =>
    createAppError(ErrorType.RATE_LIMIT_ERROR, message, ErrorSeverity.MEDIUM),
    
  internal: (message: string, cause?: Error) =>
    createAppError(ErrorType.INTERNAL_ERROR, message, ErrorSeverity.HIGH, undefined, cause),
    
  database: (message: string, cause?: Error) =>
    createAppError(ErrorType.DATABASE_ERROR, message, ErrorSeverity.HIGH, undefined, cause),
    
  externalApi: (service: string, cause?: Error) =>
    createAppError(
      ErrorType.EXTERNAL_API_ERROR,
      `${service} service unavailable`,
      ErrorSeverity.MEDIUM,
      undefined,
      cause
    ),
};
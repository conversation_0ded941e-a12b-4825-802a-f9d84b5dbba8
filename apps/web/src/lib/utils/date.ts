/**
 * Date Utilities for BuddyChip
 *
 * Comprehensive date handling utilities for billing periods, formatting,
 * and time-based operations across the application.
 */

import {
  addMonths,
  endOfMonth,
  format,
  formatDistance,
  isAfter,
  isBefore,
  startOfMonth,
} from "date-fns";

export const dateUtils = {
  /**
   * Format billing period as YYYY-MM
   */
  formatBillingPeriod: (date: Date): string => {
    return format(date, "yyyy-MM");
  },

  /**
   * Get current billing period
   */
  getCurrentBillingPeriod: (): string => {
    return dateUtils.formatBillingPeriod(startOfMonth(new Date()));
  },

  /**
   * Get billing period start and end dates
   */
  getBillingPeriodRange: (period: string): { start: Date; end: Date } => {
    const [year, month] = period.split("-").map(Number);
    const start = new Date(year, month - 1, 1);
    const end = endOfMonth(start);

    return { start, end };
  },

  /**
   * Check if date is within billing period
   */
  isWithinBillingPeriod: (date: Date, period: string): boolean => {
    const { start, end } = dateUtils.getBillingPeriodRange(period);
    return isAfter(date, start) && isBefore(date, end);
  },

  /**
   * Get next billing period
   */
  getNextBillingPeriod: (period: string): string => {
    const [year, month] = period.split("-").map(Number);
    const currentDate = new Date(year, month - 1, 1);
    const nextMonth = addMonths(currentDate, 1);
    return dateUtils.formatBillingPeriod(nextMonth);
  },

  /**
   * Format relative time (2 hours ago, yesterday, etc.)
   */
  formatRelativeTime: (date: Date): string => {
    return formatDistance(date, new Date(), { addSuffix: true });
  },

  /**
   * Format date for display with multiple options
   */
  formatDate: (
    date: Date,
    formatStr: "short" | "long" | "relative" | "time" = "short"
  ): string => {
    switch (formatStr) {
      case "short":
        return format(date, "MMM d, yyyy");
      case "long":
        return format(date, "MMMM d, yyyy 'at' h:mm a");
      case "relative":
        return dateUtils.formatRelativeTime(date);
      case "time":
        return format(date, "h:mm a");
      default:
        return format(date, "MMM d, yyyy");
    }
  },

  /**
   * Check if date is today
   */
  isToday: (date: Date): boolean => {
    const today = new Date();
    return format(date, "yyyy-MM-dd") === format(today, "yyyy-MM-dd");
  },

  /**
   * Check if date is within last N days
   */
  isWithinLastDays: (date: Date, days: number): boolean => {
    const cutoff = new Date();
    cutoff.setDate(cutoff.getDate() - days);
    return isAfter(date, cutoff);
  },

  /**
   * Get time ago in a human readable format
   */
  getTimeAgo: (date: Date): string => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return "just now";
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400)
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800)
      return `${Math.floor(diffInSeconds / 86400)}d ago`;

    return dateUtils.formatDate(date, "short");
  },

  /**
   * Parse date safely with explicit failure on invalid dates
   */
  parseDate: (dateString: string | Date): Date => {
    if (dateString instanceof Date) return dateString;

    const parsed = new Date(dateString);
    if (isNaN(parsed.getTime())) {
      throw new Error(
        `Invalid date string: "${dateString}". Unable to parse as valid date.`
      );
    }

    return parsed;
  },

  /**
   * Get start and end of day for a given date
   */
  getDayRange: (date: Date): { start: Date; end: Date } => {
    const start = new Date(date);
    start.setHours(0, 0, 0, 0);

    const end = new Date(date);
    end.setHours(23, 59, 59, 999);

    return { start, end };
  },

  /**
   * Check if two dates are the same day
   */
  isSameDay: (date1: Date, date2: Date): boolean => {
    return format(date1, "yyyy-MM-dd") === format(date2, "yyyy-MM-dd");
  },

  /**
   * Get duration between two dates in human readable format
   */
  getDuration: (startDate: Date, endDate: Date): string => {
    return formatDistance(startDate, endDate);
  },

  /**
   * Validate date range
   */
  isValidDateRange: (startDate: Date, endDate: Date): boolean => {
    return (
      isBefore(startDate, endDate) || dateUtils.isSameDay(startDate, endDate)
    );
  },
} as const;

/**
 * Type-safe date formatting options
 */
export type DateFormatOption = "short" | "long" | "relative" | "time";

/**
 * Billing period utilities
 */
export const billingUtils = {
  /**
   * Get all billing periods for a date range
   */
  getBillingPeriodsInRange: (startDate: Date, endDate: Date): string[] => {
    const periods: string[] = [];
    let current = startOfMonth(startDate);
    const end = endOfMonth(endDate);

    while (isBefore(current, end) || dateUtils.isSameDay(current, end)) {
      periods.push(dateUtils.formatBillingPeriod(current));
      current = addMonths(current, 1);
    }

    return periods;
  },

  /**
   * Check if billing period is current
   */
  isCurrentBillingPeriod: (period: string): boolean => {
    return period === dateUtils.getCurrentBillingPeriod();
  },

  /**
   * Get days remaining in billing period
   */
  getDaysRemainingInPeriod: (period: string): number => {
    const { end } = dateUtils.getBillingPeriodRange(period);
    const now = new Date();
    const diffInMs = end.getTime() - now.getTime();
    return Math.ceil(diffInMs / (1000 * 60 * 60 * 24));
  },
} as const;

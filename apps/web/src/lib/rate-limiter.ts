/**
 * Persistent Rate Limiter using Vercel KV
 * 
 * Provides distributed rate limiting that persists across deployments and instances
 */

import { kv } from "@vercel/kv";

/**
 * Rate limit configuration
 */
interface RateLimitConfig {
  window: number; // Time window in seconds
  limit: number;  // Maximum requests allowed in window
  prefix?: string; // Key prefix for organization
}

/**
 * Rate limit result
 */
interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  reset: number; // Unix timestamp when limit resets
  total: number; // Total limit
  windowSeconds: number; // Window duration in seconds
}

/**
 * Rate limit entry stored in KV
 */
interface RateLimitEntry {
  count: number;
  window: number; // Unix timestamp for window start
}

/**
 * Default rate limit configurations
 */
export const RATE_LIMIT_CONFIGS = {
  API_GENERAL: { window: 60, limit: 60, prefix: "api_general" }, // 60 req/min
  API_AUTH: { window: 60, limit: 20, prefix: "api_auth" }, // 20 req/min for auth
  AI_GENERATION: { window: 60, limit: 10, prefix: "ai_gen" }, // 10 AI calls/min
  TELEGRAM_MESSAGE: { window: 60, limit: 30, prefix: "tg_msg" }, // 30 messages/min
  TELEGRAM_COMMAND: { window: 60, limit: 15, prefix: "tg_cmd" }, // 15 commands/min
  SYNC_ENDPOINT: { window: 300, limit: 5, prefix: "sync" }, // 5 sync calls/5min
  FILE_UPLOAD: { window: 300, limit: 10, prefix: "upload" }, // 10 uploads/5min
  PASSWORD_RESET: { window: 3600, limit: 3, prefix: "pwd_reset" }, // 3 attempts/hour
} as const;

/**
 * Create rate limit key
 */
function createRateLimitKey(
  identifier: string,
  config: RateLimitConfig
): string {
  const prefix = config.prefix || "rate_limit";
  // Include window in key for automatic expiration
  const windowStart = Math.floor(Date.now() / 1000 / config.window) * config.window;
  return `${prefix}:${identifier}:${windowStart}`;
}

/**
 * Check and enforce rate limit
 */
export async function checkRateLimit(
  identifier: string,
  config: RateLimitConfig
): Promise<RateLimitResult> {
  const now = Math.floor(Date.now() / 1000);
  const windowStart = Math.floor(now / config.window) * config.window;
  const windowEnd = windowStart + config.window;
  const key = createRateLimitKey(identifier, config);

  try {
    // Get current count for this window
    const current = await kv.get<RateLimitEntry>(key);
    const currentCount = current?.count || 0;

    // Check if limit exceeded
    if (currentCount >= config.limit) {
      return {
        allowed: false,
        remaining: 0,
        reset: windowEnd,
        total: config.limit,
        windowSeconds: config.window,
      };
    }

    // Increment counter
    const newCount = currentCount + 1;
    
    // Store with expiration set to window end + buffer
    await kv.set(
      key,
      { count: newCount, window: windowStart },
      { ex: config.window + 60 } // Extra 60 seconds buffer for cleanup
    );

    return {
      allowed: true,
      remaining: config.limit - newCount,
      reset: windowEnd,
      total: config.limit,
      windowSeconds: config.window,
    };
  } catch (error) {
    // If KV fails, log error but allow request (fail open)
    console.error("Rate limiter KV error:", error);
    
    // Return a permissive response
    return {
      allowed: true,
      remaining: config.limit - 1,
      reset: windowEnd,
      total: config.limit,
      windowSeconds: config.window,
    };
  }
}

/**
 * Advanced sliding window rate limiter
 */
export async function checkSlidingWindowRateLimit(
  identifier: string,
  config: RateLimitConfig
): Promise<RateLimitResult> {
  const now = Math.floor(Date.now() / 1000);
  const windowStart = now - config.window;
  const key = `sliding:${config.prefix || "rate_limit"}:${identifier}`;

  try {
    // Get all timestamps for this identifier
    const pipeline = kv.pipeline();
    
    // Remove expired entries
    pipeline.zremrangebyscore(key, 0, windowStart);
    
    // Count current entries
    pipeline.zcard(key);
    
    // Build the member string once to ensure consistency
    const member = `${now}_${Math.random()}`;
    
    // Add current timestamp
    pipeline.zadd(key, { score: now, member });
    
    // Set expiration
    pipeline.expire(key, config.window + 60);
    
    const results = await pipeline.exec();
    const currentCount = (results[1] as number) || 0;

    // Check if adding this request exceeds limit
    if (currentCount >= config.limit) {
      // Remove the exact request we just added since it's denied
      await kv.zrem(key, member);
      
      return {
        allowed: false,
        remaining: 0,
        reset: now + config.window,
        total: config.limit,
        windowSeconds: config.window,
      };
    }

    return {
      allowed: true,
      remaining: config.limit - currentCount - 1,
      reset: now + config.window,
      total: config.limit,
      windowSeconds: config.window,
    };
  } catch (error) {
    console.error("Sliding window rate limiter error:", error);
    
    // Fail open
    return {
      allowed: true,
      remaining: config.limit - 1,
      reset: now + config.window,
      total: config.limit,
      windowSeconds: config.window,
    };
  }
}

/**
 * Rate limit middleware for API routes
 */
export function withRateLimit(
  config: RateLimitConfig,
  getIdentifier?: (req: Request) => string
) {
  return function rateLimitMiddleware(handler: Function) {
    return async (req: Request, context?: any) => {
      // Default identifier: IP address
      const identifier = getIdentifier 
        ? getIdentifier(req)
        : req.headers.get("x-forwarded-for")?.split(",")[0]?.trim() || 
          req.headers.get("x-real-ip") || 
          "unknown";

      const result = await checkRateLimit(identifier, config);

      if (!result.allowed) {
        // Log rate limit exceeded event
        try {
          const { securityLogger } = await import("./security-monitor");
          const url = new URL(req.url);
          
          await securityLogger.rateLimitExceeded(
            identifier,
            req.headers.get("x-forwarded-for")?.split(",")[0]?.trim() || "unknown",
            url.pathname
          );
        } catch (error) {
          console.error("Failed to log rate limit event:", error);
        }

        // Add rate limit headers
        const headers = new Headers({
          "X-RateLimit-Limit": config.limit.toString(),
          "X-RateLimit-Remaining": "0",
          "X-RateLimit-Reset": result.reset.toString(),
          "Retry-After": result.windowSeconds.toString(),
        });

        return new Response(
          JSON.stringify({
            error: "Rate limit exceeded",
            code: "RATE_LIMIT_EXCEEDED",
            retryAfter: result.windowSeconds,
            limit: config.limit,
            window: config.window,
          }),
          {
            status: 429,
            headers: {
              "Content-Type": "application/json",
              ...Object.fromEntries(headers.entries()),
            },
          }
        );
      }

      // Add rate limit info headers to response
      const response = await handler(req, context);
      
      if (response instanceof Response) {
        response.headers.set("X-RateLimit-Limit", config.limit.toString());
        response.headers.set("X-RateLimit-Remaining", result.remaining.toString());
        response.headers.set("X-RateLimit-Reset", result.reset.toString());
      }

      return response;
    };
  };
}

/**
 * User-specific rate limiting (uses user ID as identifier)
 */
export function withUserRateLimit(config: RateLimitConfig) {
  return withRateLimit(config, (req) => {
    // Extract user ID from auth headers, session, etc.
    const authHeader = req.headers.get("authorization");
    if (authHeader?.startsWith("Bearer ")) {
      // This would need to be implemented based on your auth system
      return authHeader.slice(7); // Remove "Bearer " prefix
    }
    
    // Fallback to IP
    return req.headers.get("x-forwarded-for")?.split(",")[0]?.trim() || 
           req.headers.get("x-real-ip") || 
           "unknown";
  });
}

/**
 * Get current rate limit status without incrementing
 */
export async function getRateLimitStatus(
  identifier: string,
  config: RateLimitConfig
): Promise<RateLimitResult> {
  const now = Math.floor(Date.now() / 1000);
  const windowStart = Math.floor(now / config.window) * config.window;
  const windowEnd = windowStart + config.window;
  const key = createRateLimitKey(identifier, config);

  try {
    const current = await kv.get<RateLimitEntry>(key);
    const currentCount = current?.count || 0;

    return {
      allowed: currentCount < config.limit,
      remaining: Math.max(0, config.limit - currentCount),
      reset: windowEnd,
      total: config.limit,
      windowSeconds: config.window,
    };
  } catch (error) {
    console.error("Rate limit status check error:", error);
    
    return {
      allowed: true,
      remaining: config.limit,
      reset: windowEnd,
      total: config.limit,
      windowSeconds: config.window,
    };
  }
}

/**
 * Reset rate limit for identifier (admin function)
 */
export async function resetRateLimit(
  identifier: string,
  config: RateLimitConfig
): Promise<void> {
  const key = createRateLimitKey(identifier, config);
  
  try {
    await kv.del(key);
  } catch (error) {
    console.error("Rate limit reset error:", error);
  }
}

/**
 * Get rate limit statistics
 */
export async function getRateLimitStats(): Promise<{
  totalKeys: number;
  keysByPrefix: Record<string, number>;
}> {
  try {
    // This would require scanning KV keys, which may not be available
    // in all KV implementations. This is a placeholder for monitoring.
    
    return {
      totalKeys: 0,
      keysByPrefix: {},
    };
  } catch (error) {
    console.error("Rate limit stats error:", error);
    
    return {
      totalKeys: 0,
      keysByPrefix: {},
    };
  }
}

/**
 * Helper functions for common scenarios
 */
export const rateLimiters = {
  // General API rate limiting
  api: withRateLimit(RATE_LIMIT_CONFIGS.API_GENERAL),
  
  // Authentication endpoints
  auth: withRateLimit(RATE_LIMIT_CONFIGS.API_AUTH),
  
  // AI generation endpoints  
  ai: withUserRateLimit(RATE_LIMIT_CONFIGS.AI_GENERATION),
  
  // Telegram bot endpoints
  telegram: withRateLimit(RATE_LIMIT_CONFIGS.TELEGRAM_MESSAGE),
  
  // File upload endpoints
  upload: withUserRateLimit(RATE_LIMIT_CONFIGS.FILE_UPLOAD),
  
  // Sync endpoints (very restrictive)
  sync: withRateLimit(RATE_LIMIT_CONFIGS.SYNC_ENDPOINT),
  
  // Password reset (very restrictive)
  passwordReset: withRateLimit(RATE_LIMIT_CONFIGS.PASSWORD_RESET),
};
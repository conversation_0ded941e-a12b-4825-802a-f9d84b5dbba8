/**
 * Security Monitoring and Audit Logging System
 * 
 * Provides comprehensive security event logging, intrusion detection,
 * and integration with Sentry for centralized security monitoring
 */

import * as Sentry from "@sentry/nextjs";
import { kv } from "@vercel/kv";
import type { SecurityEvent } from "./security-utils";

/**
 * Extended security event types
 */
export interface SecurityAuditEvent extends SecurityEvent {
  id: string;
  timestamp: string;
  sessionId?: string;
  requestId?: string;
  endpoint?: string;
  method?: string;
  statusCode?: number;
  responseTime?: number;
  severity: "low" | "medium" | "high" | "critical";
  category: "auth" | "input" | "access" | "system" | "business";
  metadata?: Record<string, any>;
}

/**
 * Security threat patterns for detection
 */
const THREAT_PATTERNS = {
  // Authentication threats
  BRUTE_FORCE: {
    timeWindow: 300, // 5 minutes
    threshold: 5,
    events: ["AUTH_FAILURE"] as const,
    severity: "high" as const,
  },
  
  // Input validation threats  
  INJECTION_ATTEMPT: {
    timeWindow: 60,
    threshold: 3,
    events: ["INVALID_INPUT"] as const,
    severity: "medium" as const,
  },
  
  // Access control threats
  UNAUTHORIZED_ACCESS: {
    timeWindow: 60,
    threshold: 5,
    events: ["UNAUTHORIZED_ACCESS"] as const,
    severity: "high" as const,
  },
  
  // Rate limiting threats
  RATE_LIMIT_ABUSE: {
    timeWindow: 300,
    threshold: 10,
    events: ["RATE_LIMIT_EXCEEDED"] as const,
    severity: "medium" as const,
  },
  
  // Suspicious behavior
  UNUSUAL_ACTIVITY: {
    timeWindow: 3600, // 1 hour
    threshold: 50,
    events: ["AUTH_FAILURE", "INVALID_INPUT", "UNAUTHORIZED_ACCESS"] as const,
    severity: "critical" as const,
  },
} as const;

/**
 * Security alert configuration
 */
interface SecurityAlert {
  type: keyof typeof THREAT_PATTERNS;
  severity: "low" | "medium" | "high" | "critical";
  message: string;
  events: SecurityAuditEvent[];
  threshold: number;
  timeWindow: number;
  identifier: string;
}

/**
 * Generate unique event ID
 */
function generateEventId(): string {
  return `sec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Enhanced security event logger with Sentry integration
 */
export async function logSecurityAuditEvent(
  event: Omit<SecurityAuditEvent, "id" | "timestamp">
): Promise<void> {
  const auditEvent: SecurityAuditEvent = {
    ...event,
    id: generateEventId(),
    timestamp: new Date().toISOString(),
  };

  try {
    // Log to console in development
    if (process.env.NODE_ENV === "development") {
      console.log("🔒 SECURITY AUDIT:", auditEvent);
    }

    // Store in KV for analysis (with 30 day expiration)
    const storageKey = `security_audit:${auditEvent.timestamp.split('T')[0]}:${auditEvent.id}`;
    await kv.set(storageKey, auditEvent, { ex: 30 * 24 * 60 * 60 });

    // Send to Sentry based on severity
    if (auditEvent.severity === "high" || auditEvent.severity === "critical") {
      Sentry.captureMessage(`Security Event: ${auditEvent.type}`, {
        level: auditEvent.severity === "critical" ? "fatal" : "error",
        tags: {
          security_event: auditEvent.type,
          severity: auditEvent.severity,
          category: auditEvent.category,
          user_id: auditEvent.userId,
          ip: auditEvent.ip,
        },
        extra: {
          event_id: auditEvent.id,
          endpoint: auditEvent.endpoint,
          method: auditEvent.method,
          status_code: auditEvent.statusCode,
          response_time: auditEvent.responseTime,
          user_agent: auditEvent.userAgent,
          details: auditEvent.details,
          metadata: auditEvent.metadata,
        },
        fingerprint: [auditEvent.type, auditEvent.userId || auditEvent.ip || "unknown"],
      });
    }

    // Check for threat patterns
    await detectSecurityThreats(auditEvent);

  } catch (error) {
    // Don't let security logging break the application
    console.error("Security audit logging failed:", error);
    
    // Still report the logging failure to Sentry
    Sentry.captureException(error, {
      tags: { section: "security_audit_logging" },
      extra: { original_event: auditEvent },
    });
  }
}

/**
 * Detect security threat patterns using sliding window analysis
 */
async function detectSecurityThreats(event: SecurityAuditEvent): Promise<void> {
  const identifier = event.userId || event.ip || "unknown";
  
  for (const [patternType, pattern] of Object.entries(THREAT_PATTERNS)) {
    if (!(pattern.events as readonly string[]).includes(event.type)) continue;

    try {
      const detectionKey = `threat_detection:${patternType}:${identifier}`;
      const now = Math.floor(Date.now() / 1000);
      const windowStart = now - pattern.timeWindow;

      // Use Redis sorted set to track events in time window
      await kv.zadd(detectionKey, { score: now, member: event.id });
      
      // Remove events outside the window
      await kv.zremrangebyscore(detectionKey, 0, windowStart);
      
      // Count events in current window
      const eventCount = await kv.zcard(detectionKey);
      
      // Set expiration for cleanup
      await kv.expire(detectionKey, pattern.timeWindow + 60);

      // Trigger alert if threshold exceeded
      if (eventCount >= pattern.threshold) {
        await triggerSecurityAlert({
          type: patternType as keyof typeof THREAT_PATTERNS,
          severity: pattern.severity,
          message: `Detected ${patternType}: ${eventCount} events in ${pattern.timeWindow}s`,
          events: [event], // In production, fetch recent events from KV
          threshold: pattern.threshold,
          timeWindow: pattern.timeWindow,
          identifier,
        });
      }

    } catch (error) {
      console.error(`Threat detection failed for ${patternType}:`, error);
    }
  }
}

/**
 * Trigger security alert
 */
async function triggerSecurityAlert(alert: SecurityAlert): Promise<void> {
  try {
    // Log the alert
    console.warn("🚨 SECURITY ALERT:", alert);

    // Send critical alert to Sentry
    Sentry.captureMessage(`Security Alert: ${alert.type}`, {
      level: alert.severity === "critical" ? "fatal" : "error",
      tags: {
        security_alert: alert.type,
        severity: alert.severity,
        identifier: alert.identifier,
      },
      extra: {
        message: alert.message,
        threshold: alert.threshold,
        time_window: alert.timeWindow,
        event_count: alert.events.length,
        events: alert.events.map(e => ({
          id: e.id,
          timestamp: e.timestamp,
          type: e.type,
          endpoint: e.endpoint,
        })),
      },
      fingerprint: [alert.type, alert.identifier],
    });

    // Store alert for dashboard/reporting
    const alertKey = `security_alert:${Date.now()}:${alert.identifier}`;
    await kv.set(alertKey, {
      ...alert,
      triggered_at: new Date().toISOString(),
    }, { ex: 7 * 24 * 60 * 60 }); // 7 day retention

    // In production, you might also:
    // - Send to Slack/Discord webhook
    // - Email security team
    // - Trigger automated response (IP blocking, etc.)
    
  } catch (error) {
    console.error("Failed to trigger security alert:", error);
  }
}

/**
 * Security middleware for API routes
 */
export function withSecurityMonitoring(
  options: {
    category?: SecurityAuditEvent["category"];
    severity?: SecurityAuditEvent["severity"];
    endpoint?: string;
  } = {}
) {
  return function securityMiddleware(handler: Function) {
    return async (req: Request, context?: any) => {
      const startTime = Date.now();
      const requestId = generateEventId();
      
      // Extract request metadata
      const ip = req.headers.get("x-forwarded-for")?.split(",")[0]?.trim() || 
                req.headers.get("x-real-ip") || "unknown";
      const userAgent = req.headers.get("user-agent") || "unknown";
      const method = req.method;
      const url = new URL(req.url);
      const endpoint = options.endpoint || url.pathname;

      let response: Response;
      let statusCode = 200;

      try {
        // Call the handler
        response = await handler(req, context);
        statusCode = response.status;

        // Log successful requests only if they're sensitive operations
        if (options.category === "auth" || options.category === "business") {
          await logSecurityAuditEvent({
            type: "UNAUTHORIZED_ACCESS", // Will be overridden by specific handlers
            category: options.category || "system",
            severity: options.severity || "low",
            ip,
            userAgent,
            endpoint,
            method,
            statusCode,
            responseTime: Date.now() - startTime,
            requestId,
            details: `${method} ${endpoint} - ${statusCode}`,
          });
        }

        return response;

      } catch (error) {
        statusCode = 500;
        
        // Log system errors
        await logSecurityAuditEvent({
          type: "AUTH_FAILURE", // Generic type for system errors
          category: "system",
          severity: "medium",
          ip,
          userAgent,
          endpoint,
          method,
          statusCode,
          responseTime: Date.now() - startTime,
          requestId,
          details: error instanceof Error ? error.message : "Unknown system error",
        });

        throw error;
      }
    };
  };
}

/**
 * Log specific security events with context
 */
export const securityLogger = {
  // Authentication events
  loginFailure: (userId: string, ip: string, reason: string) =>
    logSecurityAuditEvent({
      type: "AUTH_FAILURE",
      category: "auth",
      severity: "medium",
      userId,
      ip,
      details: `Login failure: ${reason}`,
    }),

  loginSuccess: (userId: string, ip: string) =>
    logSecurityAuditEvent({
      type: "AUTH_FAILURE", // Using existing type, but with success context
      category: "auth",
      severity: "low",
      userId,
      ip,
      details: "Successful login",
    }),

  // Input validation events
  invalidInput: (ip: string, endpoint: string, details: string) =>
    logSecurityAuditEvent({
      type: "INVALID_INPUT",
      category: "input",
      severity: "low",
      ip,
      endpoint,
      details: `Invalid input: ${details}`,
    }),

  // Access control events
  unauthorizedAccess: (userId: string | undefined, ip: string, endpoint: string, reason: string) =>
    logSecurityAuditEvent({
      type: "UNAUTHORIZED_ACCESS",
      category: "access",
      severity: "high",
      userId,
      ip,
      endpoint,
      details: `Unauthorized access: ${reason}`,
    }),

  // Rate limiting events
  rateLimitExceeded: (identifier: string, ip: string, endpoint: string) =>
    logSecurityAuditEvent({
      type: "RATE_LIMIT_EXCEEDED",
      category: "access",
      severity: "medium",
      ip,
      endpoint,
      details: `Rate limit exceeded for ${identifier}`,
    }),

  // Business logic events
  suspiciousActivity: (userId: string | undefined, ip: string, activity: string, metadata?: Record<string, any>) =>
    logSecurityAuditEvent({
      type: "UNAUTHORIZED_ACCESS", // Using as generic suspicious activity
      category: "business",
      severity: "high",
      userId,
      ip,
      details: `Suspicious activity: ${activity}`,
      metadata,
    }),
};

/**
 * Get security dashboard data
 */
export async function getSecurityDashboard(timeframe: "24h" | "7d" | "30d" = "24h"): Promise<{
  eventCounts: Record<string, number>;
  topThreats: Array<{ type: string; count: number }>;
  alertSummary: Record<string, number>;
  recentEvents: SecurityAuditEvent[];
}> {
  try {
    const hours = timeframe === "24h" ? 24 : timeframe === "7d" ? 168 : 720;
    const since = new Date(Date.now() - (hours * 60 * 60 * 1000)).toISOString();

    // This would require a more sophisticated KV scan implementation
    // For now, return placeholder data
    return {
      eventCounts: {
        AUTH_FAILURE: 0,
        INVALID_INPUT: 0,
        UNAUTHORIZED_ACCESS: 0,
        RATE_LIMIT_EXCEEDED: 0,
      },
      topThreats: [],
      alertSummary: {
        low: 0,
        medium: 0,
        high: 0,
        critical: 0,
      },
      recentEvents: [],
    };

  } catch (error) {
    console.error("Failed to get security dashboard data:", error);
    throw error;
  }
}

/**
 * Security health check
 */
export async function securityHealthCheck(): Promise<{
  status: "healthy" | "warning" | "critical";
  checks: Array<{ name: string; status: boolean; message?: string }>;
  lastCheck: string;
}> {
  const checks = [
    {
      name: "KV Storage Connection",
      status: true, // Would test KV connection
    },
    {
      name: "Sentry Integration", 
      status: true, // Would test Sentry connection
    },
    {
      name: "Security Event Processing",
      status: true, // Would check recent event processing
    },
    {
      name: "Threat Detection Active",
      status: true, // Would verify threat detection is running
    },
  ];

  const allHealthy = checks.every(check => check.status);
  const hasWarnings = checks.some(check => !check.status);

  return {
    status: allHealthy ? "healthy" : hasWarnings ? "warning" : "critical",
    checks,
    lastCheck: new Date().toISOString(),
  };
}
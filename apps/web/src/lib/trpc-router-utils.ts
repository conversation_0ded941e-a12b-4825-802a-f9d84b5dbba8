/**
 * tRPC Router Utilities
 *
 * Helper functions and patterns for creating consistent, maintainable routers
 * with standardized middleware, error handling, and validation.
 */

import { TRPCError } from "@trpc/server";
import type { FeatureType } from "../../prisma/generated/index.js";
import {
  createFeatureProcedure,
  createMonitoredProcedure,
  handleTRPCError,
} from "./trpc-middleware";
import {
  commonInputSchemas,
  responseSchemas,
  schemaUtils,
} from "./trpc-schemas";

/**
 * Common router patterns and builders
 */
export const routerPatterns = {
  /**
   * Standard CRUD operations for entities
   */
  createCRUDProcedures: <T extends Record<string, any>>(
    entityName: string,
    feature: FeatureType,
    options: {
      createSchema?: any;
      updateSchema?: any;
      listSchema?: any;
      getSchema?: any;
      deleteSchema?: any;
    } = {}
  ) => {
    const {
      createSchema = commonInputSchemas.id,
      updateSchema = commonInputSchemas.id,
      listSchema = commonInputSchemas.pagination,
      getSchema = commonInputSchemas.cuid,
      deleteSchema = commonInputSchemas.cuid,
    } = options;

    return {
      // Create entity
      create: createFeatureProcedure(feature, {
        operationName: `${entityName}.create`,
        requestedAmount: 1,
      })
        .input(createSchema)
        .mutation(async ({ input, ctx }) => {
          try {
            // Implementation would be provided by the specific router
            throw new Error("Create implementation must be provided");
          } catch (error) {
            handleTRPCError(error, `create ${entityName}`, { input });
          }
        }),

      // List entities with pagination
      list: createMonitoredProcedure(`${entityName}.list`)
        .input(listSchema)
        .query(async ({ input, ctx }) => {
          try {
            // Implementation would be provided by the specific router
            throw new Error("List implementation must be provided");
          } catch (error) {
            handleTRPCError(error, `list ${entityName}`, { input });
          }
        }),

      // Get single entity
      get: createMonitoredProcedure(`${entityName}.get`)
        .input(getSchema)
        .query(async ({ input, ctx }) => {
          try {
            // Implementation would be provided by the specific router
            throw new Error("Get implementation must be provided");
          } catch (error) {
            handleTRPCError(error, `get ${entityName}`, { input });
          }
        }),

      // Update entity
      update: createFeatureProcedure(feature, {
        operationName: `${entityName}.update`,
        requestedAmount: 1,
      })
        .input(updateSchema)
        .mutation(async ({ input, ctx }) => {
          try {
            // Implementation would be provided by the specific router
            throw new Error("Update implementation must be provided");
          } catch (error) {
            handleTRPCError(error, `update ${entityName}`, { input });
          }
        }),

      // Delete entity
      delete: createFeatureProcedure(feature, {
        operationName: `${entityName}.delete`,
        requestedAmount: 1,
      })
        .input(deleteSchema)
        .mutation(async ({ input, ctx }) => {
          try {
            // Implementation would be provided by the specific router
            throw new Error("Delete implementation must be provided");
          } catch (error) {
            handleTRPCError(error, `delete ${entityName}`, { input });
          }
        }),
    };
  },

  /**
   * Standard pagination query pattern
   */
  createPaginatedQuery: (
    operationName: string,
    inputSchema: any,
    queryBuilder: (
      input: any,
      ctx: any
    ) => Promise<{ items: any[]; total?: number; nextCursor?: string }>
  ) => {
    return createMonitoredProcedure(operationName)
      .input(schemaUtils.withPagination(inputSchema))
      .query(async ({ input, ctx }) => {
        try {
          const result = await queryBuilder(input, ctx);

          return {
            success: true,
            data: result.items,
            pagination: {
              nextCursor: result.nextCursor,
              hasNextPage: !!result.nextCursor,
              totalCount: result.total,
              limit: input.limit,
            },
          };
        } catch (error) {
          handleTRPCError(error, operationName, { input });
        }
      });
  },

  /**
   * Standard feature-gated mutation pattern
   */
  createFeatureMutation: (
    operationName: string,
    feature: FeatureType,
    inputSchema: any,
    mutationHandler: (input: any, ctx: any) => Promise<any>,
    options: {
      requestedAmount?: number;
      requireFeatureAccess?: boolean;
    } = {}
  ) => {
    return createFeatureProcedure(feature, {
      operationName,
      ...options,
    })
      .input(inputSchema)
      .mutation(async ({ input, ctx }) => {
        try {
          const result = await mutationHandler(input, ctx);
          return {
            success: true,
            data: result,
          };
        } catch (error) {
          handleTRPCError(error, operationName, { input });
        }
      });
  },

  /**
   * Standard query pattern with monitoring
   */
  createMonitoredQuery: (
    operationName: string,
    inputSchema: any,
    queryHandler: (input: any, ctx: any) => Promise<any>
  ) => {
    return createMonitoredProcedure(operationName)
      .input(inputSchema)
      .query(async ({ input, ctx }) => {
        try {
          const result = await queryHandler(input, ctx);
          return {
            success: true,
            data: result,
          };
        } catch (error) {
          handleTRPCError(error, operationName, { input });
        }
      });
  },
};

/**
 * Validation utilities for common patterns
 */
export const validationUtils = {
  /**
   * Validate entity ownership
   */
  validateOwnership: async (
    prisma: any,
    entityType: string,
    entityId: string,
    userId: string,
    customWhere?: Record<string, any>
  ) => {
    const where = {
      id: entityId,
      userId,
      ...customWhere,
    };

    const entity = await prisma[entityType].findFirst({ where });

    if (!entity) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: `${entityType} not found or access denied`,
      });
    }

    return entity;
  },

  /**
   * Validate and sanitize input
   */
  sanitizeInput: <T extends object>(
    input: T,
    sanitizers: Record<keyof T, (value: any) => any>
  ): T => {
    const sanitized = { ...input };

    for (const [key, sanitizer] of Object.entries(sanitizers)) {
      if (key in sanitized && typeof sanitizer === "function") {
        sanitized[key as keyof T] = sanitizer(sanitized[key as keyof T]);
      }
    }

    return sanitized;
  },

  /**
   * Validate pagination parameters
   */
  validatePagination: (input: {
    limit?: number;
    cursor?: string;
    offset?: number;
  }) => {
    const { limit = 10, cursor, offset = 0 } = input;

    if (limit < 1 || limit > 100) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "Limit must be between 1 and 100",
      });
    }

    if (offset < 0) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "Offset must be non-negative",
      });
    }

    return { limit, cursor, offset };
  },
};

/**
 * Response formatting utilities
 */
export const responseUtils = {
  /**
   * Format success response
   */
  success: <T>(data: T, message?: string, metadata?: Record<string, any>) => ({
    success: true as const,
    data,
    ...(message && { message }),
    ...(metadata && { metadata }),
  }),

  /**
   * Format paginated response
   */
  paginated: <T>(
    items: T[],
    pagination: {
      nextCursor?: string;
      hasNextPage: boolean;
      totalCount?: number;
      limit: number;
    }
  ) => ({
    success: true as const,
    data: items,
    pagination,
  }),

  /**
   * Format error response (for manual error handling)
   */
  error: (message: string, code?: string, details?: Record<string, any>) => ({
    success: false as const,
    error: message,
    ...(code && { code }),
    ...(details && { details }),
  }),
};

/**
 * Migration helpers for updating existing routers
 */
export const migrationHelpers = {
  /**
   * Convert old procedure to new middleware pattern
   */
  convertProcedure: (
    oldProcedure: any,
    newProcedureBuilder: any,
    operationName: string
  ) => {
    // This would help automate the conversion process
    // Implementation would depend on the specific patterns being migrated
    console.log(`Converting procedure: ${operationName}`);
    return newProcedureBuilder;
  },

  /**
   * Validate router migration
   */
  validateMigration: (oldRouter: any, newRouter: any) => {
    // Compare procedure names and ensure all are migrated
    const oldProcedures = Object.keys(oldRouter._def.procedures || {});
    const newProcedures = Object.keys(newRouter._def.procedures || {});

    const missing = oldProcedures.filter(
      (name) => !newProcedures.includes(name)
    );
    const added = newProcedures.filter((name) => !oldProcedures.includes(name));

    return {
      missing,
      added,
      isComplete: missing.length === 0,
    };
  },
};

/**
 * Security Utilities for BuddyChip
 *
 * Provides input sanitization, validation, and security helpers
 */

import validator from "validator";

/**
 * Sanitize user input to prevent XSS and injection attacks
 */
export function sanitizeInput(input: string): string {
  if (!input || typeof input !== "string") {
    return "";
  }

  // Remove potentially dangerous characters and scripts
  let sanitized = input
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, "") // Remove script tags
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, "") // Remove iframe tags
    .replace(/javascript:/gi, "") // Remove javascript: protocols
    .replace(/on\w+\s*=/gi, "") // Remove event handlers
    .replace(/data:/gi, "") // Remove data: protocols
    .trim();

  // Limit length to prevent DoS
  if (sanitized.length > 10000) {
    sanitized = sanitized.substring(0, 10000);
  }

  return sanitized;
}

/**
 * Sanitize and validate Twitter handle
 */
export function sanitizeTwitterHandle(handle: string): string {
  if (!handle || typeof handle !== "string") {
    throw new Error("Invalid Twitter handle");
  }

  // Remove @ symbol and sanitize
  const cleaned = handle.replace("@", "").trim();

  // Validate format (1-15 chars, alphanumeric and underscores only)
  if (!/^[a-zA-Z0-9_]{1,15}$/.test(cleaned)) {
    throw new Error(
      "Invalid Twitter handle format. Must be 1-15 characters, alphanumeric and underscores only."
    );
  }

  return cleaned;
}

/**
 * Sanitize URL and validate it's a Twitter URL
 */
export function sanitizeTwitterUrl(url: string): string {
  if (!url || typeof url !== "string") {
    throw new Error("Invalid URL");
  }

  // Basic URL validation
  if (!validator.isURL(url, { protocols: ["http", "https"] })) {
    throw new Error("Invalid URL format");
  }

  // Check if it's a Twitter/X URL
  const twitterDomains = [
    "twitter.com",
    "x.com",
    "mobile.twitter.com",
    "m.twitter.com",
  ];
  const urlObj = new URL(url);

  if (!twitterDomains.includes(urlObj.hostname)) {
    throw new Error("URL must be from Twitter or X");
  }

  return url;
}

/**
 * Sanitize system prompt for AI
 */
export function sanitizeSystemPrompt(prompt: string): string {
  if (!prompt || typeof prompt !== "string") {
    return "";
  }

  // Remove potentially dangerous instructions
  let sanitized = prompt
    .replace(/ignore\s+previous\s+instructions/gi, "")
    .replace(/forget\s+everything/gi, "")
    .replace(/system\s*:/gi, "")
    .replace(/assistant\s*:/gi, "")
    .replace(/user\s*:/gi, "")
    .trim();

  // Limit length
  if (sanitized.length > 2000) {
    sanitized = sanitized.substring(0, 2000);
  }

  return sanitizeInput(sanitized);
}

/**
 * Validate and sanitize user name
 */
export function sanitizeUserName(name: string): string {
  if (!name || typeof name !== "string") {
    return "";
  }

  let sanitized = sanitizeInput(name);

  // Additional validation for names
  if (sanitized.length > 100) {
    sanitized = sanitized.substring(0, 100);
  }

  // Remove excessive whitespace
  sanitized = sanitized.replace(/\s+/g, " ").trim();

  return sanitized;
}

/**
 * Validate email format (additional to Clerk validation)
 */
export function validateEmail(email: string): boolean {
  if (!email || typeof email !== "string") {
    return false;
  }

  return validator.isEmail(email) && email.length <= 254;
}

/**
 * Rate limiting key generator
 */
export function generateRateLimitKey(userId: string, action: string): string {
  return `rate_limit:${userId}:${action}`;
}

/**
 * Validate CUID format (for database IDs)
 */
export function validateCUID(id: string): boolean {
  if (!id || typeof id !== "string") {
    return false;
  }

  // CUID format: c + timestamp + counter + fingerprint + random
  return /^c[a-z0-9]{24}$/.test(id);
}

/**
 * Sanitize metadata object
 */
export function sanitizeMetadata(
  metadata: Record<string, any>
): Record<string, any> {
  if (!metadata || typeof metadata !== "object") {
    return {};
  }

  const sanitized: Record<string, any> = {};

  for (const [key, value] of Object.entries(metadata)) {
    // Sanitize key
    const cleanKey = sanitizeInput(key);
    if (cleanKey.length === 0 || cleanKey.length > 50) {
      continue;
    }

    // Sanitize value based on type
    if (typeof value === "string") {
      sanitized[cleanKey] = sanitizeInput(value);
    } else if (typeof value === "number" && isFinite(value)) {
      sanitized[cleanKey] = value;
    } else if (typeof value === "boolean") {
      sanitized[cleanKey] = value;
    }
    // Skip other types for security
  }

  return sanitized;
}

/**
 * Check if request is from allowed origin
 */
export function validateOrigin(
  origin: string | null,
  allowedOrigins: string[]
): boolean {
  if (!origin) {
    return false;
  }

  return allowedOrigins.includes(origin) || allowedOrigins.includes("*");
}

/**
 * Generate secure random string for API keys
 * Uses cryptographically secure random bytes instead of Math.random()
 */
export function generateSecureToken(length: number = 32): string {
  const bytes = Math.ceil(length / 2);

  // Prefer Web Crypto (available in browsers and Edge runtime)
  try {
    if (typeof globalThis !== 'undefined' && (globalThis as any).crypto?.getRandomValues) {
      const array = new Uint8Array(bytes);
      (globalThis as any).crypto.getRandomValues(array);
      const hex = Array.from(array, (b) => b.toString(16).padStart(2, "0")).join("");
      if (process.env.NODE_ENV !== "production") {
        console.log("🔐 generateSecureToken: using Web Crypto (Edge/browser)");
      }
      return hex.slice(0, length);
    }
  } catch (err) {
    if (process.env.NODE_ENV !== "production") {
      console.warn("🔐 generateSecureToken: Web Crypto path failed, falling back to Node crypto", err);
    }
  }

  // Fallback to Node.js crypto for traditional server runtimes
  try {
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const { randomBytes } = require("crypto");
    if (process.env.NODE_ENV !== "production") {
      console.log("🔐 generateSecureToken: using Node crypto fallback");
    }
    return randomBytes(bytes).toString("hex").slice(0, length);
  } catch (_) {
    // Last-resort, non-cryptographic fallback to avoid hard crashes
    if (process.env.NODE_ENV !== "production") {
      console.warn("🔐 generateSecureToken: No crypto available, using weak fallback (NOT SECURE)");
    }
    const chars = "abcdef0123456789";
    let out = "";
    for (let i = 0; i < length; i++) out += chars[Math.floor(Math.random() * chars.length)];
    return out;
  }
}

/**
 * Validate API key format
 */
export function validateApiKey(key: string): boolean {
  if (!key || typeof key !== "string") {
    return false;
  }

  // API keys should be at least 32 characters and alphanumeric
  return /^[a-zA-Z0-9]{32,}$/.test(key);
}

/**
 * Generate CSRF token
 */
export function generateCSRFToken(): string {
  return generateSecureToken(32);
}

/**
 * Validate CSRF token
 */
export function validateCSRFToken(token: string, expectedToken: string): boolean {
  if (!token || !expectedToken) {
    return false;
  }
  
  // Use constant-time comparison to prevent timing attacks
  if (token.length !== expectedToken.length) {
    return false;
  }
  
  let result = 0;
  for (let i = 0; i < token.length; i++) {
    result |= token.charCodeAt(i) ^ expectedToken.charCodeAt(i);
  }
  
  return result === 0;
}

/**
 * Create CSRF token with timestamp (for expiration)
 */
export function createCSRFTokenWithExpiry(expiryMinutes: number = 60): {
  token: string;
  expires: number;
  combined: string;
} {
  const token = generateCSRFToken();
  const expires = Date.now() + (expiryMinutes * 60 * 1000);
  const combined = `${token}:${expires}`;
  
  // Use universal base64 encoding (browser and Node.js compatible)
  const base64Combined = typeof globalThis.Buffer !== 'undefined'
    ? Buffer.from(combined).toString('base64')
    : btoa(unescape(encodeURIComponent(combined)));
  
  return {
    token,
    expires,
    combined: base64Combined,
  };
}

/**
 * Validate CSRF token with expiration
 */
export function validateCSRFTokenWithExpiry(combinedToken: string): {
  isValid: boolean;
  isExpired: boolean;
  error?: string;
} {
  try {
    // Use universal base64 decoding (browser and Node.js compatible)
    const decoded = typeof window !== 'undefined' && typeof window.atob === 'function'
      ? atob(combinedToken)
      : Buffer.from(combinedToken, 'base64').toString();
    const [token, expiresStr] = decoded.split(':');
    
    if (!token || !expiresStr) {
      return { isValid: false, isExpired: false, error: 'Invalid token format' };
    }
    
    const expires = parseInt(expiresStr, 10);
    const now = Date.now();
    
    if (now > expires) {
      return { isValid: false, isExpired: true, error: 'Token expired' };
    }
    
    return { isValid: true, isExpired: false };
  } catch (error) {
    return { isValid: false, isExpired: false, error: 'Token validation failed' };
  }
}

/**
 * Log security events (for audit trail)
 * @deprecated Use logSecurityAuditEvent from security-monitor.ts instead
 */
export function logSecurityEvent(event: {
  type:
    | "AUTH_FAILURE"
    | "INVALID_INPUT"
    | "RATE_LIMIT_EXCEEDED"
    | "UNAUTHORIZED_ACCESS";
  userId?: string;
  ip?: string;
  userAgent?: string;
  details?: string;
}) {
  // Import dynamically to avoid circular dependencies
  import("./security-monitor").then(({ logSecurityAuditEvent }) => {
    logSecurityAuditEvent({
      type: event.type,
      category: event.type === "AUTH_FAILURE" ? "auth" : 
               event.type === "INVALID_INPUT" ? "input" :
               event.type === "RATE_LIMIT_EXCEEDED" ? "access" : "access",
      severity: event.type === "UNAUTHORIZED_ACCESS" ? "high" : "medium",
      userId: event.userId,
      ip: event.ip,
      userAgent: event.userAgent,
      details: event.details,
    });
  }).catch(error => {
    // Fallback to console logging if audit system fails
    console.log("🚨 SECURITY EVENT:", {
      timestamp: new Date().toISOString(),
      ...event,
    });
    console.error("Security audit system unavailable:", error);
  });
}

/**
 * Validate request headers for security
 */
export function validateRequestHeaders(headers: Headers): {
  isValid: boolean;
  issues: string[];
} {
  const issues: string[] = [];

  // Check for suspicious user agents
  const userAgent = headers.get("user-agent");
  if (!userAgent || userAgent.length < 10) {
    issues.push("Suspicious or missing User-Agent");
  }

  // Check for common attack patterns in headers
  const suspiciousPatterns = [
    /script/i,
    /javascript/i,
    /vbscript/i,
    /onload/i,
    /onerror/i,
  ];

  headers.forEach((value, name) => {
    if (suspiciousPatterns.some((pattern) => pattern.test(value))) {
      issues.push(`Suspicious content in header: ${name}`);
    }
  });

  return {
    isValid: issues.length === 0,
    issues,
  };
}

export type SecurityEvent = Parameters<typeof logSecurityEvent>[0];

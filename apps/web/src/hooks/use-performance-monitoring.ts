/**
 * React Performance Monitoring Hooks
 *
 * Provides React hooks for monitoring component performance, user interactions,
 * and feature usage within the BuddyChip application.
 */

import { useUser } from "@clerk/nextjs";
import { usePathname } from "next/navigation";
import { useCallback, useEffect, useRef, useState } from "react";
import { enhancedPerformanceMonitor } from "@/lib/enhanced-performance-monitor";
import { analytics, userAnalytics } from "@/lib/user-analytics";
import type { FeatureType } from "../../prisma/generated";

/**
 * Hook for monitoring component render performance
 */
export function useComponentPerformance(componentName: string) {
  const renderCount = useRef(0);
  const mountTime = useRef<number>(0);
  const lastRenderTime = useRef<number>(0);

  useEffect(() => {
    renderCount.current++;
    const now = performance.now();

    if (!mountTime.current) {
      mountTime.current = now;
    }

    if (lastRenderTime.current) {
      const renderDuration = now - lastRenderTime.current;

      // Track slow renders
      if (renderDuration > 16) {
        // More than one frame at 60fps
        enhancedPerformanceMonitor.trackUserAnalytics({
          type: "user_action",
          sessionId: userAnalytics.getSessionSummary().sessionId,
          action: "slow_render",
          properties: {
            component: componentName,
            renderDuration,
            renderCount: renderCount.current,
          },
          timestamp: Date.now(),
          duration: renderDuration,
        });
      }
    }

    lastRenderTime.current = now;
  });

  return {
    renderCount: renderCount.current,
    mountTime: mountTime.current,
  };
}

/**
 * Hook for tracking page views and navigation
 */
export function usePageTracking() {
  const pathname = usePathname();
  const { user } = useUser();
  const previousPath = useRef<string>("");

  useEffect(() => {
    // Set user ID for analytics
    if (user?.id) {
      analytics.identifyUser(user.id);
    }
  }, [user?.id]);

  useEffect(() => {
    // Track page view
    if (pathname && pathname !== previousPath.current) {
      analytics.pageViewed(pathname, {
        previousPage: previousPath.current,
        userId: user?.id,
      });
      previousPath.current = pathname;
    }
  }, [pathname, user?.id]);

  return {
    currentPage: pathname,
    previousPage: previousPath.current,
  };
}

/**
 * Hook for tracking feature usage with automatic timing
 */
export function useFeatureTracking(feature: FeatureType) {
  const [isActive, setIsActive] = useState(false);
  const startTime = useRef<number>(0);
  const { user } = useUser();

  const startFeature = useCallback(
    (metadata?: Record<string, any>) => {
      if (isActive) return; // Already active

      setIsActive(true);
      startTime.current = performance.now();

      analytics.featureStarted(feature, {
        userId: user?.id,
        ...metadata,
      });
    },
    [feature, isActive, user?.id]
  );

  const completeFeature = useCallback(
    (metadata?: Record<string, any>) => {
      if (!isActive || !startTime.current) return;

      const duration = performance.now() - startTime.current;
      setIsActive(false);

      analytics.featureCompleted(feature, duration, {
        userId: user?.id,
        ...metadata,
      });
    },
    [feature, isActive, user?.id]
  );

  const failFeature = useCallback(
    (error?: string, metadata?: Record<string, any>) => {
      if (!isActive) return;

      setIsActive(false);

      analytics.featureFailed(feature, error, {
        userId: user?.id,
        ...metadata,
      });
    },
    [feature, isActive, user?.id]
  );

  return {
    isActive,
    startFeature,
    completeFeature,
    failFeature,
  };
}

/**
 * Hook for monitoring API call performance
 */
export function useApiPerformance() {
  const trackApiCall = useCallback(
    async <T>(
      apiName: string,
      apiCall: () => Promise<T>,
      metadata?: Record<string, any>
    ): Promise<T> => {
      const startTime = performance.now();

      try {
        const result = await apiCall();
        const duration = performance.now() - startTime;

        enhancedPerformanceMonitor.trackUserAnalytics({
          type: "user_action",
          sessionId: userAnalytics.getSessionSummary().sessionId,
          action: "api_call",
          properties: {
            apiName,
            duration,
            success: true,
            ...metadata,
          },
          timestamp: Date.now(),
          duration,
        });

        // Track slow API calls
        if (duration > 1000) {
          enhancedPerformanceMonitor.createAlert({
            type: "performance",
            severity: "medium",
            title: "Slow API Call",
            message: `API call ${apiName} took ${duration.toFixed(0)}ms`,
            metric: "api_response_time",
            currentValue: duration,
            threshold: 1000,
          });
        }

        return result;
      } catch (error) {
        const duration = performance.now() - startTime;

        enhancedPerformanceMonitor.trackUserAnalytics({
          type: "error",
          sessionId: userAnalytics.getSessionSummary().sessionId,
          properties: {
            apiName,
            duration,
            error: error instanceof Error ? error.message : String(error),
            ...metadata,
          },
          timestamp: Date.now(),
        });

        throw error;
      }
    },
    []
  );

  return { trackApiCall };
}

/**
 * Hook for tracking user interactions with UI elements
 */
export function useInteractionTracking() {
  const trackClick = useCallback(
    (elementName: string, metadata?: Record<string, any>) => {
      enhancedPerformanceMonitor.trackUserAnalytics({
        type: "user_action",
        sessionId: userAnalytics.getSessionSummary().sessionId,
        action: "click",
        properties: {
          element: elementName,
          page: window.location.pathname,
          ...metadata,
        },
        timestamp: Date.now(),
      });
    },
    []
  );

  const trackFormSubmit = useCallback(
    (formName: string, metadata?: Record<string, any>) => {
      enhancedPerformanceMonitor.trackUserAnalytics({
        type: "user_action",
        sessionId: userAnalytics.getSessionSummary().sessionId,
        action: "form_submit",
        properties: {
          form: formName,
          page: window.location.pathname,
          ...metadata,
        },
        timestamp: Date.now(),
      });
    },
    []
  );

  const trackSearch = useCallback(
    (query: string, results?: number, metadata?: Record<string, any>) => {
      // Hash the query to prevent logging sensitive information
      const sanitizedQuery =
        query.length > 100
          ? `${query.substring(0, 10)}...${query.substring(query.length - 10)}`
          : query;
      const queryHash = btoa(query).substring(0, 16); // Create a non-reversible hash for analysis

      enhancedPerformanceMonitor.trackUserAnalytics({
        type: "user_action",
        sessionId: userAnalytics.getSessionSummary().sessionId,
        action: "search",
        properties: {
          queryHash, // Use hash instead of actual query
          queryLength: query.length,
          results,
          page: window.location.pathname,
          ...metadata,
        },
        timestamp: Date.now(),
      });
    },
    []
  );

  return {
    trackClick,
    trackFormSubmit,
    trackSearch,
  };
}

/**
 * Hook for monitoring component errors
 */
export function useErrorTracking(componentName: string) {
  const trackError = useCallback(
    (error: Error, errorInfo?: any) => {
      analytics.errorOccurred(error, {
        component: componentName,
        errorInfo,
        page: window.location.pathname,
        timestamp: Date.now(),
      });
    },
    [componentName]
  );

  return { trackError };
}

/**
 * Hook for tracking business conversion events
 */
export function useConversionTracking() {
  const { user } = useUser();

  const trackSignup = useCallback(
    (metadata?: Record<string, any>) => {
      if (user?.id) {
        analytics.userSignedUp(user.id, metadata);
      }
    },
    [user?.id]
  );

  const trackSubscription = useCallback(
    (planId: string, value: number, metadata?: Record<string, any>) => {
      analytics.userSubscribed(planId, value, metadata);
    },
    []
  );

  const trackFeatureUnlock = useCallback(
    (feature: string, metadata?: Record<string, any>) => {
      userAnalytics.trackConversion({
        type: "feature_unlock",
        metadata: { feature, ...metadata },
      });
    },
    []
  );

  return {
    trackSignup,
    trackSubscription,
    trackFeatureUnlock,
  };
}

/**
 * Hook for getting real-time performance metrics
 */
export function usePerformanceMetrics(refreshInterval: number = 30000) {
  const [metrics, setMetrics] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchMetrics = () => {
      try {
        const report =
          enhancedPerformanceMonitor.getPerformanceReport(refreshInterval);
        setMetrics(report);
      } catch (error) {
        console.error("Failed to fetch performance metrics:", error);
      } finally {
        setLoading(false);
      }
    };

    // Initial fetch
    fetchMetrics();

    // Set up interval
    const interval = setInterval(fetchMetrics, refreshInterval);

    return () => clearInterval(interval);
  }, [refreshInterval]);

  return { metrics, loading };
}

/**
 * Hook for tracking session duration and engagement
 */
export function useSessionTracking() {
  const [sessionData, setSessionData] = useState<any>(null);

  useEffect(() => {
    // Update session data periodically
    const updateSession = () => {
      const summary = analytics.getSessionSummary();
      setSessionData(summary);
    };

    // Initial update
    updateSession();

    // Update every 30 seconds
    const interval = setInterval(updateSession, 30000);

    return () => clearInterval(interval);
  }, []);

  return sessionData;
}

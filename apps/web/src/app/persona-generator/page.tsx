"use client";

import { useAuth } from "@clerk/nextjs";
import { <PERSON><PERSON>ef<PERSON>, <PERSON>rk<PERSON> } from "lucide-react";
import { useRouter } from "next/navigation";
import React from "react";
import { toast } from "sonner";
import { PersonaGenerator } from "../../components/persona-generator";
import { Button } from "../../components/ui/button";
import ShardLoadingAnimation from "../../components/ui/shard-loading-animation";

export default function PersonaGeneratorPage() {
  const { isLoaded, isSignedIn } = useAuth();
  const router = useRouter();

  if (!isLoaded) {
    return (
      <div className="min-h-screen bg-app-background flex items-center justify-center">
        <ShardLoadingAnimation size={80} />
      </div>
    );
  }

  if (!isSignedIn) {
    router.push("/sign-in");
    return null;
  }

  const handlePersonaGenerated = (personaId: string) => {
    console.log(`🎭 Persona generated: ${personaId}`);
    toast.success(
      "Persona generated! You can now select it in your profile settings."
    );
  };

  return (
    <div className="min-h-screen bg-app-background">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.back()}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
          </div>

          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg">
              <Sparkles className="h-6 w-6 text-white" />
            </div>
            <h1 className="text-3xl font-bold text-app-headline">
              AI Persona Generator
            </h1>
          </div>

          <p className="text-app-card-paragraph/70 text-lg max-w-2xl">
            Transform any Twitter account into a custom AI personality. Our
            advanced AI analyzes writing style, personality traits, and
            engagement patterns to create authentic personas that capture unique
            voices and communication styles.
          </p>
        </div>

        {/* How It Works */}
        <div className="mb-8 p-6 bg-app-card border border-app-stroke rounded-lg">
          <h2 className="text-xl font-semibold text-app-card-heading mb-4">
            How It Works
          </h2>
          <div className="grid md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="w-8 h-8 bg-app-main/20 text-app-main rounded-full flex items-center justify-center mx-auto mb-2 font-semibold">
                1
              </div>
              <h3 className="font-medium text-app-card-heading mb-1">
                Collect Data
              </h3>
              <p className="text-sm text-app-card-paragraph/70">
                Fetch up to 500 tweets and 500 replies from the target account
              </p>
            </div>
            <div className="text-center">
              <div className="w-8 h-8 bg-app-highlight/20 text-app-highlight rounded-full flex items-center justify-center mx-auto mb-2 font-semibold">
                2
              </div>
              <h3 className="font-medium text-app-card-heading mb-1">
                Analyze Style
              </h3>
              <p className="text-sm text-app-card-paragraph/70">
                AI analyzes writing style, tone, personality traits, and topics
              </p>
            </div>
            <div className="text-center">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500/20 to-pink-500/20 text-purple-600 rounded-full flex items-center justify-center mx-auto mb-2 font-semibold">
                3
              </div>
              <h3 className="font-medium text-app-card-heading mb-1">
                Generate Persona
              </h3>
              <p className="text-sm text-app-card-paragraph/70">
                Create a comprehensive personality profile and system prompt
              </p>
            </div>
            <div className="text-center">
              <div className="w-8 h-8 bg-app-tertiary/20 text-app-tertiary rounded-full flex items-center justify-center mx-auto mb-2 font-semibold">
                4
              </div>
              <h3 className="font-medium text-app-card-heading mb-1">
                Use Persona
              </h3>
              <p className="text-sm text-app-card-paragraph/70">
                Select the generated persona for authentic AI responses
              </p>
            </div>
          </div>
        </div>

        {/* Features */}
        <div className="mb-8 grid md:grid-cols-2 gap-6">
          <div className="p-6 bg-app-card border border-app-stroke rounded-lg">
            <h3 className="text-lg font-semibold text-app-card-heading mb-3">
              Advanced Analysis
            </h3>
            <ul className="space-y-2 text-app-card-paragraph/70">
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-app-main rounded-full"></div>
                Writing style and tone analysis
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-app-main rounded-full"></div>
                Personality trait extraction
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-app-main rounded-full"></div>
                Topic and expertise identification
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-app-main rounded-full"></div>
                Engagement pattern analysis
              </li>
            </ul>
          </div>

          <div className="p-6 bg-app-card border border-app-stroke rounded-lg">
            <h3 className="text-lg font-semibold text-app-card-heading mb-3">
              Smart Generation
            </h3>
            <ul className="space-y-2 text-app-card-paragraph/70">
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-app-highlight rounded-full"></div>
                Comprehensive system prompts
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-app-highlight rounded-full"></div>
                Authentic voice replication
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-app-highlight rounded-full"></div>
                Context-aware responses
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-app-highlight rounded-full"></div>
                Ready-to-use personalities
              </li>
            </ul>
          </div>
        </div>

        {/* Main Generator Component */}
        <PersonaGenerator onPersonaGenerated={handlePersonaGenerated} />

        {/* Tips */}
        <div className="mt-8 p-6 bg-app-main/10 border border-app-main/20 rounded-lg">
          <h3 className="text-lg font-semibold text-app-card-heading mb-3">
            💡 Tips for Best Results
          </h3>
          <ul className="space-y-2 text-app-card-paragraph">
            <li className="flex items-start gap-2">
              <span className="text-app-main mt-1">•</span>
              <span>
                Choose accounts with diverse, original content (avoid accounts
                that mostly retweet)
              </span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-app-main mt-1">•</span>
              <span>
                Accounts with regular engagement and replies work best for
                personality analysis
              </span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-app-main mt-1">•</span>
              <span>
                The process takes 3-5 minutes depending on the amount of content
                to analyze
              </span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-app-main mt-1">•</span>
              <span>
                Generated personas can be edited and customized in your profile
                settings
              </span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}

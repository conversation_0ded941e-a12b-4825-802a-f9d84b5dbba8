/**
 * Security Dashboard API Endpoint
 * 
 * Provides security metrics, alerts, and audit trail access
 * for administrators and security monitoring
 */

import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { getSecurityDashboard, securityHealthCheck } from "@/lib/security-monitor";
import { validateRequest, commonSchemas } from "@/lib/validation-middleware";
import { rateLimiters } from "@/lib/rate-limiter";

/**
 * Query schema for dashboard endpoint
 */
const dashboardQuerySchema = z.object({
  timeframe: z.enum(["24h", "7d", "30d"]).default("24h"),
  type: z.enum(["overview", "events", "alerts", "health"]).default("overview"),
});

/**
 * GET /api/security/dashboard
 * 
 * Returns security dashboard data
 */
export const GET = rateLimiters.api(
  validateRequest({
    query: dashboardQuerySchema,
    headers: z.object({
      authorization: z.string().startsWith("Bearer ").optional(),
      "x-admin-key": z.string().optional(),
    }),
  })(async function handler(req: NextRequest) {
    try {
      // Admin authentication check
      const adminKey = req.headers.get("x-admin-key");
      const authHeader = req.headers.get("authorization");
      
      // In production, implement proper admin authentication
      const isAdmin = adminKey === process.env.ADMIN_SECRET_KEY ||
                     (authHeader && await validateAdminToken(authHeader));
      
      if (!isAdmin) {
        return NextResponse.json(
          { 
            error: "Unauthorized access to security dashboard",
            code: "ADMIN_ACCESS_REQUIRED" 
          },
          { status: 403 }
        );
      }

      // Extract validated query params
      const { timeframe, type } = (req as any).validated.query;

      switch (type) {
        case "overview":
          const dashboardData = await getSecurityDashboard(timeframe);
          return NextResponse.json({
            success: true,
            data: dashboardData,
            timeframe,
            generated_at: new Date().toISOString(),
          });

        case "health":
          const healthData = await securityHealthCheck();
          return NextResponse.json({
            success: true,
            data: healthData,
            generated_at: new Date().toISOString(),
          });

        case "events":
          // Return recent security events (would be implemented with proper KV scanning)
          return NextResponse.json({
            success: true,
            data: {
              events: [],
              total: 0,
              message: "Event querying not yet implemented - requires KV scanning",
            },
            timeframe,
            generated_at: new Date().toISOString(),
          });

        case "alerts":
          // Return recent security alerts (would be implemented with proper KV scanning)
          return NextResponse.json({
            success: true,
            data: {
              alerts: [],
              total: 0,
              message: "Alert querying not yet implemented - requires KV scanning",
            },
            timeframe,
            generated_at: new Date().toISOString(),
          });

        default:
          return NextResponse.json(
            { 
              error: "Invalid dashboard type",
              code: "INVALID_TYPE" 
            },
            { status: 400 }
          );
      }

    } catch (error) {
      console.error("Security dashboard error:", error);
      
      return NextResponse.json(
        {
          error: "Failed to load security dashboard",
          code: "DASHBOARD_ERROR",
        },
        { status: 500 }
      );
    }
  })
);

/**
 * Validate admin token (placeholder - implement with your auth system)
 */
async function validateAdminToken(authHeader: string): Promise<boolean> {
  try {
    // Extract token
    const token = authHeader.replace("Bearer ", "");
    
    // In production, validate against your auth system
    // For now, just check if it's a valid format
    return token.length >= 32 && /^[a-zA-Z0-9]+$/.test(token);
    
  } catch (error) {
    return false;
  }
}

/**
 * POST /api/security/dashboard/test-alert
 * 
 * Test endpoint for triggering security alerts (development only)
 */
export const POST = rateLimiters.api(
  validateRequest({
    body: z.object({
      type: z.enum(["AUTH_FAILURE", "INVALID_INPUT", "UNAUTHORIZED_ACCESS", "RATE_LIMIT_EXCEEDED"]),
      severity: z.enum(["low", "medium", "high", "critical"]).default("medium"),
      details: z.string().max(500).default("Test security event"),
    }),
  })(async function handler(req: NextRequest) {
    // Only allow in development
    if (process.env.NODE_ENV === "production") {
      return NextResponse.json(
        { 
          error: "Test alerts not available in production",
          code: "NOT_AVAILABLE" 
        },
        { status: 403 }
      );
    }

    try {
      const { logSecurityAuditEvent } = await import("@/lib/security-monitor");
      const { type, severity, details } = (req as any).validated.body;
      
      // Log test security event
      await logSecurityAuditEvent({
        type,
        category: "system",
        severity,
        ip: req.headers.get("x-forwarded-for")?.split(",")[0]?.trim() || "test",
        details: `[TEST] ${details}`,
        endpoint: "/api/security/dashboard/test-alert",
        method: "POST",
      });

      return NextResponse.json({
        success: true,
        message: "Test security alert triggered",
        event: {
          type,
          severity,
          details,
          timestamp: new Date().toISOString(),
        },
      });

    } catch (error) {
      console.error("Failed to trigger test alert:", error);
      
      return NextResponse.json(
        {
          error: "Failed to trigger test alert",
          code: "TEST_ALERT_FAILED",
        },
        { status: 500 }
      );
    }
  })
);
/**
 * CSRF Token API Endpoint
 * 
 * Provides CSRF tokens for authenticated users to protect against
 * Cross-Site Request Forgery attacks
 */

import { NextRequest } from "next/server";
import { getCSRFTokenAPI } from "@/lib/csrf-middleware";

/**
 * GET /api/csrf
 * 
 * Returns a CSRF token for authenticated users
 * The token is set in both the response body and as an httpOnly cookie
 */
export async function GET(req: NextRequest) {
  return getCSRFTokenAPI(req);
}
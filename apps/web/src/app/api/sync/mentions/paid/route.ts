/**
 * Paid Sync API Route - Reply Guy & Reply God Plans
 *
 * This endpoint is called by Vercel Cron every 3 minutes
 * to automatically sync mentions for <PERSON><PERSON> Guy and Reply God plan users.
 *
 * POST /api/sync/mentions/paid
 */

import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { prisma } from "@/lib/db-utils";
import { JobManager } from "@/lib/job-manager";
import { MentionSyncService } from "@/lib/mention-sync-service";
import { validateRequest, commonSchemas } from "@/lib/validation-middleware";

// Required API key for security
const SYNC_API_KEY_PAID = process.env.SYNC_API_KEY_PAID || process.env.SYNC_API_KEY;
const SYNC_PAID_MAX_USERS = parseInt(process.env.SYNC_PAID_MAX_USERS || "30", 10);

// Validation schemas
const syncHeaderSchema = z.object({
  authorization: z.string().regex(/^Bearer [a-zA-Z0-9-_]{10,}$/, "Invalid authorization format"),
});

const syncBodySchema = z.object({
  userId: commonSchemas.cuid.optional(),
  maxUsers: z.number().int().min(1).max(100).default(SYNC_PAID_MAX_USERS),
  planFilter: z.enum(["reply-guy", "reply-god", "both"]).default("both"),
}).strict();

// Apply validation middleware
const validateSyncRequest = validateRequest({
  headers: syncHeaderSchema,
  body: syncBodySchema,
  maxBodySize: 1024, // 1KB - small request body
});

async function handlePaidSyncRequest(request: NextRequest) {
  try {
    console.log("🔄 Paid sync endpoint called (Reply Guy & Reply God plans)");

    // SECURITY: API key validation using validated header data
    if (!SYNC_API_KEY_PAID) {
      console.error("❌ SYNC_API_KEY_PAID not configured");
      return NextResponse.json(
        { error: "Service configuration error" },
        { status: 500 }
      );
    }

    // Extract validated header data
    const { headers: validatedHeaders, body: validatedBody } = (request as any).validated || {};
    const providedKey = validatedHeaders?.authorization?.replace("Bearer ", "");

    if (!providedKey || providedKey !== SYNC_API_KEY_PAID) {
      console.log(
        "❌ Unauthorized paid sync request from:",
        request.headers.get("x-forwarded-for") || "unknown"
      );
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    console.log("✅ Paid sync request authorized");

    // Use validated request data
    const targetUserId = validatedBody?.userId;
    const maxUsers = validatedBody?.maxUsers || SYNC_PAID_MAX_USERS;
    const planFilter = validatedBody?.planFilter || "both";

    const syncService = new MentionSyncService(prisma);
    const jobManager = new JobManager(prisma);
    const results = [];
    let totalNewMentions = 0;
    let totalErrors = 0;

    // First, process any pending background jobs (medium priority)
    console.log("🔄 Processing pending background jobs first...");
    try {
      const jobResults = await jobManager.processAllPendingJobs(20); // Process up to 20 jobs
      console.log(`📊 Job processing results:`, jobResults);

      // Add job processing stats to results
      if (jobResults.processed > 0) {
        results.push({
          type: "background_jobs",
          processed: jobResults.processed,
          successful: jobResults.successful,
          failed: jobResults.failed,
        });
      }
    } catch (jobError) {
      console.error("❌ Error processing background jobs:", jobError);
      totalErrors++;
      results.push({
        type: "background_jobs",
        error:
          jobError instanceof Error
            ? jobError.message
            : "Unknown job processing error",
      });
    }

    if (targetUserId) {
      // Sync specific user (must be Reply Guy or Reply God plan)
      console.log(`🎯 Syncing mentions for specific paid plan user: ${targetUserId}`);

      // Verify user is on paid plan
      const user = await prisma.user.findUnique({
        where: { id: targetUserId },
        include: {
          plan: true,
          monitoredAccounts: {
            where: { isActive: true },
          },
        },
      });

      if (!user || !["reply-guy", "reply-god"].includes(user.plan.name)) {
        return NextResponse.json(
          { error: "User not found or not on paid plan" },
          { status: 404 }
        );
      }

      const bulkResult = await syncService.syncAllUserAccounts(targetUserId);
      results.push({
        userId: targetUserId,
        planName: user.plan.displayName,
        success: bulkResult.success,
        newMentions: bulkResult.totalNewMentions,
        errors: bulkResult.errors,
      });
      totalNewMentions += bulkResult.totalNewMentions;
      totalErrors += bulkResult.errors.length;

      // Update lastSyncedAt
      await prisma.user.update({
        where: { id: targetUserId },
        data: { lastSyncedAt: new Date() },
      });
    } else {
      // Sync for all paid plan users (Reply Guy & Reply God)
      console.log(`💰 Syncing mentions for paid plan users (max: ${maxUsers}, filter: ${planFilter})`);

      // Determine which plans to include
      const planNames = planFilter === "both" 
        ? ["reply-guy", "reply-god"]
        : [planFilter];

      // Get paid plan users with active monitored accounts
      const paidUsers = await prisma.user.findMany({
        where: {
          plan: {
            name: { in: planNames },
          },
          monitoredAccounts: {
            some: {
              isActive: true,
            },
          },
        },
        take: maxUsers,
        orderBy: [
          { plan: { price: "desc" } }, // Prioritize higher-paying plans (Reply God > Reply Guy)
          { lastActiveAt: "desc" }, // Prioritize recently active users
          { lastSyncedAt: "asc" }, // Prioritize users that haven't been synced recently
        ],
        include: {
          plan: true,
          monitoredAccounts: {
            where: { isActive: true },
            orderBy: { lastCheckedAt: "asc" }, // Prioritize accounts that haven't been synced recently
          },
        },
      });

      console.log(
        `📋 Found ${paidUsers.length} paid plan users with active monitored accounts`
      );

      for (const user of paidUsers) {
        try {
          console.log(
            `⏳ Syncing ${user.plan.displayName} user ${user.id} (${user.monitoredAccounts.length} accounts)`
          );

          const bulkResult = await syncService.syncAllUserAccounts(user.id);

          results.push({
            userId: user.id,
            userEmail: user.email,
            planName: user.plan.displayName,
            planPrice: user.plan.price.toString(),
            accountCount: user.monitoredAccounts.length,
            success: bulkResult.success,
            newMentions: bulkResult.totalNewMentions,
            errors: bulkResult.errors,
          });

          totalNewMentions += bulkResult.totalNewMentions;
          totalErrors += bulkResult.errors.length;

          // Update lastSyncedAt for this user
          await prisma.user.update({
            where: { id: user.id },
            data: { lastSyncedAt: new Date() },
          });

          // Add delay between users to respect rate limits
          // Reply God users get shorter delays (higher priority)
          const delayMs = user.plan.name === "reply-god" ? 1000 : 1500; // 1s for Reply God, 1.5s for Reply Guy
          await new Promise((resolve) => setTimeout(resolve, delayMs));
        } catch (error) {
          console.error(`❌ Failed to sync paid user ${user.id}:`, error);
          totalErrors++;
          results.push({
            userId: user.id,
            userEmail: user.email,
            planName: user.plan?.displayName || "Unknown",
            success: false,
            error: error instanceof Error ? error.message : "Unknown error",
          });
        }
      }
    }

    const response = {
      success: totalErrors === 0,
      timestamp: new Date().toISOString(),
      plans: planFilter === "both" ? ["reply-guy", "reply-god"] : [planFilter],
      syncType: "paid",
      summary: {
        totalNewMentions,
        totalErrors,
        usersProcessed: results.length,
        maxUsersLimit: maxUsers,
        planFilter,
      },
      results,
    };

    console.log(
      `🏁 Paid sync completed: ${totalNewMentions} new mentions, ${totalErrors} errors`
    );

    return NextResponse.json(response, {
      status: totalErrors === 0 ? 200 : 207, // 207 = Multi-Status (partial success)
    });
  } catch (error) {
    console.error("❌ Paid sync API error:", error);

    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
        plans: ["reply-guy", "reply-god"],
        syncType: "paid",
      },
      { status: 500 }
    );
  }
}

// Export POST handler with validation middleware
export const POST = validateSyncRequest(handlePaidSyncRequest);

// GET endpoint for health checks (no auth required)
const validateGetRequest = validateRequest({
  maxBodySize: 0, // No body expected for GET
});

async function handleGetRequest() {
  try {
    // Get paid plan user stats
    const replyGuyUsers = await prisma.user.count({
      where: {
        plan: { name: "reply-guy" },
        monitoredAccounts: {
          some: { isActive: true },
        },
      },
    });

    const replyGodUsers = await prisma.user.count({
      where: {
        plan: { name: "reply-god" },
        monitoredAccounts: {
          some: { isActive: true },
        },
      },
    });

    const totalPaidAccounts = await prisma.monitoredAccount.count({
      where: {
        isActive: true,
        user: {
          plan: {
            name: { in: ["reply-guy", "reply-god"] },
          },
        },
      },
    });

    const recentSyncs = await prisma.user.count({
      where: {
        plan: {
          name: { in: ["reply-guy", "reply-god"] },
        },
        lastSyncedAt: {
          gte: new Date(Date.now() - 5 * 60 * 1000), // Last 5 minutes
        },
      },
    });

    return NextResponse.json({
      status: "healthy",
      plans: ["reply-guy", "reply-god"],
      syncType: "paid",
      timestamp: new Date().toISOString(),
      stats: {
        replyGuyUsers,
        replyGodUsers,
        totalPaidUsers: replyGuyUsers + replyGodUsers,
        totalPaidAccounts,
        recentSyncs,
        maxUsersPerSync: SYNC_PAID_MAX_USERS,
      },
    });
  } catch (error) {
    console.error("❌ Paid sync status error:", error);

    return NextResponse.json(
      {
        status: "error",
        plans: ["reply-guy", "reply-god"],
        syncType: "paid",
        message: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Export GET handler with validation middleware
export const GET = validateGetRequest(handleGetRequest);
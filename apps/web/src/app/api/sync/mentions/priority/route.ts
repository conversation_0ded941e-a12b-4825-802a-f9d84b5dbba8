/**
 * Priority Sync API Route - Team Plan Only
 *
 * This endpoint is called by Vercel Cron every minute
 * to automatically sync mentions for Team plan users only.
 *
 * POST /api/sync/mentions/priority
 */

import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { prisma } from "@/lib/db-utils";
import { JobManager } from "@/lib/job-manager";
import { MentionSyncService } from "@/lib/mention-sync-service";
import { validateRequest, commonSchemas } from "@/lib/validation-middleware";

// Required API key for security
const SYNC_API_KEY_PRIORITY = process.env.SYNC_API_KEY_PRIORITY || process.env.SYNC_API_KEY;
const SYNC_PRIORITY_MAX_USERS = parseInt(process.env.SYNC_PRIORITY_MAX_USERS || "50", 10);

// Validation schemas
const syncHeaderSchema = z.object({
  authorization: z.string().regex(/^Bearer [a-zA-Z0-9-_]{10,}$/, "Invalid authorization format"),
});

const syncBodySchema = z.object({
  userId: commonSchemas.cuid.optional(),
  maxUsers: z.number().int().min(1).max(100).default(SYNC_PRIORITY_MAX_USERS),
}).strict();

// Apply validation middleware
const validateSyncRequest = validateRequest({
  headers: syncHeaderSchema,
  body: syncBodySchema,
  maxBodySize: 1024, // 1KB - small request body
});

async function handlePrioritySyncRequest(request: NextRequest) {
  try {
    console.log("🔄 Priority sync endpoint called (Team plan only)");

    // SECURITY: API key validation using validated header data
    if (!SYNC_API_KEY_PRIORITY) {
      console.error("❌ SYNC_API_KEY_PRIORITY not configured");
      return NextResponse.json(
        { error: "Service configuration error" },
        { status: 500 }
      );
    }

    // Extract validated header data
    const { headers: validatedHeaders, body: validatedBody } = (request as any).validated || {};
    const providedKey = validatedHeaders?.authorization?.replace("Bearer ", "");

    if (!providedKey || providedKey !== SYNC_API_KEY_PRIORITY) {
      console.log(
        "❌ Unauthorized priority sync request from:",
        request.headers.get("x-forwarded-for") || "unknown"
      );
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    console.log("✅ Priority sync request authorized");

    // Use validated request data
    const targetUserId = validatedBody?.userId;
    const maxUsers = validatedBody?.maxUsers || SYNC_PRIORITY_MAX_USERS;

    const syncService = new MentionSyncService(prisma);
    const jobManager = new JobManager(prisma);
    const results = [];
    let totalNewMentions = 0;
    let totalErrors = 0;

    // First, process any pending background jobs (high priority)
    console.log("🔄 Processing pending background jobs first...");
    try {
      const jobResults = await jobManager.processAllPendingJobs(30); // Process up to 30 jobs
      console.log(`📊 Job processing results:`, jobResults);

      // Add job processing stats to results
      if (jobResults.processed > 0) {
        results.push({
          type: "background_jobs",
          processed: jobResults.processed,
          successful: jobResults.successful,
          failed: jobResults.failed,
        });
      }
    } catch (jobError) {
      console.error("❌ Error processing background jobs:", jobError);
      totalErrors++;
      results.push({
        type: "background_jobs",
        error:
          jobError instanceof Error
            ? jobError.message
            : "Unknown job processing error",
      });
    }

    if (targetUserId) {
      // Sync specific user (must be Team plan)
      console.log(`🎯 Syncing mentions for specific Team plan user: ${targetUserId}`);

      // Verify user is on Team plan
      const user = await prisma.user.findUnique({
        where: { id: targetUserId },
        include: {
          plan: true,
          monitoredAccounts: {
            where: { isActive: true },
          },
        },
      });

      if (!user || user.plan.name !== "team") {
        return NextResponse.json(
          { error: "User not found or not on Team plan" },
          { status: 404 }
        );
      }

      const bulkResult = await syncService.syncAllUserAccounts(targetUserId);
      results.push({
        userId: targetUserId,
        planName: user.plan.displayName,
        success: bulkResult.success,
        newMentions: bulkResult.totalNewMentions,
        errors: bulkResult.errors,
      });
      totalNewMentions += bulkResult.totalNewMentions;
      totalErrors += bulkResult.errors.length;

      // Update lastSyncedAt
      await prisma.user.update({
        where: { id: targetUserId },
        data: { lastSyncedAt: new Date() },
      });
    } else {
      // Sync for all Team plan users
      console.log(`🌟 Syncing mentions for Team plan users (max: ${maxUsers})`);

      // Get Team plan users with active monitored accounts
      const teamUsers = await prisma.user.findMany({
        where: {
          plan: {
            name: "team",
          },
          monitoredAccounts: {
            some: {
              isActive: true,
            },
          },
        },
        take: maxUsers,
        orderBy: [
          { lastActiveAt: "desc" }, // Prioritize recently active users
          { lastSyncedAt: "asc" }, // Prioritize users that haven't been synced recently
        ],
        include: {
          plan: true,
          monitoredAccounts: {
            where: { isActive: true },
            orderBy: { lastCheckedAt: "asc" }, // Prioritize accounts that haven't been synced recently
          },
        },
      });

      console.log(
        `📋 Found ${teamUsers.length} Team plan users with active monitored accounts`
      );

      for (const user of teamUsers) {
        try {
          console.log(
            `⏳ Syncing Team user ${user.id} (${user.monitoredAccounts.length} accounts)`
          );

          const bulkResult = await syncService.syncAllUserAccounts(user.id);

          results.push({
            userId: user.id,
            userEmail: user.email,
            planName: user.plan.displayName,
            accountCount: user.monitoredAccounts.length,
            success: bulkResult.success,
            newMentions: bulkResult.totalNewMentions,
            errors: bulkResult.errors,
          });

          totalNewMentions += bulkResult.totalNewMentions;
          totalErrors += bulkResult.errors.length;

          // Update lastSyncedAt for this user
          await prisma.user.update({
            where: { id: user.id },
            data: { lastSyncedAt: new Date() },
          });

          // Minimal delay for Team users (they get priority)
          await new Promise((resolve) => setTimeout(resolve, 500)); // 0.5 second delay
        } catch (error) {
          console.error(`❌ Failed to sync Team user ${user.id}:`, error);
          totalErrors++;
          results.push({
            userId: user.id,
            userEmail: user.email,
            planName: "Team",
            success: false,
            error: error instanceof Error ? error.message : "Unknown error",
          });
        }
      }
    }

    const response = {
      success: totalErrors === 0,
      timestamp: new Date().toISOString(),
      plan: "team",
      syncType: "priority",
      summary: {
        totalNewMentions,
        totalErrors,
        usersProcessed: results.length,
        maxUsersLimit: maxUsers,
      },
      results,
    };

    console.log(
      `🏁 Priority sync completed: ${totalNewMentions} new mentions, ${totalErrors} errors`
    );

    return NextResponse.json(response, {
      status: totalErrors === 0 ? 200 : 207, // 207 = Multi-Status (partial success)
    });
  } catch (error) {
    console.error("❌ Priority sync API error:", error);

    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
        plan: "team",
        syncType: "priority",
      },
      { status: 500 }
    );
  }
}

// Export POST handler with validation middleware
export const POST = validateSyncRequest(handlePrioritySyncRequest);

// GET endpoint for health checks (no auth required)
const validateGetRequest = validateRequest({
  maxBodySize: 0, // No body expected for GET
});

async function handleGetRequest() {
  try {
    // Get Team plan user stats
    const teamUsers = await prisma.user.count({
      where: {
        plan: { name: "team" },
        monitoredAccounts: {
          some: { isActive: true },
        },
      },
    });

    const totalTeamAccounts = await prisma.monitoredAccount.count({
      where: {
        isActive: true,
        user: {
          plan: { name: "team" },
        },
      },
    });

    const recentSyncs = await prisma.user.count({
      where: {
        plan: { name: "team" },
        lastSyncedAt: {
          gte: new Date(Date.now() - 5 * 60 * 1000), // Last 5 minutes
        },
      },
    });

    return NextResponse.json({
      status: "healthy",
      plan: "team",
      syncType: "priority",
      timestamp: new Date().toISOString(),
      stats: {
        teamUsers,
        totalTeamAccounts,
        recentSyncs,
        maxUsersPerSync: SYNC_PRIORITY_MAX_USERS,
      },
    });
  } catch (error) {
    console.error("❌ Priority sync status error:", error);

    return NextResponse.json(
      {
        status: "error",
        plan: "team",
        syncType: "priority",
        message: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Export GET handler with validation middleware
export const GET = validateGetRequest(handleGetRequest);
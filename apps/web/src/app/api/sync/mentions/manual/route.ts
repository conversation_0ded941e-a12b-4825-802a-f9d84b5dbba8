/**
 * Manual Sync API Route - Free Users & Manual Triggers
 *
 * This endpoint allows free users to manually trigger syncs with rate limiting.
 * Also used for manual sync triggers from UI for any user.
 *
 * POST /api/sync/mentions/manual
 */

import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/db-utils";
import { MentionSyncService } from "@/lib/mention-sync-service";
import { validateRequest, commonSchemas } from "@/lib/validation-middleware";
import { checkRateLimit, recordUsage } from "@/lib/db-utils";

// Rate limiting configuration
const FREE_USER_MANUAL_SYNC_COOLDOWN_MINUTES = parseInt(
  process.env.FREE_USER_MANUAL_SYNC_COOLDOWN_MINUTES || "30",
  10
);

// Validation schemas
const manualSyncBodySchema = z.object({
  accountId: commonSchemas.cuid.optional(),
  force: z.boolean().default(false), // Allow paid users to bypass cooldown
}).strict();

// Apply validation middleware
const validateManualSyncRequest = validateRequest({
  body: manualSyncBodySchema,
  maxBodySize: 1024, // 1KB - small request body
});

async function handleManualSyncRequest(request: NextRequest) {
  try {
    console.log("🔄 Manual sync endpoint called");

    // Get authenticated user
    const { userId: clerkUserId } = await auth();
    
    if (!clerkUserId) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Get user with plan information
    const user = await prisma.user.findUnique({
      where: { id: clerkUserId },
      include: {
        plan: true,
        monitoredAccounts: {
          where: { isActive: true },
        },
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    console.log(`✅ Manual sync request from user: ${clerkUserId} (Plan: ${user.plan.displayName})`);

    // Extract validated request data
    const { body: validatedBody } = (request as any).validated || {};
    const targetAccountId = validatedBody?.accountId;
    const force = validatedBody?.force || false;

    const isFreeUser = user.plan.name === "free";
    const isPaidUser = !isFreeUser;

    // Rate limiting for free users
    if (isFreeUser && !force) {
      const lastManualSync = user.lastManualSyncAt;
      const cooldownMs = FREE_USER_MANUAL_SYNC_COOLDOWN_MINUTES * 60 * 1000;
      
      if (lastManualSync && Date.now() - lastManualSync.getTime() < cooldownMs) {
        const remainingMs = cooldownMs - (Date.now() - lastManualSync.getTime());
        const remainingMinutes = Math.ceil(remainingMs / (60 * 1000));
        
        console.log(`⏰ Free user ${clerkUserId} hit rate limit, ${remainingMinutes}m remaining`);
        
        return NextResponse.json(
          {
            error: "Manual sync cooldown active",
            message: `Please wait ${remainingMinutes} minutes before syncing again`,
            cooldownMinutes: remainingMinutes,
            nextSyncAt: new Date(Date.now() + remainingMs).toISOString(),
            planUpgradeRequired: true,
          },
          { status: 429 }
        );
      }
    }

    const syncService = new MentionSyncService(prisma);
    const results = [];
    let totalNewMentions = 0;
    let totalErrors = 0;

    if (targetAccountId) {
      // Sync specific account
      console.log(`🎯 Manual sync for specific account: ${targetAccountId}`);
      
      // Verify account belongs to user
      const account = await prisma.monitoredAccount.findFirst({
        where: {
          id: targetAccountId,
          userId: clerkUserId,
          isActive: true,
        },
      });

      if (!account) {
        return NextResponse.json(
          { error: "Account not found or not accessible" },
          { status: 404 }
        );
      }

      const result = await syncService.syncAccountMentions(targetAccountId, clerkUserId);
      results.push(result);
      
      if (result.success) {
        totalNewMentions += result.newMentions;
      } else {
        totalErrors++;
      }
    } else {
      // Sync all user's accounts
      console.log(`🌍 Manual sync for all user accounts: ${clerkUserId}`);
      
      if (user.monitoredAccounts.length === 0) {
        return NextResponse.json(
          {
            error: "No active monitored accounts found",
            message: "Please add and activate at least one monitored account to sync mentions",
          },
          { status: 400 }
        );
      }

      const bulkResult = await syncService.syncAllUserAccounts(clerkUserId);
      results.push({
        userId: clerkUserId,
        planName: user.plan.displayName,
        accountCount: user.monitoredAccounts.length,
        success: bulkResult.success,
        newMentions: bulkResult.totalNewMentions,
        errors: bulkResult.errors,
      });
      
      totalNewMentions += bulkResult.totalNewMentions;
      totalErrors += bulkResult.errors.length;
    }

    // Update lastManualSyncAt for the user
    await prisma.user.update({
      where: { id: clerkUserId },
      data: { 
        lastManualSyncAt: new Date(),
        // Also update lastSyncedAt if no errors
        ...(totalErrors === 0 ? { lastSyncedAt: new Date() } : {}),
      },
    });

    // Record usage for tracking
    if (totalNewMentions > 0) {
      try {
        await recordUsage(clerkUserId, "MENTIONS_PER_MONTH", totalNewMentions, {
          source: "manual_sync",
          accountId: targetAccountId || null,
          timestamp: new Date().toISOString(),
        });
      } catch (usageError) {
        console.warn("⚠️ Failed to record usage:", usageError);
      }
    }

    const nextManualSyncAt = isFreeUser 
      ? new Date(Date.now() + FREE_USER_MANUAL_SYNC_COOLDOWN_MINUTES * 60 * 1000)
      : null;

    const response = {
      success: totalErrors === 0,
      timestamp: new Date().toISOString(),
      userId: clerkUserId,
      planName: user.plan.displayName,
      syncType: "manual",
      isFreeUser,
      summary: {
        totalNewMentions,
        totalErrors,
        accountsProcessed: targetAccountId ? 1 : user.monitoredAccounts.length,
      },
      rateLimit: isFreeUser ? {
        cooldownMinutes: FREE_USER_MANUAL_SYNC_COOLDOWN_MINUTES,
        nextSyncAt: nextManualSyncAt?.toISOString(),
      } : null,
      results,
    };

    console.log(
      `🏁 Manual sync completed for ${user.plan.displayName} user: ${totalNewMentions} new mentions, ${totalErrors} errors`
    );

    return NextResponse.json(response, {
      status: totalErrors === 0 ? 200 : 207, // 207 = Multi-Status (partial success)
    });
  } catch (error) {
    console.error("❌ Manual sync API error:", error);

    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
        syncType: "manual",
      },
      { status: 500 }
    );
  }
}

// Export POST handler with validation middleware
export const POST = validateManualSyncRequest(handleManualSyncRequest);

// GET endpoint for checking sync status and cooldown
async function handleGetRequest() {
  try {
    // Get authenticated user
    const { userId: clerkUserId } = await auth();
    
    if (!clerkUserId) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Get user with plan information
    const user = await prisma.user.findUnique({
      where: { id: clerkUserId },
      include: {
        plan: true,
        monitoredAccounts: {
          where: { isActive: true },
        },
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    const isFreeUser = user.plan.name === "free";
    const lastManualSync = user.lastManualSyncAt;
    const cooldownMs = FREE_USER_MANUAL_SYNC_COOLDOWN_MINUTES * 60 * 1000;
    
    let canSync = true;
    let remainingCooldownMs = 0;
    
    if (isFreeUser && lastManualSync) {
      remainingCooldownMs = Math.max(0, cooldownMs - (Date.now() - lastManualSync.getTime()));
      canSync = remainingCooldownMs === 0;
    }

    return NextResponse.json({
      userId: clerkUserId,
      planName: user.plan.displayName,
      isFreeUser,
      canSync,
      lastManualSyncAt: lastManualSync?.toISOString(),
      activeAccounts: user.monitoredAccounts.length,
      rateLimit: isFreeUser ? {
        cooldownMinutes: FREE_USER_MANUAL_SYNC_COOLDOWN_MINUTES,
        remainingCooldownMs,
        nextSyncAt: remainingCooldownMs > 0 
          ? new Date(Date.now() + remainingCooldownMs).toISOString()
          : null,
      } : null,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("❌ Manual sync status error:", error);

    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Export GET handler (no validation middleware needed)
export const GET = handleGetRequest;
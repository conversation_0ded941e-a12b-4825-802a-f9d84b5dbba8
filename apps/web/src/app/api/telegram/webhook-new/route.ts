/**
 * New Telegram Webhook Handler
 *
 * Clean, simplified webhook handler using the new security system
 */

import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { TelegramBotService } from "@/lib/telegram-bot";
import { telegramLogger } from "@/lib/telegram-logger";
import { telegramSecurity } from "@/lib/telegram-security-config";
import { telegramSecurityValidator } from "@/lib/telegram-security-new";

// Telegram update schema for validation
const TelegramUpdateSchema = z.object({
  update_id: z.number(),
  message: z
    .object({
      message_id: z.number(),
      from: z.object({
        id: z.number(),
        is_bot: z.boolean(),
        first_name: z.string(),
        username: z.string().optional(),
        language_code: z.string().optional(),
      }),
      chat: z.object({
        id: z.number(),
        type: z.string(),
        first_name: z.string().optional(),
        username: z.string().optional(),
      }),
      date: z.number(),
      text: z.string().optional(),
    })
    .optional(),
  callback_query: z
    .object({
      id: z.string(),
      from: z.object({
        id: z.number(),
        is_bot: z.boolean(),
        first_name: z.string(),
        username: z.string().optional(),
      }),
      message: z
        .object({
          message_id: z.number(),
          chat: z.object({
            id: z.number(),
            type: z.string(),
          }),
        })
        .optional(),
      data: z.string().optional(),
    })
    .optional(),
});

type TelegramUpdate = z.infer<typeof TelegramUpdateSchema>;

/**
 * Health check endpoint
 */
export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const action = url.searchParams.get("action");

    if (action === "health") {
      // Configuration health check
      const config = telegramSecurity.getConfig();
      const isValid = telegramSecurity.isValid();
      const errors = telegramSecurity.getErrors();

      const healthStatus = {
        status: isValid ? "healthy" : "unhealthy",
        service: "telegram-webhook-new",
        timestamp: new Date().toISOString(),
        environment: config.environment,
        configuration: {
          valid: isValid,
          errors: errors,
          botTokenConfigured: !!config.botToken,
          webhookSecretConfigured: !!config.webhookSecret,
          securitySettings: {
            webhookSecretValidation: config.enableWebhookSecretValidation,
            ipValidation: config.enableIPValidation,
            rateLimit: config.enableRateLimit,
            contentValidation: config.enableContentValidation,
          },
        },
      };

      telegramLogger.logHealthCheck(
        isValid ? "healthy" : "unhealthy",
        healthStatus
      );

      return NextResponse.json(healthStatus);
    }

    return NextResponse.json({ error: "Unknown action" }, { status: 400 });
  } catch (error) {
    telegramLogger.error("Health check failed", {
      error: error instanceof Error ? error : new Error(String(error)),
    });

    return NextResponse.json(
      {
        status: "error",
        service: "telegram-webhook-new",
        timestamp: new Date().toISOString(),
        error: "Health check failed",
      },
      { status: 500 }
    );
  }
}

/**
 * Handle incoming Telegram webhook updates
 */
export async function POST(req: NextRequest) {
  const timer = telegramLogger.startTimer();
  let updateId: number | undefined;

  try {
    // Log configuration status
    telegramSecurity.logConfigurationStatus();

    // Get client IP
    const xForwardedFor = req.headers.get("x-forwarded-for");
    const xRealIp = req.headers.get("x-real-ip");
    const clientIP =
      xForwardedFor?.split(",")[0]?.trim() || xRealIp?.trim() || "unknown";

    // Get request body
    const body = await req.text();

    // Convert headers to plain object
    const headers: Record<string, string | undefined> = {};
    req.headers.forEach((value, key) => {
      headers[key] = value;
    });

    telegramLogger.info("Webhook request received", {
      metadata: {
        clientIP,
        contentLength: body.length,
        environment: telegramSecurity.getEnvironment(),
        userAgent: req.headers.get("user-agent"),
      },
    });

    // Security validation
    const securityResult = telegramSecurityValidator.validateRequest({
      ip: clientIP,
      headers,
      body,
      method: req.method,
      url: req.url,
    });

    if (!securityResult.allowed) {
      telegramLogger.warn("Security validation failed", {
        metadata: {
          clientIP,
          status: securityResult.status,
          message: securityResult.message,
          details: securityResult.details,
        },
      });

      return NextResponse.json(
        {
          error: securityResult.message,
          details: securityResult.details,
        },
        { status: securityResult.status }
      );
    }

    // Log security validation success
    if (securityResult.details?.bypassed) {
      telegramLogger.warn("Security validation passed with bypasses", {
        metadata: {
          warnings: securityResult.details.warnings,
          environment: telegramSecurity.getEnvironment(),
        },
      });
    } else {
      telegramLogger.debug("Security validation passed", {
        metadata: { checks: securityResult.details?.checks },
      });
    }

    // Parse and validate Telegram update
    let update: TelegramUpdate;
    try {
      const parsed = JSON.parse(body);
      const validationResult = TelegramUpdateSchema.safeParse(parsed);

      if (!validationResult.success) {
        telegramLogger.error("Invalid Telegram update format", {
          metadata: {
            errors: validationResult.error.errors,
            receivedData: parsed,
          },
        });
        return NextResponse.json(
          { error: "Invalid update format" },
          { status: 400 }
        );
      }

      update = validationResult.data;
      updateId = update.update_id;
    } catch (error) {
      telegramLogger.error("Failed to parse request body", {
        error: error instanceof Error ? error : new Error(String(error)),
      });
      return NextResponse.json({ error: "Invalid JSON" }, { status: 400 });
    }

    // Log update received
    const updateType = update.message
      ? "message"
      : update.callback_query
        ? "callback_query"
        : "unknown";
    const chatId =
      update.message?.chat?.id || update.callback_query?.message?.chat?.id;

    telegramLogger.logWebhookReceived(updateId, updateType, chatId);

    // Get bot service
    const config = telegramSecurity.getConfig();
    if (!config.botToken) {
      telegramLogger.error("Bot token not configured");
      return NextResponse.json(
        { error: "Service configuration error" },
        { status: 500 }
      );
    }

    const botService = new TelegramBotService({
      token: config.botToken,
      enablePolling: false,
    });

    // Process the update
    await botService.processUpdate(update);

    // Log successful processing
    const processingTime = timer();
    telegramLogger.info("Update processed successfully", {
      updateId,
      processingTime,
      metadata: {
        updateType,
        chatId,
        environment: config.environment,
      },
    });

    return NextResponse.json({ ok: true });
  } catch (error) {
    const processingTime = timer();

    telegramLogger.error("Error processing webhook update", {
      updateId,
      processingTime,
      error: error instanceof Error ? error : new Error(String(error)),
      metadata: {
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      },
    });

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

"use client";

import * as Sen<PERSON> from "@sentry/nextjs";
import NextError from "next/error";
import { useEffect } from "react";

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log to Sentry with additional context
    Sentry.captureException(error, {
      tags: {
        section: "global_error_boundary",
        environment: process.env.NODE_ENV,
      },
      extra: {
        digest: error.digest,
        timestamp: new Date().toISOString(),
      },
    });

    // Log error details server-side (but not to console in production)
    if (process.env.NODE_ENV === "development") {
      console.error("🚨 Global Error Boundary:", {
        message: error.message,
        stack: error.stack,
        digest: error.digest,
        timestamp: new Date().toISOString(),
      });
    }
  }, [error]);

  return (
    <html lang="en">
      <body style={{ 
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        padding: '2rem',
        textAlign: 'center',
        backgroundColor: '#f8f9fa'
      }}>
        <div style={{ maxWidth: '600px', margin: '0 auto' }}>
          <h1 style={{ color: '#dc3545', marginBottom: '1rem' }}>
            Something went wrong
          </h1>
          <p style={{ color: '#6c757d', marginBottom: '2rem' }}>
            {process.env.NODE_ENV === "development" 
              ? `Error: ${error.message}` 
              : "We're experiencing technical difficulties. Please try again later."
            }
          </p>
          <button 
            onClick={reset}
            style={{
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              padding: '0.75rem 1.5rem',
              borderRadius: '0.375rem',
              cursor: 'pointer',
              fontSize: '1rem',
              marginRight: '1rem'
            }}
          >
            Try Again
          </button>
          <button 
            onClick={() => window.location.href = '/'}
            style={{
              backgroundColor: '#6c757d',
              color: 'white',
              border: 'none',
              padding: '0.75rem 1.5rem',
              borderRadius: '0.375rem',
              cursor: 'pointer',
              fontSize: '1rem'
            }}
          >
            Go Home
          </button>
          {process.env.NODE_ENV === "development" && error.digest && (
            <p style={{ 
              marginTop: '1rem', 
              fontSize: '0.875rem', 
              color: '#6c757d',
              fontFamily: 'monospace'
            }}>
              Error ID: {error.digest}
            </p>
          )}
        </div>
      </body>
    </html>
  );
}

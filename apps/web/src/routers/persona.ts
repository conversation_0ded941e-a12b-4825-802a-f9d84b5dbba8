/**
 * Persona Generation tRPC Router
 *
 * Handles Twitter persona generation, job tracking, and persona management
 */

import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { prisma } from "../lib/db-utils";
import { createTRPCRouter, protectedProcedure } from "../lib/trpc";
import { twitterClient } from "../lib/twitter-client";
import { TwitterPersonaService } from "../lib/twitter-persona-service";

export const personaRouter = createTRPCRouter({
  /**
   * Start persona generation from Twitter handle
   */
  generateFromTwitter: protectedProcedure
    .input(
      z.object({
        twitterHandle: z.string().min(1, "Twitter handle is required"),
        tweetsTarget: z.number().min(50).max(500).default(500),
        repliesTarget: z.number().min(50).max(500).default(500),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        console.log(
          `🎭 Starting persona generation for @${input.twitterHandle} by user ${ctx.userId}`
        );

        // Clean the Twitter handle
        const cleanHandle = input.twitterHandle.replace("@", "");

        // Validate Twitter handle format
        if (!twitterClient.validateTwitterHandle(cleanHandle)) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Invalid Twitter handle format",
          });
        }

        // Check if user has reached their persona generation limit
        const userLimits = await getUserPersonaLimits(ctx.userId!);
        if (userLimits.used >= userLimits.limit && userLimits.limit !== -1) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: `You have reached your persona generation limit of ${userLimits.limit} per month. Upgrade your plan for more generations.`,
          });
        }

        // Check if user already has a pending job for this handle
        const existingJob = await prisma.personaGenerationJob.findFirst({
          where: {
            userId: ctx.userId!,
            twitterHandle: cleanHandle,
            status: {
              in: [
                "pending",
                "fetching_tweets",
                "fetching_replies",
                "analyzing",
                "generating",
              ],
            },
          },
        });

        if (existingJob) {
          throw new TRPCError({
            code: "CONFLICT",
            message: `A persona generation job for @${cleanHandle} is already in progress.`,
          });
        }

        // Verify the Twitter account exists and has enough content
        try {
          const userInfo = await twitterClient.getUserInfo(cleanHandle);
          console.log(
            `✅ Twitter account verified: @${cleanHandle} (${userInfo.followers} followers)`
          );
        } catch (error) {
          console.error(
            `❌ Failed to verify Twitter account @${cleanHandle}:`,
            error
          );
          throw new TRPCError({
            code: "NOT_FOUND",
            message: `Twitter account @${cleanHandle} not found or not accessible.`,
          });
        }

        // Start the generation process
        const jobId = await TwitterPersonaService.startGeneration({
          twitterHandle: cleanHandle,
          tweetsTarget: input.tweetsTarget,
          repliesTarget: input.repliesTarget,
          userId: ctx.userId!,
        });

        console.log(`✅ Persona generation job started: ${jobId}`);

        return {
          success: true,
          jobId,
          message: `Persona generation started for @${cleanHandle}. This may take several minutes.`,
        };
      } catch (error) {
        console.error("Persona generation error:", error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to start persona generation",
        });
      }
    }),

  /**
   * Get persona generation job status
   */
  getJobStatus: protectedProcedure
    .input(
      z.object({
        jobId: z.string().min(1, "Job ID is required"),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        const job = await TwitterPersonaService.getJobStatus(input.jobId);

        if (!job) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Persona generation job not found",
          });
        }

        // Ensure user can only access their own jobs
        if (job.userId !== ctx.userId) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "Access denied to this persona generation job",
          });
        }

        return {
          success: true,
          job: {
            id: job.id,
            twitterHandle: job.twitterHandle,
            status: job.status,
            progress: job.progress,
            tweetsCollected: job.tweetsCollected,
            repliesCollected: job.repliesCollected,
            totalTweetsTarget: job.totalTweetsTarget,
            totalRepliesTarget: job.totalRepliesTarget,
            errorMessage: job.errorMessage,
            createdAt: job.createdAt,
            updatedAt: job.updatedAt,
            completedAt: job.completedAt,
            resultPersonality: job.resultPersonality,
          },
        };
      } catch (error) {
        console.error("Get job status error:", error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get job status",
        });
      }
    }),

  /**
   * Get user's persona generation jobs
   */
  getUserJobs: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(50).default(10),
        offset: z.number().min(0).default(0),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        const jobs = await TwitterPersonaService.getUserJobs(ctx.userId!);

        const paginatedJobs = jobs.slice(
          input.offset,
          input.offset + input.limit
        );

        return {
          success: true,
          jobs: paginatedJobs.map((job) => ({
            id: job.id,
            twitterHandle: job.twitterHandle,
            status: job.status,
            progress: job.progress,
            tweetsCollected: job.tweetsCollected,
            repliesCollected: job.repliesCollected,
            errorMessage: job.errorMessage,
            createdAt: job.createdAt,
            completedAt: job.completedAt,
            resultPersonality: job.resultPersonality,
          })),
          total: jobs.length,
          hasMore: input.offset + input.limit < jobs.length,
        };
      } catch (error) {
        console.error("Get user jobs error:", error);

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get persona generation jobs",
        });
      }
    }),

  /**
   * Get user's generated personas
   */
  getUserGeneratedPersonas: protectedProcedure.query(async ({ ctx }) => {
    try {
      const personas = await prisma.personalityProfile.findMany({
        where: {
          createdById: ctx.userId!,
          isUserGenerated: true,
          isActive: true,
        },
        select: {
          id: true,
          name: true,
          description: true,
          sourceTwitterHandle: true,
          generationMetadata: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: { createdAt: "desc" },
      });

      return {
        success: true,
        personas,
      };
    } catch (error) {
      console.error("Get user generated personas error:", error);

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to get generated personas",
      });
    }
  }),

  /**
   * Delete a user-generated persona
   */
  deleteGeneratedPersona: protectedProcedure
    .input(
      z.object({
        personaId: z.string().min(1, "Persona ID is required"),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        // Verify the persona exists and belongs to the user
        const persona = await prisma.personalityProfile.findUnique({
          where: { id: input.personaId },
        });

        if (!persona) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Persona not found",
          });
        }

        if (persona.createdById !== ctx.userId || !persona.isUserGenerated) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "You can only delete your own generated personas",
          });
        }

        // Check if any users are currently using this persona
        const usersUsingPersona = await prisma.user.count({
          where: { personalityId: input.personaId },
        });

        if (usersUsingPersona > 0) {
          // Instead of deleting, mark as inactive
          await prisma.personalityProfile.update({
            where: { id: input.personaId },
            data: { isActive: false },
          });

          return {
            success: true,
            message: "Persona deactivated (it was in use by some users)",
          };
        } else {
          // Safe to delete - also clean up associated memories

          // First, delete associated memories
          try {
            const memories = await prisma.memory.findMany({
              where: {
                userId: ctx.userId!,
                metadata: {
                  path: ["personaId"],
                  equals: input.personaId,
                },
              },
              select: { id: true },
            });

            if (memories.length > 0) {
              await prisma.memory.deleteMany({
                where: {
                  id: {
                    in: memories.map((m) => m.id),
                  },
                },
              });
              console.log(
                `🗑️ Deleted ${memories.length} memories for persona ${persona.name}`
              );
            }
          } catch (memoryError) {
            console.error("Failed to delete persona memories:", memoryError);
            // Continue with persona deletion even if memory cleanup fails
          }

          // Delete the persona
          await prisma.personalityProfile.delete({
            where: { id: input.personaId },
          });

          return {
            success: true,
            message: "Persona and associated memories deleted successfully",
          };
        }
      } catch (error) {
        console.error("Delete generated persona error:", error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete persona",
        });
      }
    }),

  /**
   * Get persona generation limits for user
   */
  getPersonaLimits: protectedProcedure.query(async ({ ctx }) => {
    try {
      const limits = await getUserPersonaLimits(ctx.userId!);

      return {
        success: true,
        limits: {
          limit: limits.limit,
          used: limits.used,
          remaining:
            limits.limit === -1 ? -1 : Math.max(0, limits.limit - limits.used),
          resetDate: limits.resetDate,
        },
      };
    } catch (error) {
      console.error("Get persona limits error:", error);

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to get persona generation limits",
      });
    }
  }),

  /**
   * Get persona-related memories for a specific persona
   */
  getPersonaMemories: protectedProcedure
    .input(
      z.object({
        personaId: z.string().min(1, "Persona ID is required"),
        limit: z.number().min(1).max(50).default(20),
        memoryType: z
          .enum([
            "persona_tweet",
            "persona_reply",
            "persona_analysis",
            "persona_profile",
          ])
          .optional(),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        // Verify the persona belongs to the user
        const persona = await prisma.personalityProfile.findFirst({
          where: {
            id: input.personaId,
            createdById: ctx.userId,
            isUserGenerated: true,
          },
        });

        if (!persona) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Persona not found or not accessible",
          });
        }

        // Import mem0Service here to avoid circular imports
        const { mem0Service } = await import("../lib/mem0-service");

        // Search for persona memories
        const searchOptions: any = {
          limit: input.limit,
          metadata: {
            personaId: input.personaId,
          },
        };

        if (input.memoryType) {
          searchOptions.metadata.contentType = input.memoryType;
        }

        const memories = await mem0Service.searchMemories(
          ctx.userId!,
          searchOptions
        );

        return {
          success: true,
          memories: memories.map((memory) => ({
            id: memory.id,
            content: memory.content,
            metadata: memory.metadata,
            similarity: memory.similarity,
            createdAt: memory.createdAt,
          })),
          persona: {
            id: persona.id,
            name: persona.name,
            sourceTwitterHandle: persona.sourceTwitterHandle,
          },
        };
      } catch (error) {
        console.error("Get persona memories error:", error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get persona memories",
        });
      }
    }),

  /**
   * Get memory statistics for a persona
   */
  getPersonaMemoryStats: protectedProcedure
    .input(
      z.object({
        personaId: z.string().min(1, "Persona ID is required"),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        // Verify the persona belongs to the user
        const persona = await prisma.personalityProfile.findFirst({
          where: {
            id: input.personaId,
            createdById: ctx.userId,
            isUserGenerated: true,
          },
          include: {
            generationJobs: {
              select: {
                memoriesStored: true,
                memoryStorageProgress: true,
                memoryErrors: true,
                completedAt: true,
              },
              orderBy: { createdAt: "desc" },
              take: 1,
            },
          },
        });

        if (!persona) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Persona not found or not accessible",
          });
        }

        const latestJob = persona.generationJobs[0];

        return {
          success: true,
          stats: {
            memoriesStored: latestJob?.memoriesStored || 0,
            memoryStorageProgress: latestJob?.memoryStorageProgress || 0,
            hasMemoryErrors: !!latestJob?.memoryErrors,
            memoryErrors: latestJob?.memoryErrors,
            lastGeneratedAt: latestJob?.completedAt,
          },
          persona: {
            id: persona.id,
            name: persona.name,
            sourceTwitterHandle: persona.sourceTwitterHandle,
          },
        };
      } catch (error) {
        console.error("Get persona memory stats error:", error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get persona memory statistics",
        });
      }
    }),

  /**
   * Delete all memories for a specific persona
   */
  deletePersonaMemories: protectedProcedure
    .input(
      z.object({
        personaId: z.string().min(1, "Persona ID is required"),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        // Verify the persona belongs to the user
        const persona = await prisma.personalityProfile.findFirst({
          where: {
            id: input.personaId,
            createdById: ctx.userId,
            isUserGenerated: true,
          },
        });

        if (!persona) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Persona not found or not accessible",
          });
        }

        // Get all memories for this persona
        const memories = await prisma.memory.findMany({
          where: {
            userId: ctx.userId!,
            metadata: {
              path: ["personaId"],
              equals: input.personaId,
            },
          },
          select: { id: true },
        });

        if (memories.length === 0) {
          return {
            success: true,
            message: "No memories found for this persona",
            deletedCount: 0,
          };
        }

        // Delete memories from database
        await prisma.memory.deleteMany({
          where: {
            id: {
              in: memories.map((m) => m.id),
            },
          },
        });

        return {
          success: true,
          message: `Successfully deleted ${memories.length} memories for persona ${persona.name}`,
          deletedCount: memories.length,
        };
      } catch (error) {
        console.error("Delete persona memories error:", error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete persona memories",
        });
      }
    }),
});

/**
 * Helper function to get user's persona generation limits
 */
async function getUserPersonaLimits(userId: string) {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      plan: {
        include: {
          features: true,
        },
      },
    },
  });

  if (!user) {
    throw new Error("User not found");
  }

  // Get persona generation limit from plan features
  const personaFeature = user.plan.features.find(
    (feature) => feature.feature === "PERSONA_GENERATIONS"
  );

  const limit = personaFeature?.limit ?? 0;

  // Count current month's usage
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const endOfMonth = new Date(
    now.getFullYear(),
    now.getMonth() + 1,
    0,
    23,
    59,
    59
  );

  const used = await prisma.personaGenerationJob.count({
    where: {
      userId,
      status: "completed",
      completedAt: {
        gte: startOfMonth,
        lte: endOfMonth,
      },
    },
  });

  return {
    limit,
    used,
    resetDate: new Date(now.getFullYear(), now.getMonth() + 1, 1), // First day of next month
  };
}

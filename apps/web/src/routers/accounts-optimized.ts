/**
 * Optimized Accounts Router for BuddyChip tRPC API
 *
 * This router is optimized with service layer integration, comprehensive
 * error handling, performance monitoring, and caching strategies.
 */

import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { validateCUID } from "../lib/security-utils";
import { createTRPCRouter, protectedProcedure } from "../lib/trpc";
import { getServiceRegistry } from "../services";
import { PrismaClient } from "../../prisma/generated/client";

// Input validation schemas
const addAccountSchema = z.object({
  handle: z
    .string()
    .min(1, "Twitter handle is required")
    .max(15, "Twitter handle too long"),
  syncSettings: z
    .object({
      syncMentions: z.boolean().default(true),
      syncUserTweets: z.boolean().default(false),
      syncReplies: z.boolean().default(false),
      syncRetweets: z.boolean().default(true),
    })
    .optional(),
});

const accountIdSchema = z.object({
  accountId: z.string().refine(validateCUID, "Invalid account ID"),
});

const toggleStatusSchema = z.object({
  accountId: z.string().refine(validateCUID, "Invalid account ID"),
  isActive: z.boolean(),
});

const updateSyncSettingsSchema = z.object({
  accountId: z.string().refine(validateCUID, "Invalid account ID"),
  syncSettings: z.object({
    syncMentions: z.boolean(),
    syncUserTweets: z.boolean(),
    syncReplies: z.boolean(),
    syncRetweets: z.boolean(),
  }),
});

const discoverAccountsSchema = z.object({
  targetSector: z.string().optional(),
  seedAccount: z.string().optional(),
  limit: z.number().min(1).max(50).default(10),
});

const sectorRecommendationsSchema = z.object({
  sector: z.string().min(1, "Sector is required"),
  limit: z.number().min(1).max(20).default(5),
});

/**
 * Helper function to handle service results and convert to tRPC responses
 */
function handleServiceResult<T>(result: any, operation: string): T {
  if (result.success) {
    return result.data;
  }

  // Log the error for monitoring with structured context
  console.error(`❌ ${operation} failed:`, {
    error: result.error,
    errorCode: result.errorCode,
    operation,
    timestamp: new Date().toISOString()
  });

  // Determine appropriate tRPC error code based on structured error codes first
  let code: "BAD_REQUEST" | "NOT_FOUND" | "FORBIDDEN" | "CONFLICT" | "INTERNAL_SERVER_ERROR" = "INTERNAL_SERVER_ERROR";

  // Check for structured error codes first
  if (result.errorCode) {
    switch (result.errorCode) {
      case "INVALID_INPUT":
      case "VALIDATION_ERROR":
        code = "BAD_REQUEST";
        break;
      case "NOT_FOUND":
      case "ACCESS_DENIED":
        code = "NOT_FOUND";
        break;
      case "LIMIT_EXCEEDED":
      case "FORBIDDEN":
        code = "FORBIDDEN";
        break;
      case "ALREADY_EXISTS":
      case "CONFLICT":
        code = "CONFLICT";
        break;
      default:
        code = "INTERNAL_SERVER_ERROR";
    }
  } else {
    // Fall back to string matching for legacy error handling
    const errorMessage = typeof result.error === 'string' ? result.error : String(result.error);
    
    if (errorMessage.includes("Invalid")) {
      code = "BAD_REQUEST";
    } else if (
      errorMessage.includes("not found") ||
      errorMessage.includes("access denied")
    ) {
      code = "NOT_FOUND";
    } else if (
      errorMessage.includes("limit exceeded") ||
      errorMessage.includes("forbidden")
    ) {
      code = "FORBIDDEN";
    } else if (
      errorMessage.includes("already monitoring") ||
      errorMessage.includes("conflict")
    ) {
      code = "CONFLICT";
    } else if (errorMessage.includes("unavailable")) {
      code = "INTERNAL_SERVER_ERROR"; // SERVICE_UNAVAILABLE is not a valid tRPC error code
    }
  }

  throw new TRPCError({
    code,
    message: result.error || "Operation failed",
  });
}

export const accountsOptimizedRouter = createTRPCRouter({
  /**
   * Get all monitored accounts for the current user
   */
  getMonitored: protectedProcedure.query(async ({ ctx }) => {
    const registry = getServiceRegistry(ctx.prisma as PrismaClient);
    const accountService = registry.getAccountService();

    const result = await accountService.getMonitoredAccounts(ctx.userId!);
    return handleServiceResult(result, "getMonitoredAccounts");
  }),

  /**
   * Add a new Twitter account to monitor
   */
  add: protectedProcedure
    .input(addAccountSchema)
    .mutation(async ({ input, ctx }) => {
      const registry = getServiceRegistry(ctx.prisma as PrismaClient);
      const accountService = registry.getAccountService();

      const result = await accountService.addAccount(
        ctx.userId!,
        input.handle,
        input.syncSettings
      );

      return handleServiceResult(result, "addAccount");
    }),

  /**
   * Remove a monitored account
   */
  remove: protectedProcedure
    .input(accountIdSchema)
    .mutation(async ({ input, ctx }) => {
      const registry = getServiceRegistry(ctx.prisma as PrismaClient);
      const accountService = registry.getAccountService();

      const result = await accountService.removeAccount(
        ctx.userId!,
        input.accountId
      );
      return handleServiceResult(result, "removeAccount");
    }),

  /**
   * Toggle monitoring status for an account
   */
  toggleStatus: protectedProcedure
    .input(toggleStatusSchema)
    .mutation(async ({ input, ctx }) => {
      const registry = getServiceRegistry(ctx.prisma as PrismaClient);
      const accountService = registry.getAccountService();

      const result = await accountService.toggleAccountStatus(
        ctx.userId!,
        input.accountId,
        input.isActive
      );

      return handleServiceResult(result, "toggleAccountStatus");
    }),

  /**
   * Update sync settings for a monitored account
   */
  updateSyncSettings: protectedProcedure
    .input(updateSyncSettingsSchema)
    .mutation(async ({ input, ctx }) => {
      const registry = getServiceRegistry(ctx.prisma as PrismaClient);
      const accountService = registry.getAccountService();

      const result = await accountService.updateSyncSettings(
        ctx.userId!,
        input.accountId,
        input.syncSettings
      );

      return handleServiceResult(result, "updateSyncSettings");
    }),

  /**
   * Discover smart accounts to monitor based on crypto sector and influence
   */
  discoverSmartAccounts: protectedProcedure
    .input(discoverAccountsSchema)
    .mutation(async ({ input, ctx }) => {
      const registry = getServiceRegistry(ctx.prisma as PrismaClient);
      const accountService = registry.getAccountService();

      const result = await accountService.discoverSmartAccounts(
        ctx.userId!,
        input
      );
      return handleServiceResult(result, "discoverSmartAccounts");
    }),

  /**
   * Get sector-based account recommendations
   */
  getSectorRecommendations: protectedProcedure
    .input(sectorRecommendationsSchema)
    .query(async ({ input, ctx }) => {
      const registry = getServiceRegistry(ctx.prisma as PrismaClient);
      const accountService = registry.getAccountService();

      const result = await accountService.getSectorRecommendations(
        ctx.userId!,
        input.sector,
        input.limit
      );

      return handleServiceResult(result, "getSectorRecommendations");
    }),

  /**
   * Validate account ownership (utility endpoint)
   */
  validateOwnership: protectedProcedure
    .input(accountIdSchema)
    .query(async ({ input, ctx }) => {
      const registry = getServiceRegistry(ctx.prisma as PrismaClient);
      const accountService = registry.getAccountService();

      const isOwner = await accountService.validateAccountOwnership(
        ctx.userId!,
        input.accountId
      );
      return { isOwner };
    }),

  /**
   * Get account statistics (counts, usage, etc.)
   */
  getAccountStats: protectedProcedure.query(async ({ ctx }) => {
    const registry = getServiceRegistry(ctx.prisma as PrismaClient);
    const accountService = registry.getAccountService();

    // Get basic account info
    const accountsResult = await accountService.getMonitoredAccounts(
      ctx.userId!
    );

    if (!accountsResult.success) {
      return handleServiceResult(accountsResult, "getAccountStats");
    }

    const accounts = accountsResult.data.accounts;

    // Calculate statistics
    const stats = {
      totalAccounts: accounts.length,
      activeAccounts: accounts.filter((acc: any) => acc.isActive).length,
      inactiveAccounts: accounts.filter((acc: any) => !acc.isActive).length,
      totalMentions: accounts.reduce(
        (sum: number, acc: any) => sum + (acc.mentionsCount || 0),
        0
      ),
      averageMentionsPerAccount:
        accounts.length > 0
          ? accounts.reduce(
              (sum: number, acc: any) => sum + (acc.mentionsCount || 0),
              0
            ) / accounts.length
          : 0,
      mostActivePlatform: "twitter", // Currently only support Twitter
      oldestAccount:
        accounts.length > 0
          ? accounts.reduce((oldest: any, acc: any) =>
              new Date(acc.createdAt) < new Date(oldest.createdAt)
                ? acc
                : oldest
            ).createdAt
          : null,
      newestAccount:
        accounts.length > 0
          ? accounts.reduce((newest: any, acc: any) =>
              new Date(acc.createdAt) > new Date(newest.createdAt)
                ? acc
                : newest
            ).createdAt
          : null,
    };

    return { success: true, stats };
  }),
});

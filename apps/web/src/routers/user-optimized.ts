/**
 * Optimized User Router for BuddyChip tRPC API
 *
 * This router is optimized with service layer integration, comprehensive
 * error handling, performance monitoring, and caching strategies.
 */

import { TRPCError } from "@trpc/server";
import { z } from "zod";
import {
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
} from "../lib/trpc";
import { getServiceRegistry } from "../services";
import { PrismaClient } from "../../prisma/generated/client";

// Input validation schemas
const updateProfileSchema = z.object({
  name: z.string().min(1).max(100).optional(),
});

const featureCheckSchema = z.object({
  feature: z.enum([
    "AI_CALLS",
    "IMAGE_GENERATIONS",
    "MONITORED_ACCOUNTS",
    "MENTIONS_PER_MONTH",
    "STORAGE_GB",
    "TEAM_MEMBERS",
    "COOKIE_API_CALLS",
  ]),
});

const logUsageSchema = z.object({
  feature: z.enum([
    "AI_CALLS",
    "IMAGE_GENERATIONS",
    "MONITORED_ACCOUNTS",
    "MENTIONS_PER_MONTH",
    "STORAGE_GB",
    "TEAM_MEMBERS",
    "COOKIE_API_CALLS",
  ]),
  amount: z.number().min(1).max(1000).default(1),
  metadata: z.record(z.any()).optional(),
});

const updatePersonalitySchema = z.object({
  personalityId: z.string().nullable(),
  customSystemPrompt: z.string().max(2000).optional(),
  useFirstPerson: z.boolean().optional(),
});

const updateSelectedModelSchema = z.object({
  modelId: z.string().optional(),
});

/**
 * Helper function to handle service results and convert to tRPC responses
 */
function handleServiceResult<T>(result: any, operation: string): T {
  if (result.success) {
    return result.data;
  }

  // Log the error for monitoring
  console.error(`❌ ${operation} failed:`, result.error);

  // Determine appropriate tRPC error code
  let code: any = "INTERNAL_SERVER_ERROR";

  if (result.error?.includes("Invalid")) {
    code = "BAD_REQUEST";
  } else if (result.error?.includes("not found")) {
    code = "NOT_FOUND";
  } else if (result.error?.includes("unauthorized")) {
    code = "UNAUTHORIZED";
  } else if (
    result.error?.includes("limit exceeded") ||
    result.error?.includes("forbidden")
  ) {
    code = "FORBIDDEN";
  } else if (result.error?.includes("unavailable")) {
    code = "SERVICE_UNAVAILABLE";
  }

  throw new TRPCError({
    code,
    message: result.error || "Operation failed",
  });
}

export const userOptimizedRouter = createTRPCRouter({
  /**
   * Get current user profile with plan and usage data
   */
  getProfile: protectedProcedure.query(async ({ ctx }) => {
    const registry = getServiceRegistry(ctx.prisma as PrismaClient);
    const userService = registry.getUserService();

    const result = await userService.getProfile(ctx.userId!);
    return handleServiceResult(result, "getProfile");
  }),

  /**
   * Get user's current usage for all features
   */
  getUsage: protectedProcedure.query(async ({ ctx }) => {
    const registry = getServiceRegistry(ctx.prisma as PrismaClient);
    const userService = registry.getUserService();

    const result = await userService.getUsage(ctx.userId!);
    return handleServiceResult(result, "getUsage");
  }),

  /**
   * Check if user can perform a specific action
   */
  canUseFeature: protectedProcedure
    .input(featureCheckSchema)
    .query(async ({ input, ctx }) => {
      const registry = getServiceRegistry(ctx.prisma as PrismaClient);
      const userService = registry.getUserService();

      const result = await userService.canUseFeature(
        ctx.userId!,
        input.feature
      );
      return handleServiceResult(result, "canUseFeature");
    }),

  /**
   * Update user profile information
   */
  updateProfile: protectedProcedure
    .input(updateProfileSchema)
    .mutation(async ({ input, ctx }) => {
      const registry = getServiceRegistry(ctx.prisma as PrismaClient);
      const userService = registry.getUserService();

      const result = await userService.updateProfile(ctx.userId!, input);
      return handleServiceResult(result, "updateProfile");
    }),

  /**
   * Log feature usage (for tracking rate limits)
   */
  logUsage: protectedProcedure
    .input(logUsageSchema)
    .mutation(async ({ input, ctx }) => {
      const registry = getServiceRegistry(ctx.prisma as PrismaClient);
      const userService = registry.getUserService();

      const result = await userService.logUsage(
        ctx.userId!,
        input.feature,
        input.amount,
        input.metadata
      );

      return handleServiceResult(result, "logUsage");
    }),

  /**
   * Get current user's personality settings
   */
  getPersonality: protectedProcedure.query(async ({ ctx }) => {
    const registry = getServiceRegistry(ctx.prisma as PrismaClient);
    const userService = registry.getUserService();

    const result = await userService.getPersonality(ctx.userId!);
    return handleServiceResult(result, "getPersonality");
  }),

  /**
   * Get all available personality profiles
   */
  getPersonalities: publicProcedure.query(async ({ ctx }) => {
    const registry = getServiceRegistry(ctx.prisma as PrismaClient);
    const userService = registry.getUserService();

    const result = await userService.getAvailablePersonalities();
    return handleServiceResult(result, "getPersonalities");
  }),

  /**
   * Update user's personality settings
   */
  updatePersonality: protectedProcedure
    .input(updatePersonalitySchema)
    .mutation(async ({ input, ctx }) => {
      const registry = getServiceRegistry(ctx.prisma as PrismaClient);
      const userService = registry.getUserService();

      const result = await userService.updatePersonality(ctx.userId!, input);
      return handleServiceResult(result, "updatePersonality");
    }),

  /**
   * Get available AI models
   */
  getAIModels: publicProcedure.query(async ({ ctx }) => {
    const registry = getServiceRegistry(ctx.prisma as PrismaClient);
    const userService = registry.getUserService();

    const result = await userService.getAvailableModels();
    return handleServiceResult(result, "getAIModels");
  }),

  /**
   * Get user's selected AI model
   */
  getSelectedModel: protectedProcedure.query(async ({ ctx }) => {
    const registry = getServiceRegistry(ctx.prisma as PrismaClient);
    const userService = registry.getUserService();

    const result = await userService.getSelectedModel(ctx.userId!);
    return handleServiceResult(result, "getSelectedModel");
  }),

  /**
   * Update user's selected AI model
   */
  updateSelectedModel: protectedProcedure
    .input(updateSelectedModelSchema)
    .mutation(async ({ input, ctx }) => {
      const registry = getServiceRegistry(ctx.prisma as PrismaClient);
      const userService = registry.getUserService();

      const result = await userService.updateSelectedModel(
        ctx.userId!,
        input.modelId
      );
      return handleServiceResult(result, "updateSelectedModel");
    }),

  /**
   * Get comprehensive user dashboard data
   */
  getDashboardData: protectedProcedure.query(async ({ ctx }) => {
    const registry = getServiceRegistry(ctx.prisma as PrismaClient);
    const userService = registry.getUserService();
    const accountService = registry.getAccountService();
    const subscriptionService = registry.getSubscriptionService();

    // Fetch data in parallel for better performance
    const [profileResult, usageResult, accountsResult, subscriptionResult] =
      await Promise.all([
        userService.getProfile(ctx.userId!),
        userService.getUsage(ctx.userId!),
        accountService.getMonitoredAccounts(ctx.userId!),
        subscriptionService.getUsageStats(ctx.userId!),
      ]);

    // Handle any failures gracefully
    const dashboardData = {
      profile: profileResult.success ? profileResult.data : null,
      usage: usageResult.success ? usageResult.data : [],
      accounts: accountsResult.success ? accountsResult.data : { accounts: [] },
      subscription: subscriptionResult.success ? subscriptionResult.data : null,
      timestamp: new Date().toISOString(),
    };

    return { success: true, data: dashboardData };
  }),

  /**
   * Get user activity summary
   */
  getActivitySummary: protectedProcedure.query(async ({ ctx }) => {
    const registry = getServiceRegistry(ctx.prisma as PrismaClient);
    const userService = registry.getUserService();
    const subscriptionService = registry.getSubscriptionService();

    // Get usage stats for activity summary
    const usageResult = await subscriptionService.getUsageStats(ctx.userId!);

    if (!usageResult.success) {
      return handleServiceResult(usageResult, "getActivitySummary");
    }

    const usageData = usageResult.data;

    // Calculate activity metrics
    const totalUsage = usageData.usage.reduce(
      (sum: number, u: any) => sum + u.currentUsage,
      0
    );
    const activeFeatures = usageData.usage.filter(
      (u: any) => u.currentUsage > 0
    ).length;
    const atRiskFeatures = usageData.usage.filter(
      (u: any) => u.usagePercentage > 75
    ).length;

    const activitySummary = {
      totalUsage,
      activeFeatures,
      atRiskFeatures,
      totalFeatures: usageData.totalFeatures,
      healthScore: Math.max(
        0,
        100 - usageData.exceededFeatures * 25 - atRiskFeatures * 10
      ),
      recommendations: [
        ...(usageData.exceededFeatures > 0
          ? ["Consider upgrading your plan to increase limits"]
          : []),
        ...(atRiskFeatures > 0
          ? ["Some features are approaching their limits"]
          : []),
        ...(activeFeatures === 0
          ? ["Start using BuddyChip features to monitor your accounts"]
          : []),
      ],
      timestamp: new Date().toISOString(),
    };

    return { success: true, data: activitySummary };
  }),

  /**
   * Get user preferences and settings
   */
  getPreferences: protectedProcedure.query(async ({ ctx }) => {
    const registry = getServiceRegistry(ctx.prisma as PrismaClient);
    const userService = registry.getUserService();

    // Get personality and model preferences
    const [personalityResult, modelResult] = await Promise.all([
      userService.getPersonality(ctx.userId!),
      userService.getSelectedModel(ctx.userId!),
    ]);

    const preferences = {
      personality: personalityResult.success ? personalityResult.data : null,
      selectedModel: modelResult.success ? modelResult.data : null,
      timestamp: new Date().toISOString(),
    };

    return { success: true, data: preferences };
  }),
});

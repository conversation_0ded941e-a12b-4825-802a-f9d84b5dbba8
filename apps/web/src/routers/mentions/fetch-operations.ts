/**
 * Fetch Operations Module
 *
 * Handles tweet fetching and mention discovery operations including:
 * - Tweet fetching from URLs
 * - Account tweet retrieval
 * - Mention discovery and filtering
 * - Tweet validation and parsing
 */

import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { performanceMonitor } from "../../lib/performance-monitor";
import { protectedProcedure } from "../../lib/trpc";
import { getServiceFactory } from "../../services/service-factory";
import { commonInputSchemas, handleTRPCError } from "./shared";

export const fetchOperations = {
  /**
   * Create mention from Twitter/X URL (for Quick Reply feature)
   */
  createFromUrl: protectedProcedure
    .input(
      z.object({
        url: z.string().url("Please provide a valid URL"),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const serviceFactory = getServiceFactory(ctx.prisma);
        const twitterService = serviceFactory.createTwitterService();
        const mentionService = serviceFactory.createMentionService();

        const result = await mentionService.createMentionFromUrl(
          ctx.userId!,
          input.url
        );

        if (!result.success) {
          throw new TRPCError({
            code: result.error?.includes("not found")
              ? "NOT_FOUND"
              : "BAD_REQUEST",
            message: result.error || "Failed to create mention",
          });
        }

        return result.data;
      } catch (error) {
        handleTRPCError(error, "create mention from URL", {
          userId: ctx.userId,
          url: input.url,
        });
      }
    }),

  /**
   * Get tweets for a monitored account with advanced filtering
   */
  getAccountTweets: protectedProcedure
    .input(
      z.object({
        accountId: z.string(),
        cursor: z.string().optional(),
        limit: z.number().min(1).max(100).default(20),
        // Tweet type filters
        tweetTypes: z
          .object({
            mentions: z.boolean().default(true),
            userTweets: z.boolean().default(true),
            replies: z.boolean().default(true),
            retweets: z.boolean().default(true),
          })
          .optional(),
        // Additional filters
        hasAIResponse: z.boolean().optional(),
        archived: z.boolean().default(false),
        // Date range filters
        startDate: z.date().optional(),
        endDate: z.date().optional(),
        // Sorting
        sortBy: z
          .enum(["createdAt", "bullishScore", "importanceScore"])
          .default("createdAt"),
        sortOrder: z.enum(["asc", "desc"]).default("desc"),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        const serviceFactory = getServiceFactory(ctx.prisma);
        const mentionService = serviceFactory.createMentionService();

        // Verify account ownership
        const account = await ctx.prisma.monitoredAccount.findFirst({
          where: {
            id: input.accountId,
            userId: ctx.userId!,
          },
        });

        if (!account) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Account not found or access denied",
          });
        }

        // Use mention service to get filtered tweets
        const result = await mentionService.getMentions(ctx.userId!, {
          limit: input.limit,
          offset: input.cursor,
          filters: {
            accountId: input.accountId,
            hasAIResponse: input.hasAIResponse,
            archived: input.archived,
            startDate: input.startDate,
            endDate: input.endDate,
            tweetTypes: input.tweetTypes || {
              mentions: true,
              userTweets: true,
              replies: true,
              retweets: true,
            },
          },
          sortBy: input.sortBy,
          sortOrder: input.sortOrder,
          includeAccount: true,
          includeResponses: true,
        });

        if (!result.success) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: result.error || "Failed to fetch account tweets",
          });
        }

        return {
          success: true,
          tweets: result.data!.mentions.map((mention: any) => ({
            id: mention.id,
            content: mention.content,
            authorHandle: mention.authorHandle,
            authorName: mention.authorName,
            authorAvatar: mention.authorAvatar,
            tweetUrl: mention.tweetUrl,
            createdAt: mention.createdAt,
            bullishScore: mention.bullishScore,
            importanceScore: mention.importanceScore,
            keywords: mention.keywords,
            hasAIResponse: mention.hasAIResponse,
            tweetType:
              mention.account?.id === input.accountId ? "userTweet" : "mention",
            account: mention.account,
          })),
          nextCursor: result.data!.nextCursor,
          hasNextPage: result.data!.hasNextPage,
          filters: {
            applied: input.tweetTypes || {
              mentions: true,
              userTweets: true,
              replies: true,
              retweets: true,
            },
            account: {
              id: account.id,
              handle: account.twitterHandle,
              displayName: account.displayName,
            },
          },
        };
      } catch (error) {
        handleTRPCError(error, "get account tweets", {
          userId: ctx.userId,
          accountId: input.accountId,
        });
      }
    }),

  /**
   * Validate Twitter URL and extract basic info
   */
  validateTwitterUrl: protectedProcedure
    .input(
      z.object({
        url: z.string().url("Please provide a valid URL"),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        const serviceFactory = getServiceFactory(ctx.prisma);
        const twitterService = serviceFactory.createTwitterService();

        const isValid = twitterService.validateTwitterUrl(input.url);

        if (!isValid) {
          return {
            success: false,
            valid: false,
            error: "Invalid Twitter URL format",
          };
        }

        const tweetId = twitterService.extractTweetId(input.url);
        const normalizedUrl = twitterService.normalizeTwitterUrl(input.url);

        return {
          success: true,
          valid: true,
          tweetId,
          normalizedUrl,
          message: "Valid Twitter URL",
        };
      } catch (error) {
        handleTRPCError(error, "validate Twitter URL", {
          userId: ctx.userId,
          url: input.url,
        });
      }
    }),

  /**
   * Get tweet preview from URL without creating mention
   */
  getTweetPreview: protectedProcedure
    .input(
      z.object({
        url: z.string().url("Please provide a valid URL"),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        const serviceFactory = getServiceFactory(ctx.prisma);
        const twitterService = serviceFactory.createTwitterService();

        const result = await twitterService.getTweetFromUrl(input.url);

        if (!result.success) {
          throw new TRPCError({
            code: result.error?.includes("not found")
              ? "NOT_FOUND"
              : "BAD_REQUEST",
            message: result.error || "Failed to fetch tweet",
          });
        }

        return {
          success: true,
          tweet: result.data,
          message: "Tweet preview retrieved successfully",
        };
      } catch (error) {
        handleTRPCError(error, "get tweet preview", {
          userId: ctx.userId,
          url: input.url,
        });
      }
    }),

  /**
   * Get user info from Twitter handle
   */
  getUserInfo: protectedProcedure
    .input(
      z.object({
        handle: z.string().min(1, "Handle is required"),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        const serviceFactory = getServiceFactory(ctx.prisma);
        const twitterService = serviceFactory.createTwitterService();

        // Validate handle format
        if (!twitterService.validateHandle(input.handle)) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Invalid Twitter handle format",
          });
        }

        const result = await twitterService.getUserInfo(input.handle);

        if (!result.success) {
          throw new TRPCError({
            code: result.error?.includes("not found")
              ? "NOT_FOUND"
              : "INTERNAL_SERVER_ERROR",
            message: result.error || "Failed to fetch user info",
          });
        }

        return {
          success: true,
          user: result.data,
          message: "User info retrieved successfully",
        };
      } catch (error) {
        handleTRPCError(error, "get user info", {
          userId: ctx.userId,
          handle: input.handle,
        });
      }
    }),

  /**
   * Check Twitter API health
   */
  checkApiHealth: protectedProcedure.query(async ({ ctx }) => {
    try {
      const serviceFactory = getServiceFactory(ctx.prisma);
      const twitterService = serviceFactory.createTwitterService();

      const result = await twitterService.checkTwitterApiHealth();

      return {
        success: result.success,
        healthy: result.data?.healthy || false,
        message:
          result.data?.message || result.error || "API health check completed",
      };
    } catch (error) {
      handleTRPCError(error, "check API health", {
        userId: ctx.userId,
      });
    }
  }),
};

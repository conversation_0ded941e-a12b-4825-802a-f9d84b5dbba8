/**
 * Monitoring Module
 *
 * Handles account monitoring and sync operations including:
 * - Account sync management
 * - Mention syncing from Twitter
 * - Monitoring status and statistics
 * - Sync scheduling and automation
 */

import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { MentionSyncService } from "../../lib/mention-sync-service";
import { createTRPCRouter, protectedProcedure } from "../../lib/trpc";
import { handleTRPCError } from "./shared";

export const monitoring = createTRPCRouter({
  /**
   * Sync mentions for a specific monitored account
   */
  syncAccount: protectedProcedure
    .input(
      z.object({
        accountId: z.string().cuid("Invalid account ID"),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        console.log(
          `🔄 Starting mention sync for account ${input.accountId} by user ${ctx.userId}`
        );

        // Create sync service instance with Prisma client
        const syncService = new MentionSyncService(ctx.prisma);

        // Sync mentions for the specific account
        const result = await syncService.syncAccountMentions(
          input.accountId,
          ctx.userId!
        );

        if (!result.success) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: result.error || "Failed to sync mentions",
          });
        }

        return {
          success: true,
          result: result,
          message: `Synced ${result.newMentions} new mentions for @${result.accountHandle}`,
        };
      } catch (error) {
        handleTRPCError(error, "sync account mentions", {
          userId: ctx.userId,
          accountId: input.accountId,
        });
      }
    }),

  /**
   * Sync mentions for all monitored accounts of the current user
   */
  syncAllAccounts: protectedProcedure.mutation(async ({ ctx }) => {
    try {
      console.log(`🚀 Starting bulk mention sync for user ${ctx.userId}`);

      // Create sync service instance with Prisma client
      const syncService = new MentionSyncService(ctx.prisma);

      // Sync mentions for all user's accounts
      const result = await syncService.syncAllUserAccounts(ctx.userId!);

      return {
        success: result.success,
        totalNewMentions: result.totalNewMentions,
        accountResults: result.results,
        errors: result.errors,
        message: result.success
          ? `Successfully synced ${result.totalNewMentions} new mentions across ${result.results.length} accounts`
          : `Sync completed with ${result.errors.length} errors`,
      };
    } catch (error) {
      handleTRPCError(error, "sync all accounts", {
        userId: ctx.userId,
      });
    }
  }),

  /**
   * Get sync statistics for the current user
   */
  getSyncStats: protectedProcedure.query(async ({ ctx }) => {
    try {
      // Create sync service instance with Prisma client
      const syncService = new MentionSyncService(ctx.prisma);

      // Get sync statistics
      const stats = await syncService.getSyncStats(ctx.userId!);

      return {
        success: true,
        stats: stats,
      };
    } catch (error) {
      handleTRPCError(error, "get sync statistics", {
        userId: ctx.userId,
      });
    }
  }),

  /**
   * Get monitoring status for all accounts
   */
  getMonitoringStatus: protectedProcedure.query(async ({ ctx }) => {
    try {
      console.log(`📊 Getting monitoring status for user ${ctx.userId}`);

      // Get all monitored accounts with their sync status
      const accounts = await ctx.prisma.monitoredAccount.findMany({
        where: {
          userId: ctx.userId!,
        },
        include: {
          _count: {
            select: {
              mentions: {
                where: {
                  archived: false,
                },
              },
            },
          },
        },
        orderBy: {
          lastCheckedAt: "desc",
        },
      });

      // Calculate overall statistics
      const totalAccounts = accounts.length;
      const activeAccounts = accounts.filter((a) => a.isActive).length;
      const inactiveAccounts = totalAccounts - activeAccounts;
      const totalMentions = accounts.reduce(
        (sum, account) => sum + account._count.mentions,
        0
      );

      // Find accounts that need sync (haven't been checked recently)
      const staleSyncThreshold = new Date(Date.now() - 60 * 60 * 1000); // 1 hour ago
      const accountsNeedingSync = accounts.filter(
        (a) =>
          a.isActive &&
          (!a.lastCheckedAt || a.lastCheckedAt < staleSyncThreshold)
      );

      // Get recent sync activity
      const recentSyncActivity = accounts
        .filter((a) => a.lastCheckedAt)
        .sort((a, b) => b.lastCheckedAt!.getTime() - a.lastCheckedAt!.getTime())
        .slice(0, 5)
        .map((account) => ({
          id: account.id,
          handle: account.twitterHandle,
          displayName: account.displayName,
          lastCheckedAt: account.lastCheckedAt,
          mentionCount: account._count.mentions,
          isActive: account.isActive,
        }));

      return {
        success: true,
        status: {
          overview: {
            totalAccounts,
            activeAccounts,
            inactiveAccounts,
            totalMentions,
            accountsNeedingSync: accountsNeedingSync.length,
          },
          accounts: accounts.map((account) => ({
            id: account.id,
            handle: account.twitterHandle,
            displayName: account.displayName,
            isActive: account.isActive,
            lastCheckedAt: account.lastCheckedAt,
            mentionCount: account._count.mentions,
            syncSettings: {
              syncMentions: account.syncMentions,
              syncUserTweets: account.syncUserTweets,
              syncReplies: account.syncReplies,
              syncRetweets: account.syncRetweets,
            },
            needsSync:
              account.isActive &&
              (!account.lastCheckedAt ||
                account.lastCheckedAt < staleSyncThreshold),
          })),
          recentActivity: recentSyncActivity,
        },
      };
    } catch (error) {
      handleTRPCError(error, "get monitoring status", {
        userId: ctx.userId,
      });
    }
  }),

  /**
   * Update account sync settings
   */
  updateSyncSettings: protectedProcedure
    .input(
      z.object({
        accountId: z.string().cuid("Invalid account ID"),
        settings: z.object({
          syncMentions: z.boolean().optional(),
          syncUserTweets: z.boolean().optional(),
          syncReplies: z.boolean().optional(),
          syncRetweets: z.boolean().optional(),
        }),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        console.log(`⚙️ Updating sync settings for account ${input.accountId}`);

        // Verify account ownership
        const account = await ctx.prisma.monitoredAccount.findFirst({
          where: {
            id: input.accountId,
            userId: ctx.userId!,
          },
        });

        if (!account) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Account not found or access denied",
          });
        }

        // Update sync settings
        const updatedAccount = await ctx.prisma.monitoredAccount.update({
          where: {
            id: input.accountId,
          },
          data: {
            syncMentions: input.settings.syncMentions ?? account.syncMentions,
            syncUserTweets:
              input.settings.syncUserTweets ?? account.syncUserTweets,
            syncReplies: input.settings.syncReplies ?? account.syncReplies,
            syncRetweets: input.settings.syncRetweets ?? account.syncRetweets,
          },
        });

        return {
          success: true,
          account: {
            id: updatedAccount.id,
            handle: updatedAccount.twitterHandle,
            displayName: updatedAccount.displayName,
            syncSettings: {
              syncMentions: updatedAccount.syncMentions,
              syncUserTweets: updatedAccount.syncUserTweets,
              syncReplies: updatedAccount.syncReplies,
              syncRetweets: updatedAccount.syncRetweets,
            },
          },
          message: `Sync settings updated for @${updatedAccount.twitterHandle}`,
        };
      } catch (error) {
        handleTRPCError(error, "update sync settings", {
          userId: ctx.userId,
          accountId: input.accountId,
        });
      }
    }),

  /**
   * Toggle account monitoring status
   */
  toggleAccountStatus: protectedProcedure
    .input(
      z.object({
        accountId: z.string().cuid("Invalid account ID"),
        isActive: z.boolean(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        console.log(
          `🔄 Toggling account status for ${input.accountId} to ${input.isActive}`
        );

        // Verify account ownership
        const account = await ctx.prisma.monitoredAccount.findFirst({
          where: {
            id: input.accountId,
            userId: ctx.userId!,
          },
        });

        if (!account) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Account not found or access denied",
          });
        }

        // Update account status
        const updatedAccount = await ctx.prisma.monitoredAccount.update({
          where: {
            id: input.accountId,
          },
          data: {
            isActive: input.isActive,
          },
        });

        return {
          success: true,
          account: {
            id: updatedAccount.id,
            handle: updatedAccount.twitterHandle,
            displayName: updatedAccount.displayName,
            isActive: updatedAccount.isActive,
          },
          message: `Account @${updatedAccount.twitterHandle} ${input.isActive ? "activated" : "deactivated"}`,
        };
      } catch (error) {
        handleTRPCError(error, "toggle account status", {
          userId: ctx.userId,
          accountId: input.accountId,
          isActive: input.isActive,
        });
      }
    }),

  /**
   * Get sync history for an account
   */
  getSyncHistory: protectedProcedure
    .input(
      z.object({
        accountId: z.string().cuid("Invalid account ID"),
        limit: z.number().min(1).max(50).default(10),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        console.log(`📜 Getting sync history for account ${input.accountId}`);

        // Verify account ownership
        const account = await ctx.prisma.monitoredAccount.findFirst({
          where: {
            id: input.accountId,
            userId: ctx.userId!,
          },
        });

        if (!account) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Account not found or access denied",
          });
        }

        // Get recent mentions with sync timestamps
        const mentions = await ctx.prisma.mention.findMany({
          where: {
            accountId: input.accountId,
            userId: ctx.userId!,
          },
          orderBy: {
            createdAt: "desc",
          },
          take: input.limit,
          select: {
            id: true,
            content: true,
            authorHandle: true,
            authorName: true,
            createdAt: true,
            mentionedAt: true,
            processed: true,
            isUserTweet: true,
            bullishScore: true,
            importanceScore: true,
          },
        });

        // Group mentions by sync date (day)
        const syncHistory = mentions.reduce(
          (acc, mention) => {
            const syncDate = mention.createdAt.toISOString().split("T")[0];
            if (!acc[syncDate]) {
              acc[syncDate] = {
                date: syncDate,
                mentionCount: 0,
                userTweetCount: 0,
                processedCount: 0,
                avgBullishScore: 0,
                mentions: [],
              };
            }

            acc[syncDate].mentionCount++;
            if (mention.isUserTweet) {
              acc[syncDate].userTweetCount++;
            }
            if (mention.processed) {
              acc[syncDate].processedCount++;
            }
            acc[syncDate].mentions.push(mention);

            return acc;
          },
          {} as Record<string, any>
        );

        // Calculate averages for each day
        Object.values(syncHistory).forEach((day: any) => {
          const bullishScores = day.mentions
            .filter((m: any) => m.bullishScore !== null)
            .map((m: any) => m.bullishScore);
          day.avgBullishScore =
            bullishScores.length > 0
              ? bullishScores.reduce(
                  (sum: number, score: number) => sum + score,
                  0
                ) / bullishScores.length
              : 0;
          day.avgBullishScore = Math.round(day.avgBullishScore * 100) / 100;

          // Remove raw mentions for cleaner response
          delete day.mentions;
        });

        return {
          success: true,
          account: {
            id: account.id,
            handle: account.twitterHandle,
            displayName: account.displayName,
            lastCheckedAt: account.lastCheckedAt,
            totalMentions: account.totalMentions,
          },
          syncHistory: Object.values(syncHistory).sort(
            (a: any, b: any) =>
              new Date(b.date).getTime() - new Date(a.date).getTime()
          ),
          message: `Retrieved sync history for @${account.twitterHandle}`,
        };
      } catch (error) {
        handleTRPCError(error, "get sync history", {
          userId: ctx.userId,
          accountId: input.accountId,
        });
      }
    }),

  /**
   * Force sync for a specific account (ignores rate limits)
   */
  forceSync: protectedProcedure
    .input(
      z.object({
        accountId: z.string().cuid("Invalid account ID"),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        console.log(
          `🚀 Force syncing account ${input.accountId} by user ${ctx.userId}`
        );

        // Verify account ownership
        const account = await ctx.prisma.monitoredAccount.findFirst({
          where: {
            id: input.accountId,
            userId: ctx.userId!,
          },
        });

        if (!account) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Account not found or access denied",
          });
        }

        // Create sync service instance
        const syncService = new MentionSyncService(ctx.prisma);

        // Force sync (this bypasses normal rate limiting)
        const result = await syncService.syncAccountMentions(
          input.accountId,
          ctx.userId!
        );

        if (!result.success) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: result.error || "Failed to force sync mentions",
          });
        }

        return {
          success: true,
          result: result,
          message: `Force sync completed: ${result.newMentions} new mentions for @${result.accountHandle}`,
        };
      } catch (error) {
        handleTRPCError(error, "force sync account", {
          userId: ctx.userId,
          accountId: input.accountId,
        });
      }
    }),
});

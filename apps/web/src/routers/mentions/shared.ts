/**
 * Shared utilities, types, and functions for mentions router
 */

import { TRPCError } from "@trpc/server";
import { z } from "zod";

// Common input schemas
export const commonInputSchemas = {
  pagination: z.object({
    cursor: z.string().optional(),
    limit: z.number().min(1).max(100).default(20),
  }),
  mentionId: z.object({
    mentionId: z.string().min(1, "Mention ID is required"),
  }),
};

// Common validation functions
export function validateMentionId(mentionId: string): void {
  if (!mentionId || mentionId.trim() === "") {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "Mention ID is required",
    });
  }
}

// Error handling utility
export function handleTRPCError(
  error: any,
  operation: string,
  context?: any
): never {
  console.error(`❌ ${operation} error:`, error);
  console.error("❌ Error context:", context);

  if (error instanceof TRPCError) {
    throw error;
  }

  throw new TRPCError({
    code: "INTERNAL_SERVER_ERROR",
    message: `Failed to ${operation}`,
  });
}

/**
 * Extract keywords from mention content
 * This is a simple implementation - could be enhanced with AI
 */
export function extractKeywords(content: string): string[] {
  // Remove URLs, mentions, hashtags for cleaner analysis
  const cleanContent = content
    .replace(/https?:\/\/[^\s]+/g, "") // Remove URLs
    .replace(/@\w+/g, "") // Remove mentions
    .replace(/#\w+/g, "") // Remove hashtags
    .toLowerCase();

  // Simple keyword extraction based on common patterns
  const words = cleanContent
    .split(/\s+/)
    .filter(
      (word) =>
        word.length > 3 &&
        ![
          "this",
          "that",
          "with",
          "from",
          "they",
          "were",
          "been",
          "have",
          "will",
          "would",
          "could",
          "should",
        ].includes(word)
    );

  // Get most frequent words (max 5)
  const wordCounts = words.reduce(
    (acc, word) => {
      acc[word] = (acc[word] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>
  );

  return Object.entries(wordCounts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 5)
    .map(([word]) => word);
}

/**
 * Calculate bullish score based on content sentiment
 * This is a simple implementation - could be enhanced with AI
 */
export function calculateBullishScore(content: string): number {
  const lowerContent = content.toLowerCase();

  // Positive indicators
  const positiveWords = [
    "great",
    "awesome",
    "amazing",
    "excellent",
    "fantastic",
    "love",
    "best",
    "good",
    "nice",
    "wonderful",
    "perfect",
    "brilliant",
    "outstanding",
  ];
  const negativeWords = [
    "bad",
    "terrible",
    "awful",
    "hate",
    "worst",
    "horrible",
    "disappointing",
    "useless",
    "broken",
    "poor",
    "failed",
  ];

  let score = 50; // Neutral starting point

  // Check for positive words
  positiveWords.forEach((word) => {
    if (lowerContent.includes(word)) {
      score += 10;
    }
  });

  // Check for negative words
  negativeWords.forEach((word) => {
    if (lowerContent.includes(word)) {
      score -= 15;
    }
  });

  // Check for exclamation marks (usually positive energy)
  const exclamationCount = (content.match(/!/g) || []).length;
  score += Math.min(exclamationCount * 5, 20);

  // Check for question marks (usually neutral to slightly negative)
  const questionCount = (content.match(/\?/g) || []).length;
  score -= Math.min(questionCount * 2, 10);

  // Ensure score is within bounds
  return Math.max(0, Math.min(100, Math.round(score)));
}

/**
 * Generate performance recommendations based on query statistics
 */
export function generatePerformanceRecommendations(stats: any): string[] {
  const recommendations: string[] = [];

  if (stats.averageDuration > 1000) {
    recommendations.push(
      "Average query time is over 1s - consider optimizing database indexes or query structure"
    );
  }

  if (stats.slowQueries.length > stats.queryCount * 0.1) {
    recommendations.push(
      "High percentage of slow queries detected - review database configuration and query optimization"
    );
  }

  const mentionsGetAllQueries = stats.topSlowQueries.find(
    (q: any) => q.query === "mentions.getAll"
  );
  if (mentionsGetAllQueries && mentionsGetAllQueries.avgDuration > 500) {
    recommendations.push(
      "mentions.getAll queries are slow - consider using includeResponses: false when responses are not needed"
    );
  }

  if (stats.queryCount === 0) {
    recommendations.push("No queries recorded in the specified time range");
  } else if (stats.queryCount > 100) {
    recommendations.push(
      "High query volume detected - consider implementing caching strategies"
    );
  }

  if (recommendations.length === 0) {
    recommendations.push(
      "Performance looks good! All queries are running efficiently."
    );
  }

  return recommendations;
}

// Transform helper for mention data
export function transformMentionData(mention: any) {
  return {
    id: mention.id,
    content: mention.content,
    authorHandle: mention.authorHandle,
    authorName: mention.authorName,
    authorAvatar: mention.authorAvatarUrl,
    tweetUrl: mention.link,
    platform: "twitter",
    createdAt: mention.createdAt,
    bullishScore: mention.bullishScore,
    importanceScore: mention.importanceScore,
    keywords: mention.keywords,
    hasAIResponse: mention.processed,
    account: mention.account
      ? {
          id: mention.account.id,
          handle: mention.account.twitterHandle,
          displayName: mention.account.displayName,
          avatar: mention.account.avatarUrl,
          isActive: mention.account.isActive,
        }
      : null,
  };
}

// Transform helper for response data
export function transformResponseData(response: any) {
  return {
    id: response.id,
    content: response.content,
    model: response.model,
    confidence: response.confidence,
    tokensUsed: response.tokensUsed,
    createdAt: response.createdAt,
  };
}

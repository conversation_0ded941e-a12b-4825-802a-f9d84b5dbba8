/**
 * Mention Sync Operations
 * 
 * All sync operations for mentions including:
 * - syncAccount: Sync mentions for a specific account
 * - syncAllAccounts: Sync mentions for all user accounts
 * - getSyncStats: Get sync statistics
 */

import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { createTRPCRouter, protectedProcedure } from "../../lib/trpc";
import { MentionSyncService } from "../../lib/mention-sync-service";

export const mentionSync = createTRPCRouter({
  /**
   * Sync mentions for a specific monitored account
   */
  syncAccount: protectedProcedure
    .input(
      z.object({
        accountId: z.string().cuid("Invalid account ID"),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        console.log(
          `🔄 Starting mention sync for account ${input.accountId} by user ${ctx.userId}`
        );

        // Create sync service instance with Prisma client
        const syncService = new MentionSyncService(ctx.prisma);

        // Sync mentions for the specific account
        const result = await syncService.syncAccountMentions(
          input.accountId,
          ctx.userId!
        );

        if (!result.success) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: result.error || "Failed to sync mentions",
          });
        }

        return {
          success: true,
          result: result,
          message: `Synced ${result.newMentions} new mentions for @${result.accountHandle}`,
        };
      } catch (error) {
        console.error("Sync account mentions error:", error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to sync account mentions",
        });
      }
    }),

  /**
   * Sync mentions for all monitored accounts of the current user
   */
  syncAllAccounts: protectedProcedure.mutation(async ({ ctx }) => {
    try {
      console.log(`🚀 Starting bulk mention sync for user ${ctx.userId}`);

      // Create sync service instance with Prisma client
      const syncService = new MentionSyncService(ctx.prisma);

      // Sync mentions for all user's accounts
      const result = await syncService.syncAllUserAccounts(ctx.userId!);

      return {
        success: result.success,
        totalNewMentions: result.totalNewMentions,
        accountResults: result.results,
        errors: result.errors,
        message: result.success
          ? `Successfully synced ${result.totalNewMentions} new mentions across ${result.results.length} accounts`
          : `Sync completed with ${result.errors.length} errors`,
      };
    } catch (error) {
      console.error("Bulk sync mentions error:", error);

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to sync mentions for all accounts",
      });
    }
  }),

  /**
   * Get sync statistics for the current user
   */
  getSyncStats: protectedProcedure.query(async ({ ctx }) => {
    try {
      // Create sync service instance with Prisma client
      const syncService = new MentionSyncService(ctx.prisma);

      // Get sync statistics
      const stats = await syncService.getSyncStats(ctx.userId!);

      return {
        success: true,
        stats: stats,
      };
    } catch (error) {
      console.error("Get sync stats error:", error);

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to get sync statistics",
      });
    }
  }),
});
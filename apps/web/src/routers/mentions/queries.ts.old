/**
 * Mention Query Operations
 * 
 * All read operations for mentions including:
 * - getLatest: Get recent mentions
 * - getAll: Get all mentions with pagination
 * - getById: Get specific mention by ID
 * - getAccountTweets: Get tweets for a monitored account
 * - getArchived: Get archived mentions
 * - getPerformanceStats: Get performance statistics
 */

import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { protectedProcedure } from "../../lib/trpc";
import { performanceMonitor } from "../../lib/performance-monitor";
import { 
  commonInputSchemas, 
  handleTRPCError, 
  transformMentionData, 
  transformResponseData,
  generatePerformanceRecommendations 
} from "./shared";

export const mentionQueries = {
  /**
   * Get latest mentions for dashboard
   */
  getLatest: protectedProcedure
    .input(
      commonInputSchemas.pagination.pick({ limit: true })
    )
    .query(async ({ input, ctx }) => {
      try {
        const mentions = await ctx.prisma.mention.findMany({
          where: {
            userId: ctx.userId!,
            archived: false, // Only show non-archived mentions
          },
          orderBy: {
            createdAt: "desc",
          },
          take: input.limit,
          include: {
            account: {
              select: {
                id: true,
                twitterHandle: true,
                displayName: true,
                avatarUrl: true,
                isActive: true,
              },
            },
          },
        });

        return {
          success: true,
          mentions: mentions.map(transformMentionData),
        };
      } catch (error: any) {
        handleTRPCError(error, "fetch latest mentions", {
          userId: ctx.userId,
          limit: input.limit
        });
      }
    }),

  /**
   * Get all mentions with pagination (for reply-guy page)
   */
  getAll: protectedProcedure
    .input(
      z.object({
        cursor: z.string().optional(),
        limit: z.number().min(1).max(100).default(20),
        accountId: z.string().optional(),
        hasAIResponse: z.boolean().optional(),
        includeResponses: z.boolean().default(false), // Only load responses when explicitly requested
        includeAccount: z.boolean().default(true), // Allow disabling account data if not needed
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        console.log("🔍 Mentions.getAll: Starting query with input:", input);
        console.log("🔍 Mentions.getAll: User ID:", ctx.userId);
        console.log("🔍 Mentions.getAll: Prisma client exists:", !!ctx.prisma);

        const where: any = {
          userId: ctx.userId!,
          archived: false, // Only show non-archived mentions by default
        };

        // Add filters
        if (input.accountId) {
          where.accountId = input.accountId;
        }
        if (input.hasAIResponse !== undefined) {
          where.processed = input.hasAIResponse; // Using processed field as proxy
        }

        // Optimized cursor-based pagination using timestamp + id
        if (input.cursor) {
          try {
            const [timestamp, id] = input.cursor.split("_");
            if (timestamp && id) {
              // Use compound cursor for better performance with existing indexes
              where.OR = [
                {
                  createdAt: { lt: new Date(timestamp) },
                },
                {
                  createdAt: new Date(timestamp),
                  id: { lt: id },
                },
              ];
            } else {
              // Fallback to simple ID cursor for backwards compatibility
              where.id = { lt: input.cursor };
            }
          } catch (cursorError) {
            console.warn(
              "Invalid cursor format, falling back to ID-based pagination:",
              cursorError
            );
            where.id = { lt: input.cursor };
          }
        }

        console.log(
          "🔍 Mentions.getAll: Where clause:",
          JSON.stringify(where, null, 2)
        );
        console.log("🔍 Mentions.getAll: About to execute findMany query...");

        const mentions = await performanceMonitor.trackQuery(
          "mentions.getAll",
          () =>
            ctx.prisma.mention.findMany({
              where,
              orderBy: {
                createdAt: "desc",
              },
              take: input.limit + 1, // Take one extra to determine if there are more results
              include: {
                // Conditionally include account data
                ...(input.includeAccount && {
                  account: {
                    select: {
                      id: true,
                      twitterHandle: true,
                      displayName: true,
                      avatarUrl: true,
                      isActive: true,
                    },
                  },
                }),
                // Only load responses when explicitly requested (eliminates N+1)
                ...(input.includeResponses && {
                  responses: {
                    orderBy: {
                      createdAt: "desc",
                    },
                    take: 3, // Get latest 3 AI responses
                    select: {
                      id: true,
                      content: true,
                      model: true,
                      confidence: true,
                      tokensUsed: true,
                      createdAt: true,
                    },
                  },
                }),
              },
            }),
          {
            limit: input.limit,
            includeResponses: input.includeResponses,
            includeAccount: input.includeAccount,
            accountId: input.accountId,
            hasAIResponse: input.hasAIResponse,
          },
          ctx.userId
        );

        console.log(
          "✅ Mentions.getAll: Query completed successfully, found",
          mentions.length,
          "mentions"
        );

        // Check if there are more results
        const hasNextPage = mentions.length > input.limit;
        const items = hasNextPage ? mentions.slice(0, -1) : mentions;
        // Generate compound cursor for better pagination performance
        const nextCursor = hasNextPage
          ? `${items[items.length - 1].createdAt.toISOString()}_${items[items.length - 1].id}`
          : null;

        return {
          success: true,
          mentions: items.map((mention) => ({
            ...transformMentionData(mention),
            responses: input.includeResponses
              ? mention.responses?.map(transformResponseData) || []
              : [], // Return empty array when responses not loaded
          })),
          nextCursor,
          hasNextPage,
          // Add metadata about what was included for debugging
          meta: {
            includeResponses: input.includeResponses,
            includeAccount: input.includeAccount,
            totalItems: items.length,
          },
        };
      } catch (error) {
        console.error("❌ Mentions.getAll: Error occurred:", error);
        console.error("❌ Mentions.getAll: Error details:", {
          message: error instanceof Error ? error.message : String(error),
          code: (error as any)?.code,
          meta: (error as any)?.meta,
          stack: error instanceof Error ? error.stack : undefined,
        });
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch mentions",
        });
      }
    }),

  /**
   * Get tweets for a monitored account with advanced filtering
   * Can fetch mentions, user tweets, replies, or any combination
   */
  getAccountTweets: protectedProcedure
    .input(
      z.object({
        accountId: z.string(),
        cursor: z.string().optional(),
        limit: z.number().min(1).max(100).default(20),
        // Tweet type filters
        tweetTypes: z
          .object({
            mentions: z.boolean().default(true), // Tweets mentioning the account
            userTweets: z.boolean().default(true), // Tweets by the account
            replies: z.boolean().default(true), // Include replies (for userTweets)
            retweets: z.boolean().default(true), // Include retweets (for userTweets)
          })
          .optional(),
        // Additional filters
        hasAIResponse: z.boolean().optional(),
        archived: z.boolean().default(false),
        // Date range filters
        startDate: z.date().optional(),
        endDate: z.date().optional(),
        // Sorting
        sortBy: z
          .enum(["createdAt", "bullishScore", "importanceScore"])
          .default("createdAt"),
        sortOrder: z.enum(["asc", "desc"]).default("desc"),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        // Verify account ownership
        const account = await ctx.prisma.monitoredAccount.findFirst({
          where: {
            id: input.accountId,
            userId: ctx.userId!,
          },
        });

        if (!account) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Account not found or access denied",
          });
        }

        // Build where clause based on filters
        const where: any = {
          archived: input.archived,
        };

        // Tweet type filtering
        const typeConditions = [];
        const types = input.tweetTypes || {
          mentions: true,
          userTweets: true,
          replies: true,
          retweets: true,
        };

        if (types.mentions) {
          // Mentions: tweets that mention this account but aren't by this account
          typeConditions.push({
            accountId: input.accountId,
            isUserTweet: false,
          });
        }

        if (types.userTweets) {
          // User tweets: tweets by this account
          const userTweetCondition: any = {
            authorHandle: account.twitterHandle,
            isUserTweet: true,
          };

          // Apply reply/retweet filters only to user tweets
          if (!types.replies) {
            userTweetCondition.isReply = false;
          }
          // Note: We'd need to add isRetweet field to fully support this filter

          typeConditions.push(userTweetCondition);
        }

        // Combine type conditions with OR
        if (typeConditions.length > 0) {
          where.OR = typeConditions;
        }

        // Additional filters
        if (input.hasAIResponse !== undefined) {
          where.processed = input.hasAIResponse;
        }

        if (input.startDate || input.endDate) {
          where.mentionedAt = {};
          if (input.startDate) where.mentionedAt.gte = input.startDate;
          if (input.endDate) where.mentionedAt.lte = input.endDate;
        }

        // Pagination
        if (input.cursor) {
          where.id = { lt: input.cursor };
        }

        // Build orderBy
        const orderBy: any = {};
        orderBy[input.sortBy] = input.sortOrder;

        const tweets = await ctx.prisma.mention.findMany({
          where,
          orderBy,
          take: input.limit + 1,
          include: {
            account: true,
            responses: {
              orderBy: { createdAt: "desc" },
              take: 3,
            },
          },
        });

        const hasNextPage = tweets.length > input.limit;
        const items = hasNextPage ? tweets.slice(0, -1) : tweets;
        const nextCursor = hasNextPage ? items[items.length - 1].id : null;

        return {
          success: true,
          tweets: items.map((tweet) => ({
            id: tweet.id,
            content: tweet.content,
            authorHandle: tweet.authorHandle,
            authorName: tweet.authorName,
            authorAvatar: tweet.authorAvatarUrl,
            tweetUrl: tweet.link,
            createdAt: tweet.createdAt,
            bullishScore: tweet.bullishScore,
            importanceScore: tweet.importanceScore,
            keywords: tweet.keywords,
            hasAIResponse: tweet.processed,
            isUserTweet: tweet.isUserTweet,
            isReply: tweet.isReply,
            tweetType: tweet.isUserTweet ? "userTweet" : "mention",
            account: tweet.account,
            responses: tweet.responses.map(transformResponseData),
          })),
          nextCursor,
          hasNextPage,
          filters: {
            applied: input.tweetTypes || {
              mentions: true,
              userTweets: true,
              replies: true,
              retweets: true,
            },
            account: {
              id: account.id,
              handle: account.twitterHandle,
              displayName: account.displayName,
            },
          },
        };
      } catch (error) {
        console.error("Get account tweets error:", error);
        throw error instanceof TRPCError
          ? error
          : new TRPCError({
              code: "INTERNAL_SERVER_ERROR",
              message: "Failed to fetch account tweets",
            });
      }
    }),

  /**
   * Get mention by ID with all details
   */
  getById: protectedProcedure
    .input(commonInputSchemas.mentionId)
    .query(async ({ input, ctx }) => {
      try {
        const mention = await ctx.prisma.mention.findFirst({
          where: {
            id: input.mentionId,
            userId: ctx.userId!,
          },
          include: {
            account: {
              select: {
                id: true,
                twitterHandle: true,
                displayName: true,
                avatarUrl: true,
                isActive: true,
              },
            },
            responses: {
              orderBy: {
                createdAt: "desc",
              },
            },
          },
        });

        if (!mention) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Mention not found",
          });
        }

        return {
          success: true,
          mention: {
            ...transformMentionData(mention),
            responses: mention.responses.map(transformResponseData),
          },
        };
      } catch (error) {
        console.error("Get mention by ID error:", error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch mention details",
        });
      }
    }),

  /**
   * Get archived mentions
   */
  getArchived: protectedProcedure
    .input(
      z.object({
        cursor: z.string().optional(),
        limit: z.number().min(1).max(100).default(20),
        accountId: z.string().optional(),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        const where: any = {
          userId: ctx.userId!,
          archived: true,
        };

        if (input.accountId) {
          where.accountId = input.accountId;
        }

        const mentions = await ctx.prisma.mention.findMany({
          where,
          orderBy: {
            archivedAt: "desc",
          },
          take: input.limit + 1,
          cursor: input.cursor ? { id: input.cursor } : undefined,
          include: {
            account: {
              select: {
                id: true,
                twitterHandle: true,
                displayName: true,
                avatarUrl: true,
                isActive: true,
              },
            },
            responses: {
              orderBy: {
                createdAt: "desc",
              },
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    avatar: true,
                  },
                },
              },
            },
          },
        });

        let nextCursor: string | undefined;
        if (mentions.length > input.limit) {
          const nextItem = mentions.pop();
          nextCursor = nextItem!.id;
        }

        return {
          success: true,
          mentions: mentions.map((mention) => ({
            id: mention.id,
            content: mention.content,
            authorHandle: mention.authorHandle,
            authorName: mention.authorName,
            authorAvatar: mention.authorAvatarUrl,
            tweetUrl: mention.link,
            platform: "twitter",
            createdAt: mention.createdAt,
            archivedAt: mention.archivedAt,
            bullishScore: mention.bullishScore,
            importanceScore: mention.importanceScore,
            keywords: mention.keywords,
            hasAIResponse: mention.responses.length > 0,
            account: mention.account
              ? {
                  id: mention.account.id,
                  handle: mention.account.twitterHandle,
                  displayName: mention.account.displayName,
                  avatar: mention.account.avatarUrl,
                  isActive: mention.account.isActive,
                }
              : null,
            responses: mention.responses.map((response) => ({
              id: response.id,
              content: response.content,
              model: response.model,
              confidence: response.confidence,
              tokensUsed: response.tokensUsed,
              used: response.used,
              createdAt: response.createdAt,
            })),
          })),
          nextCursor,
        };
      } catch (error) {
        console.error("Get archived mentions error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch archived mentions",
        });
      }
    }),

  /**
   * Get performance statistics for debugging and monitoring
   */
  getPerformanceStats: protectedProcedure
    .input(
      z.object({
        minutes: z.number().min(1).max(1440).default(60), // Default to last hour, max 24 hours
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        const stats = performanceMonitor.getStats(input.minutes);

        // Log summary to console for debugging
        performanceMonitor.logSummary(input.minutes);

        return {
          success: true,
          stats: {
            timeRange: `${input.minutes} minutes`,
            queryCount: stats.queryCount,
            averageDuration: Math.round(stats.averageDuration * 100) / 100, // Round to 2 decimals
            slowQueriesCount: stats.slowQueries.length,
            topSlowQueries: stats.topSlowQueries.map((q) => ({
              ...q,
              avgDuration: Math.round(q.avgDuration * 100) / 100,
            })),
            recommendations: generatePerformanceRecommendations(stats),
          },
        };
      } catch (error) {
        console.error("Get performance stats error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get performance statistics",
        });
      }
    }),
};
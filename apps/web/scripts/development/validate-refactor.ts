#!/usr/bin/env tsx
/**
 * Refactoring Validation Script
 * 
 * Comprehensive validation tool for refactoring progress and quality.
 * Checks file sizes, code quality, test coverage, and performance metrics.
 */

import { execSync } from "child_process";
import { readdirSync, statSync, readFileSync } from "fs";
import { join, extname } from "path";

interface FileAnalysis {
  path: string;
  lines: number;
  size: number;
  complexity?: number;
  issues?: string[];
}

interface ValidationResult {
  status: "pass" | "warning" | "fail";
  message: string;
  details?: any;
}

interface RefactorMetrics {
  fileCount: number;
  totalLines: number;
  largeFiles: FileAnalysis[];
  duplicateCode: number;
  testCoverage: number;
  typeErrors: number;
  performanceIssues: string[];
}

class RefactorValidator {
  private srcPath = join(process.cwd(), "src");
  private maxFileLines = 500;
  private maxComplexity = 10;
  
  /**
   * Run complete validation suite
   */
  async validate(): Promise<ValidationResult[]> {
    console.log("🔍 Starting refactoring validation...\n");
    
    const results: ValidationResult[] = [];
    
    try {
      // File size analysis
      results.push(await this.validateFileSizes());
      
      // Code quality checks
      results.push(await this.validateCodeQuality());
      
      // Type checking
      results.push(await this.validateTypes());
      
      // Test coverage
      results.push(await this.validateTestCoverage());
      
      // Performance checks
      results.push(await this.validatePerformance());
      
      // Dependency analysis
      results.push(await this.validateDependencies());
      
      // Security scan
      results.push(await this.validateSecurity());
      
    } catch (error) {
      results.push({
        status: "fail",
        message: "Validation failed with error",
        details: error instanceof Error ? error.message : String(error),
      });
    }
    
    this.printResults(results);
    return results;
  }
  
  /**
   * Analyze file sizes and identify large files
   */
  private async validateFileSizes(): Promise<ValidationResult> {
    console.log("📊 Analyzing file sizes...");
    
    const largeFiles: FileAnalysis[] = [];
    const allFiles = this.getAllFiles(this.srcPath, [".ts", ".tsx"]);
    
    for (const filePath of allFiles) {
      const content = readFileSync(filePath, "utf-8");
      const lines = content.split("\n").length;
      const size = statSync(filePath).size;
      
      if (lines > this.maxFileLines) {
        largeFiles.push({
          path: filePath.replace(process.cwd(), ""),
          lines,
          size,
          issues: [`File has ${lines} lines (max: ${this.maxFileLines})`],
        });
      }
    }
    
    if (largeFiles.length === 0) {
      return {
        status: "pass",
        message: `✅ All files under ${this.maxFileLines} lines`,
      };
    }
    
    const status = largeFiles.length > 5 ? "fail" : "warning";
    return {
      status,
      message: `${status === "fail" ? "❌" : "⚠️"} Found ${largeFiles.length} large files`,
      details: largeFiles.slice(0, 10), // Show top 10
    };
  }
  
  /**
   * Check code quality metrics
   */
  private async validateCodeQuality(): Promise<ValidationResult> {
    console.log("🎯 Checking code quality...");
    
    try {
      // Run linting
      const lintOutput = execSync("pnpm lint", { 
        encoding: "utf-8",
        cwd: process.cwd(),
      });
      
      // Check for circular dependencies
      let circularDeps: string[] = [];
      try {
        const madgeOutput = execSync("npx madge --circular --warning src", {
          encoding: "utf-8",
          cwd: process.cwd(),
        });
        circularDeps = madgeOutput.split("\n").filter(line => line.trim());
      } catch {
        // madge not installed or no circular deps
      }
      
      // Check for duplicate code
      let duplicateCode = 0;
      try {
        const jscpdOutput = execSync("npx jscpd src --threshold 10 --format json", {
          encoding: "utf-8",
          cwd: process.cwd(),
        });
        const jscpdResult = JSON.parse(jscpdOutput);
        duplicateCode = jscpdResult.statistics?.total?.percentage || 0;
      } catch {
        // jscpd not available
      }
      
      const issues: string[] = [];
      
      if (circularDeps.length > 0) {
        issues.push(`${circularDeps.length} circular dependencies found`);
      }
      
      if (duplicateCode > 10) {
        issues.push(`${duplicateCode}% duplicate code (max: 10%)`);
      }
      
      if (issues.length === 0) {
        return {
          status: "pass",
          message: "✅ Code quality checks passed",
        };
      }
      
      return {
        status: "warning",
        message: `⚠️ Code quality issues found`,
        details: issues,
      };
      
    } catch (error) {
      return {
        status: "fail",
        message: "❌ Code quality check failed",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
  
  /**
   * Validate TypeScript types
   */
  private async validateTypes(): Promise<ValidationResult> {
    console.log("🔍 Checking TypeScript types...");
    
    try {
      execSync("pnpm check-types", {
        encoding: "utf-8",
        cwd: process.cwd(),
      });
      
      return {
        status: "pass",
        message: "✅ No TypeScript errors",
      };
    } catch (error) {
      const errorOutput = error instanceof Error ? error.message : String(error);
      const errorCount = (errorOutput.match(/error TS/g) || []).length;
      
      return {
        status: "fail",
        message: `❌ ${errorCount} TypeScript errors found`,
        details: errorOutput.split("\n").slice(0, 20), // Show first 20 lines
      };
    }
  }
  
  /**
   * Check test coverage
   */
  private async validateTestCoverage(): Promise<ValidationResult> {
    console.log("🧪 Checking test coverage...");
    
    try {
      const coverageOutput = execSync("pnpm test:coverage --reporter=json", {
        encoding: "utf-8",
        cwd: process.cwd(),
      });
      
      // Parse coverage report
      const coverage = JSON.parse(coverageOutput);
      const totalCoverage = coverage.total?.lines?.pct || 0;
      
      if (totalCoverage >= 80) {
        return {
          status: "pass",
          message: `✅ Test coverage: ${totalCoverage}%`,
        };
      } else if (totalCoverage >= 60) {
        return {
          status: "warning",
          message: `⚠️ Test coverage: ${totalCoverage}% (target: 80%)`,
        };
      } else {
        return {
          status: "fail",
          message: `❌ Test coverage: ${totalCoverage}% (minimum: 60%)`,
        };
      }
    } catch (error) {
      return {
        status: "warning",
        message: "⚠️ Could not determine test coverage",
        details: "Run 'pnpm test:coverage' manually to check",
      };
    }
  }
  
  /**
   * Check performance metrics
   */
  private async validatePerformance(): Promise<ValidationResult> {
    console.log("⚡ Checking performance...");
    
    try {
      // Build the application to check bundle size
      execSync("pnpm build", {
        encoding: "utf-8",
        cwd: process.cwd(),
      });
      
      // Check bundle size (simplified)
      const buildPath = join(process.cwd(), ".next");
      const bundleSize = this.getFolderSize(buildPath);
      const bundleSizeMB = bundleSize / (1024 * 1024);
      
      const issues: string[] = [];
      
      if (bundleSizeMB > 50) {
        issues.push(`Bundle size: ${bundleSizeMB.toFixed(2)}MB (max: 50MB)`);
      }
      
      if (issues.length === 0) {
        return {
          status: "pass",
          message: `✅ Performance checks passed (bundle: ${bundleSizeMB.toFixed(2)}MB)`,
        };
      }
      
      return {
        status: "warning",
        message: "⚠️ Performance issues found",
        details: issues,
      };
    } catch (error) {
      return {
        status: "warning",
        message: "⚠️ Could not check performance metrics",
        details: "Build failed or metrics unavailable",
      };
    }
  }
  
  /**
   * Validate dependencies
   */
  private async validateDependencies(): Promise<ValidationResult> {
    console.log("📦 Checking dependencies...");
    
    try {
      // Check for security vulnerabilities
      const auditOutput = execSync("pnpm audit --json", {
        encoding: "utf-8",
        cwd: process.cwd(),
      });
      
      const audit = JSON.parse(auditOutput);
      const vulnerabilities = audit.metadata?.vulnerabilities || {};
      const totalVulns = Object.values(vulnerabilities).reduce((sum: number, count) => sum + (count as number), 0);
      
      if (totalVulns === 0) {
        return {
          status: "pass",
          message: "✅ No security vulnerabilities found",
        };
      } else if (totalVulns < 5) {
        return {
          status: "warning",
          message: `⚠️ ${totalVulns} security vulnerabilities found`,
          details: vulnerabilities,
        };
      } else {
        return {
          status: "fail",
          message: `❌ ${totalVulns} security vulnerabilities found`,
          details: vulnerabilities,
        };
      }
    } catch (error) {
      return {
        status: "warning",
        message: "⚠️ Could not check dependencies",
        details: "Run 'pnpm audit' manually",
      };
    }
  }
  
  /**
   * Security validation
   */
  private async validateSecurity(): Promise<ValidationResult> {
    console.log("🔒 Running security checks...");
    
    const issues: string[] = [];
    
    // Check for hardcoded secrets
    const secretPatterns = [
      /api[_-]?key\s*[:=]\s*['"][^'"]+['"]/i,
      /secret\s*[:=]\s*['"][^'"]+['"]/i,
      /password\s*[:=]\s*['"][^'"]+['"]/i,
      /token\s*[:=]\s*['"][^'"]+['"]/i,
    ];
    
    const allFiles = this.getAllFiles(this.srcPath, [".ts", ".tsx", ".js", ".jsx"]);
    
    for (const filePath of allFiles) {
      const content = readFileSync(filePath, "utf-8");
      
      for (const pattern of secretPatterns) {
        if (pattern.test(content)) {
          issues.push(`Potential hardcoded secret in ${filePath.replace(process.cwd(), "")}`);
        }
      }
    }
    
    if (issues.length === 0) {
      return {
        status: "pass",
        message: "✅ No security issues found",
      };
    }
    
    return {
      status: "warning",
      message: `⚠️ ${issues.length} potential security issues`,
      details: issues.slice(0, 10),
    };
  }
  
  /**
   * Get all files with specific extensions
   */
  private getAllFiles(dir: string, extensions: string[]): string[] {
    const files: string[] = [];
    
    try {
      const items = readdirSync(dir);
      
      for (const item of items) {
        const fullPath = join(dir, item);
        const stat = statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith(".") && item !== "node_modules") {
          files.push(...this.getAllFiles(fullPath, extensions));
        } else if (stat.isFile() && extensions.includes(extname(item))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      // Directory doesn't exist or permission denied
    }
    
    return files;
  }
  
  /**
   * Get folder size recursively
   */
  private getFolderSize(dir: string): number {
    let size = 0;
    
    try {
      const items = readdirSync(dir);
      
      for (const item of items) {
        const fullPath = join(dir, item);
        const stat = statSync(fullPath);
        
        if (stat.isDirectory()) {
          size += this.getFolderSize(fullPath);
        } else {
          size += stat.size;
        }
      }
    } catch (error) {
      // Directory doesn't exist or permission denied
    }
    
    return size;
  }
  
  /**
   * Print validation results
   */
  private printResults(results: ValidationResult[]): void {
    console.log("\n" + "=".repeat(60));
    console.log("📋 REFACTORING VALIDATION RESULTS");
    console.log("=".repeat(60));
    
    const passed = results.filter(r => r.status === "pass").length;
    const warnings = results.filter(r => r.status === "warning").length;
    const failed = results.filter(r => r.status === "fail").length;
    
    console.log(`\n📊 Summary: ${passed} passed, ${warnings} warnings, ${failed} failed\n`);
    
    for (const result of results) {
      console.log(result.message);
      
      if (result.details && Array.isArray(result.details)) {
        result.details.slice(0, 5).forEach(detail => {
          console.log(`   • ${detail}`);
        });
        
        if (result.details.length > 5) {
          console.log(`   ... and ${result.details.length - 5} more`);
        }
      } else if (result.details) {
        console.log(`   Details: ${JSON.stringify(result.details, null, 2)}`);
      }
      
      console.log();
    }
    
    // Overall status
    if (failed > 0) {
      console.log("❌ VALIDATION FAILED - Critical issues need to be addressed");
      process.exit(1);
    } else if (warnings > 0) {
      console.log("⚠️ VALIDATION PASSED WITH WARNINGS - Consider addressing warnings");
    } else {
      console.log("✅ VALIDATION PASSED - All checks successful");
    }
  }
}

// Run validation if called directly
if (require.main === module) {
  const validator = new RefactorValidator();
  validator.validate().catch(error => {
    console.error("❌ Validation failed:", error);
    process.exit(1);
  });
}

export { RefactorValidator };

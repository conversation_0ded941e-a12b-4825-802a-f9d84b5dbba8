#!/usr/bin/env tsx
/**
 * Validation Script for DEBUG1.md Fixes
 * 
 * This script validates that all fixes from DEBUG1.md have been applied correctly
 * and tests the improved reliability of the refactoring scripts.
 */

import { execSync } from "child_process";
import { readFileSync, existsSync } from "fs";
import { join } from "path";

interface ValidationResult {
  script: string;
  passed: boolean;
  errors: string[];
  warnings: string[];
}

class FixValidator {
  private results: ValidationResult[] = [];
  
  /**
   * Main validation function
   */
  async validateFixes(): Promise<void> {
    console.log("🔍 Validating DEBUG1.md fixes...\n");
    
    // Test create-baseline.ts improvements
    await this.validateCreateBaseline();
    
    // Test quality-check.ts improvements  
    await this.validateQualityCheck();
    
    // Test setup-refactor.ts improvements
    await this.validateSetupRefactor();
    
    // Test validate-service-optimization.ts improvements
    await this.validateServiceOptimization();
    
    // Print summary
    this.printSummary();
  }
  
  /**
   * Validate create-baseline.ts fixes
   */
  private async validateCreateBaseline(): Promise<void> {
    console.log("📊 Validating create-baseline.ts fixes...");
    
    const result: ValidationResult = {
      script: "create-baseline.ts",
      passed: true,
      errors: [],
      warnings: []
    };
    
    try {
      // Check if wc command parsing uses regex
      const content = readFileSync(join(process.cwd(), "apps/web/scripts/refactor/create-baseline.ts"), "utf-8");
      
      if (!content.includes("lineMatch = lineCountOutput.match")) {
        result.errors.push("wc command parsing should use regex");
        result.passed = false;
      }
      
      // Check if test coverage reading handles JSON parse errors
      if (!content.includes("JSON.parse") || !content.includes("parseError")) {
        result.errors.push("Missing JSON.parse error handling for coverage");
        result.passed = false;
      }
      
      // Check if outdated dependencies handling is improved
      if (!content.includes("Could not parse outdated dependencies")) {
        result.errors.push("Missing improved outdated dependencies handling");
        result.passed = false;
      }
      
      console.log(`   ✅ create-baseline.ts validation: ${result.passed ? "PASSED" : "FAILED"}`);
      
    } catch (error) {
      result.errors.push(`Could not validate create-baseline.ts: ${error}`);
      result.passed = false;
    }
    
    this.results.push(result);
  }
  
  /**
   * Validate quality-check.ts fixes
   */
  private async validateQualityCheck(): Promise<void> {
    console.log("🎯 Validating quality-check.ts fixes...");
    
    const result: ValidationResult = {
      script: "quality-check.ts",
      passed: true,
      errors: [],
      warnings: []
    };
    
    try {
      const content = readFileSync(join(process.cwd(), "apps/web/scripts/refactor/quality-check.ts"), "utf-8");
      
      // Check if JSON.parse calls are wrapped in try-catch
      const jsonParseCount = (content.match(/JSON\.parse/g) || []).length;
      const tryCatchCount = (content.match(/try\s*\{[^}]*JSON\.parse/g) || []).length;
      
      if (jsonParseCount !== tryCatchCount) {
        result.warnings.push("Some JSON.parse calls may not be wrapped in try-catch");
      }
      
      // Check if error handling is improved
      if (!content.includes("Could not parse") || !content.includes("parseError")) {
        result.errors.push("Missing improved JSON parsing error handling");
        result.passed = false;
      }
      
      console.log(`   ✅ quality-check.ts validation: ${result.passed ? "PASSED" : "FAILED"}`);
      
    } catch (error) {
      result.errors.push(`Could not validate quality-check.ts: ${error}`);
      result.passed = false;
    }
    
    this.results.push(result);
  }
  
  /**
   * Validate setup-refactor.ts fixes
   */
  private async validateSetupRefactor(): Promise<void> {
    console.log("🚀 Validating setup-refactor.ts fixes...");
    
    const result: ValidationResult = {
      script: "setup-refactor.ts",
      passed: true,
      errors: [],
      warnings: []
    };
    
    try {
      const content = readFileSync(join(process.cwd(), "apps/web/scripts/refactor/setup-refactor.ts"), "utf-8");
      
      // Check if Node.js filesystem APIs are used instead of Unix commands
      if (content.includes("execSync") && content.includes("find src")) {
        result.warnings.push("May still be using Unix commands instead of Node.js APIs");
      }
      
      // Check if cross-platform file operations are used
      if (!content.includes("readFileSync") || !content.includes("readdirSync")) {
        result.errors.push("Should use Node.js filesystem APIs for cross-platform compatibility");
        result.passed = false;
      }
      
      console.log(`   ✅ setup-refactor.ts validation: ${result.passed ? "PASSED" : "FAILED"}`);
      
    } catch (error) {
      result.errors.push(`Could not validate setup-refactor.ts: ${error}`);
      result.passed = false;
    }
    
    this.results.push(result);
  }
  
  /**
   * Validate validate-service-optimization.ts fixes
   */
  private async validateServiceOptimization(): Promise<void> {
    console.log("🔧 Validating validate-service-optimization.ts fixes...");
    
    const result: ValidationResult = {
      script: "validate-service-optimization.ts",
      passed: true,
      errors: [],
      warnings: []
    };
    
    try {
      const content = readFileSync(join(process.cwd(), "apps/web/scripts/validate-service-optimization.ts"), "utf-8");
      
      // Check if test user ID is configurable
      if (!content.includes("process.env.TEST_USER_ID")) {
        result.errors.push("Test user ID should be configurable via environment variable");
        result.passed = false;
      }
      
      // Check if hardcoded value is used as fallback
      if (!content.includes("|| 'ctest123456789012345678901'")) {
        result.errors.push("Should have hardcoded fallback for test user ID");
        result.passed = false;
      }
      
      console.log(`   ✅ validate-service-optimization.ts validation: ${result.passed ? "PASSED" : "FAILED"}`);
      
    } catch (error) {
      result.errors.push(`Could not validate validate-service-optimization.ts: ${error}`);
      result.passed = false;
    }
    
    this.results.push(result);
  }
  
  /**
   * Test script robustness
   */
  private async testScriptRobustness(): Promise<void> {
    console.log("🧪 Testing script robustness...");
    
    const scripts = [
      "apps/web/scripts/refactor/create-baseline.ts",
      "apps/web/scripts/refactor/quality-check.ts", 
      "apps/web/scripts/refactor/setup-refactor.ts"
    ];
    
    for (const script of scripts) {
      try {
        // Test if script can be imported without errors
        const fullPath = join(process.cwd(), script);
        if (existsSync(fullPath)) {
          console.log(`   ✅ ${script} exists and can be accessed`);
        } else {
          console.log(`   ⚠️ ${script} does not exist`);
        }
      } catch (error) {
        console.log(`   ❌ ${script} has issues: ${error}`);
      }
    }
  }
  
  /**
   * Print validation summary
   */
  private printSummary(): void {
    console.log("\n" + "=".repeat(60));
    console.log("📋 VALIDATION SUMMARY");
    console.log("=".repeat(60));
    
    let totalPassed = 0;
    let totalFailed = 0;
    
    for (const result of this.results) {
      if (result.passed) {
        totalPassed++;
        console.log(`✅ ${result.script} - PASSED`);
      } else {
        totalFailed++;
        console.log(`❌ ${result.script} - FAILED`);
        
        for (const error of result.errors) {
          console.log(`   • ${error}`);
        }
      }
      
      if (result.warnings.length > 0) {
        console.log(`⚠️ ${result.script} - WARNINGS:`);
        for (const warning of result.warnings) {
          console.log(`   • ${warning}`);
        }
      }
    }
    
    console.log(`\n📊 Results: ${totalPassed} passed, ${totalFailed} failed`);
    
    if (totalFailed === 0) {
      console.log("🎉 All DEBUG1.md fixes have been successfully applied!");
    } else {
      console.log("⚠️ Some fixes may need additional work.");
    }
  }
}

// Run validation if called directly
if (require.main === module) {
  const validator = new FixValidator();
  validator.validateFixes().catch(error => {
    console.error("❌ Validation failed:", error);
    process.exit(1);
  });
}

export { FixValidator };
#!/usr/bin/env npx tsx

/**
 * Service Optimization Validation Script
 * 
 * This script validates that the new service-optimized routers work correctly
 * and provide the same functionality as the original routers.
 */

import { PrismaClient } from '../../prisma/generated';
import { getServiceRegistry, initializeServiceRegistry } from '../../src/services';
import { createLogger } from '../../src/services/logger.service';
import { getServiceIntegration } from '../../src/lib/service-integration';

// Configurable test user ID for testing (can be overridden via env variable)
const TEST_USER_ID = process.env.TEST_USER_ID || 'ctest123456789012345678901';

/**
 * Main validation function
 */
async function validateServiceOptimization() {
  const logger = createLogger('ValidationScript');
  const prisma = new PrismaClient();
  
  try {
    logger.info('Starting service optimization validation...');
    
    // Initialize service registry
    const registry = await initializeServiceRegistry(prisma);
    logger.info('Service registry initialized successfully');
    
    // Validate service registry
    await validateServiceRegistry(registry, logger);
    
    // Validate individual services
    await validateAccountService(registry, logger);
    await validateUserService(registry, logger);
    await validateCryptoService(registry, logger);
    await validateSubscriptionService(registry, logger);
    
    // Validate service integration
    await validateServiceIntegration(logger);
    
    // Validate performance monitoring
    await validatePerformanceMonitoring(registry, logger);
    
    logger.info('✅ All service optimization validations passed!');
    
  } catch (error) {
    logger.error('❌ Service optimization validation failed:', error instanceof Error ? error : new Error(String(error)));
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * Validate service registry functionality
 */
async function validateServiceRegistry(registry: any, logger: any) {
  logger.info('Validating service registry...');
  
  // Test service creation
  const accountService = registry.getAccountService();
  const userService = registry.getUserService();
  const cryptoService = registry.getCryptoService();
  const subscriptionService = registry.getSubscriptionService();
  
  if (!accountService || !userService || !cryptoService || !subscriptionService) {
    throw new Error('Failed to create all required services');
  }
  
  // Test service health
  const health = await registry.getServiceHealth();
  if (!health.healthy) {
    logger.warn('Some services are not healthy:', health.services);
  }
  
  // Test service metrics
  const metrics = registry.getServiceMetrics();
  logger.info('Service metrics:', metrics);
  
  logger.info('✅ Service registry validation passed');
}

/**
 * Validate account service functionality
 */
async function validateAccountService(registry: any, logger: any) {
  logger.info('Validating account service...');
  
  const accountService = registry.getAccountService();
  
  // Test getting monitored accounts (should work even with no accounts)
  const accountsResult = await accountService.getMonitoredAccounts(TEST_USER_ID);
  if (!accountsResult.success) {
    throw new Error(`Failed to get monitored accounts: ${accountsResult.error}`);
  }
  
  // Test account ownership validation
  const ownershipResult = await accountService.validateAccountOwnership(TEST_USER_ID, 'ctest123456789012345678901');
  if (typeof ownershipResult !== 'boolean') {
    throw new Error('Account ownership validation should return boolean');
  }
  
  logger.info('✅ Account service validation passed');
}

/**
 * Validate user service functionality
 */
async function validateUserService(registry: any, logger: any) {
  logger.info('Validating user service...');
  
  const userService = registry.getUserService();
  
  // Test getting available models
  const modelsResult = await userService.getAvailableModels();
  if (!modelsResult.success) {
    throw new Error(`Failed to get available models: ${modelsResult.error}`);
  }
  
  // Test getting available personalities
  const personalitiesResult = await userService.getAvailablePersonalities();
  if (!personalitiesResult.success) {
    throw new Error(`Failed to get available personalities: ${personalitiesResult.error}`);
  }
  
  logger.info('✅ User service validation passed');
}

/**
 * Validate crypto service functionality
 */
async function validateCryptoService(registry: any, logger: any) {
  logger.info('Validating crypto service...');
  
  const cryptoService = registry.getCryptoService();
  
  // Test cache stats (should work even if cache is empty)
  const cacheStatsResult = await cryptoService.getCacheStats();
  if (!cacheStatsResult.success) {
    throw new Error(`Failed to get cache stats: ${cacheStatsResult.error}`);
  }
  
  // Test cache cleanup
  const cleanupResult = await cryptoService.cleanupCache();
  if (!cleanupResult.success) {
    throw new Error(`Failed to cleanup cache: ${cleanupResult.error}`);
  }
  
  logger.info('✅ Crypto service validation passed');
}

/**
 * Validate subscription service functionality
 */
async function validateSubscriptionService(registry: any, logger: any) {
  logger.info('Validating subscription service...');
  
  const subscriptionService = registry.getSubscriptionService();
  
  // Test getting usage stats
  const usageStatsResult = await subscriptionService.getUsageStats(TEST_USER_ID);
  if (!usageStatsResult.success) {
    throw new Error(`Failed to get usage stats: ${usageStatsResult.error}`);
  }
  
  // Test getting rate limits
  const rateLimitsResult = await subscriptionService.getRateLimits(TEST_USER_ID);
  if (!rateLimitsResult.success) {
    throw new Error(`Failed to get rate limits: ${rateLimitsResult.error}`);
  }
  
  logger.info('✅ Subscription service validation passed');
}

/**
 * Validate service integration functionality
 */
async function validateServiceIntegration(logger: any) {
  logger.info('Validating service integration...');
  
  const integration = getServiceIntegration();
  
  // Test performance stats
  const performanceStats = integration.getPerformanceStats();
  if (typeof performanceStats.totalOperations !== 'number') {
    throw new Error('Performance stats should return valid data');
  }
  
  // Test cache stats
  const cacheStats = integration.getCacheStats();
  if (typeof cacheStats.totalEntries !== 'number') {
    throw new Error('Cache stats should return valid data');
  }
  
  logger.info('✅ Service integration validation passed');
}

/**
 * Validate performance monitoring functionality
 */
async function validatePerformanceMonitoring(registry: any, logger: any) {
  logger.info('Validating performance monitoring...');
  
  // Test that services are properly monitored
  const accountService = registry.getAccountService();
  
  // Execute a monitored operation
  const startTime = Date.now();
  await accountService.getMonitoredAccounts(TEST_USER_ID);
  const endTime = Date.now();
  
  const operationTime = endTime - startTime;
  if (operationTime < 0) {
    throw new Error('Performance monitoring should track operation time');
  }
  
  logger.info('✅ Performance monitoring validation passed');
}

/**
 * Validate API compatibility
 */
async function validateApiCompatibility(logger: any) {
  logger.info('Validating API compatibility...');
  
  // This would require setting up actual tRPC routers and testing them
  // For now, we'll just validate that the router files exist and can be imported
  
  try {
    // Try importing the optimized routers
    await import('../../src/routers/accounts-optimized');
    await import('../../src/routers/user-optimized');
    await import('../../src/routers/crypto-optimized');
    
    logger.info('✅ All optimized routers can be imported successfully');
  } catch (error) {
    throw new Error(`Failed to import optimized routers: ${error instanceof Error ? error.message : String(error)}`);
  }
  
  logger.info('✅ API compatibility validation passed');
}

/**
 * Performance benchmark test
 */
async function runPerformanceBenchmark(registry: any, logger: any) {
  logger.info('Running performance benchmark...');
  
  const accountService = registry.getAccountService();
  const iterations = 10;
  const times: number[] = [];
  
  for (let i = 0; i < iterations; i++) {
    const startTime = Date.now();
    await accountService.getMonitoredAccounts(TEST_USER_ID);
    const endTime = Date.now();
    times.push(endTime - startTime);
  }
  
  const averageTime = times.reduce((sum, time) => sum + time, 0) / times.length;
  const minTime = Math.min(...times);
  const maxTime = Math.max(...times);
  
  logger.info(`Performance benchmark results:`);
  logger.info(`  Average time: ${averageTime.toFixed(2)}ms`);
  logger.info(`  Min time: ${minTime}ms`);
  logger.info(`  Max time: ${maxTime}ms`);
  logger.info(`  Iterations: ${iterations}`);
  
  // Check if performance is reasonable (should be under 100ms for simple operations)
  if (averageTime > 100) {
    logger.warn('⚠️ Performance benchmark shows slow operations (>100ms average)');
  } else {
    logger.info('✅ Performance benchmark passed');
  }
}

/**
 * Memory usage test
 */
async function validateMemoryUsage(registry: any, logger: any) {
  logger.info('Validating memory usage...');
  
  const initialMemory = process.memoryUsage().heapUsed;
  
  // Perform multiple operations to test for memory leaks
  const accountService = registry.getAccountService();
  const userService = registry.getUserService();
  
  for (let i = 0; i < 100; i++) {
    await accountService.getMonitoredAccounts(TEST_USER_ID);
    await userService.getAvailableModels();
  }
  
  // Force garbage collection if available
  if (global.gc) {
    global.gc();
  }
  
  const finalMemory = process.memoryUsage().heapUsed;
  const memoryIncrease = finalMemory - initialMemory;
  const memoryIncreaseMB = memoryIncrease / (1024 * 1024);
  
  logger.info(`Memory usage:`);
  logger.info(`  Initial: ${(initialMemory / (1024 * 1024)).toFixed(2)}MB`);
  logger.info(`  Final: ${(finalMemory / (1024 * 1024)).toFixed(2)}MB`);
  logger.info(`  Increase: ${memoryIncreaseMB.toFixed(2)}MB`);
  
  // Check for significant memory leaks (increase > 10MB)
  if (memoryIncreaseMB > 10) {
    logger.warn('⚠️ Potential memory leak detected (>10MB increase)');
  } else {
    logger.info('✅ Memory usage validation passed');
  }
}

/**
 * Run all validations
 */
async function runAllValidations() {
  const logger = createLogger('ValidationScript');
  
  try {
    await validateServiceOptimization();
    
    // Additional validations
    const prisma = new PrismaClient();
    const registry = await initializeServiceRegistry(prisma);
    
    await validateApiCompatibility(logger);
    await runPerformanceBenchmark(registry, logger);
    await validateMemoryUsage(registry, logger);
    
    await prisma.$disconnect();
    
    logger.info('🎉 All validations completed successfully!');
    
  } catch (error) {
    logger.error('❌ Validation failed:', error instanceof Error ? error : new Error(String(error)));
    process.exit(1);
  }
}

// ESM execution guard - check if this script is executed directly
// This works for both CommonJS (require.main === module) and ESM (import.meta.url === `file://${process.argv[1]}`)
const isMainModule = (
  (typeof require !== 'undefined' && require.main === module) ||
  (typeof import.meta !== 'undefined' && import.meta.url === `file://${process.argv[1]}`)
);

if (isMainModule) {
  runAllValidations();
}

export {
  validateServiceOptimization,
  validateServiceRegistry,
  validateAccountService,
  validateUserService,
  validateCryptoService,
  validateSubscriptionService,
  validateServiceIntegration,
  validatePerformanceMonitoring,
  validateApiCompatibility,
  runPerformanceBenchmark,
  validateMemoryUsage,
  runAllValidations,
};

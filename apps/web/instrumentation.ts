import * as Sentry from "@sentry/nextjs";

/**
 * Next.js Instrumentation Hook
 * This file is automatically loaded by Next.js and should be placed at the project root
 * It handles both server-side and edge runtime instrumentation
 */
export async function register() {
  // Server-side instrumentation (Node.js runtime)
  if (process.env.NEXT_RUNTIME === "nodejs") {
    await import("./config/sentry.server.config");
  }

  // Edge runtime instrumentation
  if (process.env.NEXT_RUNTIME === "edge") {
    await import("./config/sentry.edge.config");
  }

  // Browser/client-side instrumentation is handled separately in _app.tsx or layout.tsx
  // The client-side code is now in src/instrumentation-client.ts for reference
}

/**
 * Request Error Handler
 * Called automatically by Next.js when an unhandled error occurs during request processing
 */
export async function onRequestError(
  err: unknown,
  request: {
    path: string;
    method: string;
    headers: { [key: string]: string | string[] | undefined };
  },
  context: {
    routerKind: string;
    routePath: string;
    routeType: string;
  }
) {
  Sentry.captureRequestError(err, request, context);
}

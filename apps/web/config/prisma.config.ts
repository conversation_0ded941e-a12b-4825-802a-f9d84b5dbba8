import "dotenv/config";
import path from "node:path";
import { defineConfig } from "prisma/config";
import { fileURLToPath } from "node:url";

// Get absolute path to the project root (apps/web directory)
// This ensures paths work regardless of the current working directory
const getCurrentDir = () => {
  if (typeof __dirname !== 'undefined') {
    // CommonJS
    return path.dirname(__dirname); // Go up from config/ to apps/web/
  } else {
    // ESM
    const currentFile = fileURLToPath(import.meta.url);
    return path.dirname(path.dirname(currentFile)); // Go up from config/ to apps/web/
  }
};

const projectRoot = getCurrentDir();
const schemaPath = path.join(projectRoot, "prisma", "schema");

// Robust console logging for debugging
console.log("🔧 Loading Prisma configuration...");
console.log(`📁 Project root: ${projectRoot}`);
console.log(`📁 Schema path: ${schemaPath}`);
console.log(`🌍 Environment: ${process.env.NODE_ENV || "development"}`);
console.log(`🗄️ Database URL configured: ${!!process.env.DATABASE_URL}`);

export default defineConfig({
  earlyAccess: true,
  schema: schemaPath, // Use absolute path instead of relative
});

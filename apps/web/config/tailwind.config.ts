import type { Config } from "tailwindcss";
import path from "node:path";
import { fileURLToPath } from "node:url";

// Get absolute path to the project root (apps/web directory)
// This ensures paths work regardless of the current working directory
const getCurrentDir = () => {
  if (typeof __dirname !== 'undefined') {
    // CommonJS
    return path.dirname(__dirname); // Go up from config/ to apps/web/
  } else {
    // ESM
    const currentFile = fileURLToPath(import.meta.url);
    return path.dirname(path.dirname(currentFile)); // Go up from config/ to apps/web/
  }
};

const projectRoot = getCurrentDir();

// Convert relative paths to absolute paths from project root
const config = {
  content: [
    path.join(projectRoot, "pages/**/*.{ts,tsx}"),
    path.join(projectRoot, "components/**/*.{ts,tsx}"),
    path.join(projectRoot, "app/**/*.{ts,tsx}"),
    path.join(projectRoot, "src/**/*.{ts,tsx}"),
    path.join(projectRoot, "*.{js,ts,jsx,tsx,mdx}"),
  ],
  theme: {
    extend: {
      colors: {
        // Custom app color palette
        app: {
          background: "var(--app-background)",
          headline: "var(--app-headline)",
          "sub-headline": "var(--app-sub-headline)",
          card: "var(--app-card)",
          "card-heading": "var(--app-card-heading)",
          "card-paragraph": "var(--app-card-paragraph)",
          stroke: "var(--app-stroke)",
          main: "var(--app-main)",
          highlight: "var(--app-highlight)",
          secondary: "var(--app-secondary)",
          tertiary: "var(--app-tertiary)",
          bg: "var(--app-background)",
        },
        // shadcn/ui colors
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
    },
  },
  plugins: [],
} satisfies Config;

export default config;

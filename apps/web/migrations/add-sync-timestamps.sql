-- Migration: Add sync timestamp fields to users table
-- Date: 2025-01-18
-- Description: Adds lastSyncedAt and lastManualSyncAt fields for plan-based sync tracking

BEGIN;

-- Add new timestamp columns to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS "lastSyncedAt" TIMESTAMP(3),
ADD COLUMN IF NOT EXISTS "lastManualSyncAt" TIMESTAMP(3);

-- Add comments for documentation
COMMENT ON COLUMN users."lastSyncedAt" IS 'Last automatic sync timestamp (paid plans)';
COMMENT ON COLUMN users."lastManualSyncAt" IS 'Last manual sync timestamp (free users)';

-- Create indexes for performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS "users_lastSyncedAt_idx" ON users ("lastSyncedAt");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "users_lastManualSyncAt_idx" ON users ("lastManualSyncAt");

-- Verify the changes
SELECT 
  column_name, 
  data_type, 
  is_nullable
FROM information_schema.columns 
WHERE table_name = 'users' 
  AND column_name IN ('lastSyncedAt', 'lastManualSyncAt');

COMMIT;
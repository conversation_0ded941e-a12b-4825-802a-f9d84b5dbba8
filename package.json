{"name": "BuddyChipUtimate", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo -F web dev", "build": "turbo -F web build", "check-types": "turbo -F web check-types", "lint": "biome check . --config-path=config/biome.json", "lint:fix": "biome check --write . --config-path=config/biome.json", "format": "biome format --write . --config-path=config/biome.json", "check": "biome ci . --config-path=config/biome.json", "dev:web": "turbo -F web dev", "db:push": "turbo -F web db:push", "db:studio": "turbo -F web db:studio", "db:generate": "turbo -F web db:generate", "db:migrate": "turbo -F web db:migrate"}, "devDependencies": {"@biomejs/biome": "^2.2.0", "turbo": "^2.5.6"}, "packageManager": "bun@1.2.19", "dependencies": {"import-in-the-middle": "^1.14.2", "require-in-the-middle": "^7.5.2"}, "overrides": {"tough-cookie": "^4.1.3", "next": "^15.3.3", "@clerk/nextjs": "^6.23.3", "@clerk/backend": "^2.4.0", "form-data": "^4.0.4"}}
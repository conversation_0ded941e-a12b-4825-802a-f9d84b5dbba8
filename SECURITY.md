# BuddyChip Ultimate - Security Documentation

## 🔒 Security Architecture Overview

This document outlines the comprehensive security measures implemented in BuddyChip Ultimate to ensure production readiness and protect against common web vulnerabilities.

## 🛡️ Security Features Implemented

### 1. Authentication & Authorization
- **Clerk Integration**: Secure user authentication and session management
- **JWT Token Validation**: Secure API endpoint protection
- **Role-based Access Control**: Admin and user permission levels

### 2. Input Validation & Sanitization
- **Zod Schema Validation**: Type-safe request validation
- **XSS Protection**: Input sanitization and output encoding
- **SQL Injection Prevention**: Parameterized queries with Prisma
- **CSRF Protection**: Token-based CSRF validation

### 3. Rate Limiting
- **Persistent Rate Limiting**: Vercel KV-backed rate limiting
- **Multiple Rate Limit Tiers**: Different limits for different endpoints
- **Sliding Window Algorithm**: Advanced rate limiting for better UX
- **User-specific Rate Limits**: Per-user and per-IP rate limiting

### 4. Security Monitoring & Audit Logging
- **Comprehensive Audit Trail**: All security events logged and monitored
- **Sentry Integration**: Real-time error tracking and security alerts
- **Threat Detection**: Pattern-based intrusion detection
- **Security Dashboard**: Real-time security metrics and alerts

### 5. Secure Communication
- **HTTPS Enforcement**: All communications encrypted in transit
- **Secure Headers**: CSP, HSTS, and other security headers
- **CORS Configuration**: Proper cross-origin resource sharing setup

## 📁 Security File Structure

```
apps/web/src/lib/
├── security-utils.ts          # Core security utilities
├── security-monitor.ts        # Audit logging & threat detection
├── rate-limiter.ts           # Persistent rate limiting
├── validation-middleware.ts   # Input validation middleware
├── csrf-middleware.ts        # CSRF protection
├── error-handler.ts          # Secure error handling
└── telegram-security.ts      # Telegram bot security

apps/web/src/app/api/security/
└── dashboard/                # Security monitoring dashboard
    └── route.ts
```

## 🚀 Quick Start Guide

### Environment Variables

Add these security-related environment variables:

```env
# Required - Telegram Security
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_WEBHOOK_SECRET=your_32_char_webhook_secret

# Required - Security Keys  
ADMIN_SECRET_KEY=your_admin_secret_key
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Optional - Rate Limiting
VERCEL_KV_URL=your_vercel_kv_url
VERCEL_KV_REST_API_URL=your_kv_rest_api_url
VERCEL_KV_REST_API_TOKEN=your_kv_rest_api_token

# Optional - Monitoring
SENTRY_DSN=your_sentry_dsn
```

### Basic Usage Examples

#### 1. Protecting API Routes with Rate Limiting

```typescript
import { rateLimiters } from "@/lib/rate-limiter";

export const POST = rateLimiters.api(async (req: Request) => {
  // Your API logic here
  return Response.json({ success: true });
});
```

#### 2. Adding Input Validation

```typescript
import { validateRequest, commonSchemas } from "@/lib/validation-middleware";
import { z } from "zod";

const schema = z.object({
  username: commonSchemas.userName,
  email: commonSchemas.email,
});

export const POST = validateRequest({
  body: schema,
})(async (req: Request) => {
  const { username, email } = (req as any).validated.body;
  // Validated and sanitized data available here
});
```

#### 3. CSRF Protection

```typescript
import { withCSRFProtection } from "@/lib/csrf-middleware";

export const POST = withCSRFProtection(async (req: Request) => {
  // CSRF token automatically validated
  return Response.json({ success: true });
});
```

#### 4. Security Event Logging

```typescript
import { securityLogger } from "@/lib/security-monitor";

// Log authentication failure
await securityLogger.loginFailure(userId, ipAddress, "Invalid password");

// Log suspicious activity
await securityLogger.suspiciousActivity(userId, ipAddress, "Multiple failed requests");

// Log unauthorized access attempt
await securityLogger.unauthorizedAccess(userId, ipAddress, endpoint, "Missing permissions");
```

## 🔧 Security Configuration

### Rate Limit Configurations

Different endpoints have different rate limits:

```typescript
export const RATE_LIMIT_CONFIGS = {
  API_GENERAL: { window: 60, limit: 60 },     // 60 req/min
  API_AUTH: { window: 60, limit: 20 },        // 20 req/min for auth
  AI_GENERATION: { window: 60, limit: 10 },   // 10 AI calls/min
  TELEGRAM_MESSAGE: { window: 60, limit: 30 }, // 30 messages/min
  SYNC_ENDPOINT: { window: 300, limit: 5 },   // 5 sync calls/5min
  PASSWORD_RESET: { window: 3600, limit: 3 }, // 3 attempts/hour
};
```

### Threat Detection Patterns

Automatic threat detection for:

- **Brute Force Attacks**: 5 failed auth attempts in 5 minutes
- **Injection Attempts**: 3 invalid inputs in 1 minute  
- **Unauthorized Access**: 5 unauthorized attempts in 1 minute
- **Rate Limit Abuse**: 10 rate limit hits in 5 minutes
- **Unusual Activity**: 50+ security events in 1 hour

## 📊 Security Dashboard

Access the security dashboard at `/api/security/dashboard` with admin credentials:

### Available Endpoints

```bash
# Get security overview
GET /api/security/dashboard?type=overview&timeframe=24h
Headers: X-Admin-Key: your_admin_secret

# Get system health
GET /api/security/dashboard?type=health

# Test security alerts (development only)
POST /api/security/dashboard/test-alert
Body: { "type": "AUTH_FAILURE", "severity": "high" }
```

### Dashboard Data

The dashboard provides:
- Real-time security event counts
- Top security threats
- Alert summaries by severity
- System health checks
- Recent security events

## 🚨 Security Event Types

### Authentication Events
- `AUTH_FAILURE`: Failed login attempts
- Login success events
- Session hijacking attempts

### Input Validation Events  
- `INVALID_INPUT`: Malformed or dangerous input
- XSS attempt detection
- SQL injection attempt detection

### Access Control Events
- `UNAUTHORIZED_ACCESS`: Accessing protected resources
- Permission escalation attempts
- Admin function abuse

### Rate Limiting Events
- `RATE_LIMIT_EXCEEDED`: API abuse detection
- Automated bot detection
- DDoS attempt mitigation

## 🛠️ Security Maintenance

### Regular Security Tasks

1. **Weekly**: Review security dashboard for unusual patterns
2. **Monthly**: Update dependencies and security patches
3. **Quarterly**: Security audit and penetration testing
4. **Annually**: Comprehensive security review

### Monitoring & Alerts

All security events are automatically sent to:
- **Console**: Development environment
- **Sentry**: Production error tracking
- **Vercel KV**: Persistent audit logs
- **Security Dashboard**: Real-time monitoring

### Log Retention

- Security events: 30 days
- Security alerts: 7 days  
- Audit logs: Configurable (default 30 days)

## 🔍 Security Testing

### Manual Testing

Test security features:

```bash
# Test rate limiting
for i in {1..100}; do curl -X POST http://localhost:3000/api/test; done

# Test input validation
curl -X POST http://localhost:3000/api/test \
  -H "Content-Type: application/json" \
  -d '{"malicious": "<script>alert(1)</script>"}'

# Test CSRF protection
curl -X POST http://localhost:3000/api/test \
  -H "Content-Type: application/json" \
  -d '{}' # Should fail without CSRF token
```

### Automated Testing

Run security tests:

```bash
# TypeScript compilation
bun run type-check

# Security linting
bun run lint

# Run test suite
bun run test

# Security audit
bun audit
```

## 🚨 Incident Response

### Security Incident Workflow

1. **Detection**: Automatic alerts via Sentry or manual discovery
2. **Assessment**: Determine severity and impact scope
3. **Containment**: Block attackers, revoke compromised tokens
4. **Investigation**: Analyze logs and determine root cause
5. **Recovery**: Fix vulnerabilities and restore services
6. **Documentation**: Update security measures and documentation

### Emergency Contacts

- **Development Team**: Technical response
- **DevOps Team**: Infrastructure response  
- **Security Team**: Incident coordination

## 📋 Security Checklist

### Pre-Production Checklist

- [ ] All environment variables set correctly
- [ ] Rate limiting configured and tested
- [ ] CSRF protection enabled on all forms
- [ ] Input validation on all endpoints
- [ ] Security headers configured
- [ ] Sentry error tracking active
- [ ] Security dashboard accessible
- [ ] Threat detection patterns active
- [ ] SSL/TLS certificates valid
- [ ] Security audit completed

### Post-Deployment Checklist

- [ ] Security dashboard showing normal activity
- [ ] No critical security alerts in Sentry
- [ ] Rate limiting working correctly
- [ ] All security tests passing
- [ ] Log retention policies active
- [ ] Security monitoring active
- [ ] Backup systems functional

## 🔗 Additional Resources

- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [Next.js Security Best Practices](https://nextjs.org/docs/advanced-features/security-headers)
- [Vercel Security Documentation](https://vercel.com/docs/security)
- [Sentry Security Monitoring](https://docs.sentry.io/security-legal-pii/)

## 📞 Support

For security-related questions or to report vulnerabilities:
- **Email**: <EMAIL>
- **Emergency**: Create GitHub issue with "security" label
- **Documentation**: This file and inline code comments

---

**Last Updated**: `date` | **Version**: 1.0.0 | **Reviewed By**: Development Team